/* Modern Forum Styles */
.Forum {
    /* Remove old styles and use modern Tailwind classes in JSX */

    /* Custom animations and effects */
    .forum-question-container {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .forum-question-container:hover {
        transform: translateY(-2px);
    }

    /* Modern button styles */
    .modern-btn {
        @apply inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200;
    }

    .modern-btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl;
    }

    .modern-btn-secondary {
        @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
    }

    .modern-btn-danger {
        @apply bg-red-50 text-red-600 hover:bg-red-100;
    }

    /* Profile image enhancements */
    .profile-image {
        @apply relative overflow-hidden;
    }

    .profile-image::after {
        content: '';
        @apply absolute inset-0 rounded-full ring-2 ring-blue-100 ring-opacity-50;
    }

    /* Card hover effects */
    .question-card {
        @apply transform transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;
    }

    /* Typography improvements */
    .question-title {
        @apply text-xl font-bold text-gray-900 leading-tight;
    }

    .question-body {
        @apply text-gray-700 leading-relaxed;
    }

    .user-name {
        @apply font-semibold text-gray-900;
    }

    .timestamp {
        @apply text-sm text-gray-500;
    }

    /* Modern Replies Section */
    .replies {
        @apply bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mt-4 border border-gray-100;
    }

    .reply {
        @apply mb-4 p-4 bg-white rounded-lg shadow-sm border border-gray-100 transition-all duration-200 hover:shadow-md;
    }

    .reply-text {
        @apply text-gray-700 leading-relaxed;
    }

    /* Admin Reply Styles */
    .admin-reply {
        @apply bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 relative;
    }

    .admin-reply::before {
        content: '👑 Admin';
        @apply absolute -top-2 left-4 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold;
    }

    .admin-reply .reply-text {
        @apply font-semibold text-green-800;
    }

    /* Verified Reply Styles */
    .verified-reply {
        @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 relative;
    }

    .verified-reply::before {
        content: '✓ Verified';
        @apply absolute -top-2 left-4 bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-semibold;
    }

    .verified-reply .reply-text {
        @apply font-semibold text-blue-800;
    }

    /* Verification Button */
    .verification-btn {
        @apply px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200 font-medium;
    }

    /* Date styling */
    .date {
        @apply text-xs text-gray-500 font-medium;
    }

    /* Form styling */
    .modern-form {
        @apply bg-white rounded-xl p-6 shadow-lg border border-gray-100 mt-6;
    }

    .modern-form .ant-form-item-label > label {
        @apply font-semibold text-gray-700;
    }

    .modern-form .ant-input,
    .modern-form .ant-input-affix-wrapper {
        @apply border-gray-300 rounded-lg hover:border-blue-400 focus:border-blue-500;
    }

    .modern-form .ant-btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold;
    }

}

@media only screen and (max-width: 768px) {
    .Forum {
        .replies {
            padding: 3px;
        }

        .verified-reply {
            flex-direction: column-reverse;
        }
    }
}