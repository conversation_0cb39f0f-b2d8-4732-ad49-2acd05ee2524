const mongoose = require('mongoose');
const User = require('./models/userModel');
require('dotenv').config();

// Test subscription scenarios for admin filtering
async function createTestSubscriptionScenarios() {
  try {
    console.log('🧪 Creating test subscription scenarios...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    const now = new Date();
    
    // Test scenarios to create
    const testScenarios = [
      {
        name: 'John Active Premium',
        email: '<EMAIL>',
        scenario: 'ON-PLAN',
        data: {
          paymentRequired: true,
          subscriptionStatus: 'active',
          subscriptionPlan: 'premium',
          subscriptionStartDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          subscriptionEndDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          lastActivity: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          totalQuizzesTaken: 15
        }
      },
      {
        name: '<PERSON> Expired Basic',
        email: '<EMAIL>',
        scenario: 'EXPIRED-PLAN',
        data: {
          paymentRequired: true,
          subscriptionStatus: 'expired',
          subscriptionPlan: 'basic',
          subscriptionStartDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
          subscriptionEndDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago (expired)
          lastActivity: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
          totalQuizzesTaken: 8
        }
      },
      {
        name: 'Mike Free User',
        email: '<EMAIL>',
        scenario: 'NO-PLAN',
        data: {
          paymentRequired: false,
          subscriptionStatus: 'free',
          lastActivity: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          totalQuizzesTaken: 3
        }
      },
      {
        name: 'Lisa Resubscribed',
        email: '<EMAIL>',
        scenario: 'ON-PLAN (Resubscribed)',
        data: {
          paymentRequired: true,
          subscriptionStatus: 'active',
          subscriptionPlan: 'pro',
          // First subscription was 6 months ago, expired 2 months ago
          // New subscription started 1 month ago
          subscriptionStartDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago (new subscription)
          subscriptionEndDate: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
          lastActivity: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          totalQuizzesTaken: 25
        }
      },
      {
        name: 'Tom Just Expired',
        email: '<EMAIL>',
        scenario: 'EXPIRED-PLAN (Just Expired)',
        data: {
          paymentRequired: true,
          subscriptionStatus: 'expired',
          subscriptionPlan: 'premium',
          subscriptionStartDate: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
          subscriptionEndDate: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago (just expired)
          lastActivity: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          totalQuizzesTaken: 12
        }
      }
    ];
    
    // Create or update test users
    for (const scenario of testScenarios) {
      try {
        // Check if user already exists
        let user = await User.findOne({ email: scenario.email });
        
        if (user) {
          // Update existing user
          await User.findByIdAndUpdate(user._id, {
            ...scenario.data,
            name: scenario.name,
            // Preserve existing fields
            school: user.school || 'Test School',
            class: user.class || 'Test Class',
            level: user.level || 'primary'
          });
          console.log(`✅ Updated: ${scenario.name} (${scenario.scenario})`);
        } else {
          // Create new user
          const newUser = new User({
            name: scenario.name,
            email: scenario.email,
            school: 'Test School',
            class: 'Test Class',
            level: 'primary',
            password: '$2b$10$hashedpassword', // Dummy hashed password
            ...scenario.data
          });
          
          await newUser.save();
          console.log(`✅ Created: ${scenario.name} (${scenario.scenario})`);
        }
        
        // Show the subscription logic result
        const updatedUser = await User.findOne({ email: scenario.email });
        const subscriptionStatus = getSubscriptionFilterStatus(updatedUser);
        console.log(`   Filter Status: ${subscriptionStatus}\n`);
        
      } catch (error) {
        console.error(`❌ Error with ${scenario.name}:`, error.message);
      }
    }
    
    console.log('🎯 Test scenarios created successfully!');
    console.log('\n📋 Summary:');
    console.log('- ON-PLAN: Users with valid subscription dates');
    console.log('- EXPIRED-PLAN: Users whose subscription end date has passed');
    console.log('- NO-PLAN: Users with paymentRequired = false');
    console.log('\n🔍 Check the admin panel to see the filtering in action!');
    
  } catch (error) {
    console.error('❌ Error creating test scenarios:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Helper function to test subscription logic (same as client-side)
function getSubscriptionFilterStatus(user) {
  const now = new Date();
  const paymentRequired = user.paymentRequired;
  const subscriptionEndDate = user.subscriptionEndDate;
  const subscriptionStartDate = user.subscriptionStartDate;
  
  // NO-PLAN: Users who never required payment or never had subscription
  if (!paymentRequired) {
    return 'no-plan';
  }
  
  // Users with paymentRequired = true (have or had a subscription)
  if (paymentRequired) {
    // Check if subscription has expired by date
    if (subscriptionEndDate) {
      const endDate = new Date(subscriptionEndDate);
      
      if (endDate < now) {
        // Subscription end date has passed - EXPIRED PLAN
        return 'expired-plan';
      } else {
        // Subscription is still valid by date - ON PLAN
        return 'on-plan';
      }
    } else {
      // Has paymentRequired = true but no end date specified
      // This could be a lifetime subscription or missing data
      // Assume they are on plan if they have paymentRequired = true
      return 'on-plan';
    }
  }
  
  // Default fallback for edge cases
  return 'no-plan';
}

// Run the test
if (require.main === module) {
  createTestSubscriptionScenarios();
}

module.exports = { createTestSubscriptionScenarios, getSubscriptionFilterStatus };
