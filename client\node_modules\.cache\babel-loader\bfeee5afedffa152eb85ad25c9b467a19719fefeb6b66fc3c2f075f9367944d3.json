{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport AdminNavigation from \"./AdminNavigation\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      getUserData();\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: [(user === null || user === void 0 ? void 0 : user.isAdmin) && /*#__PURE__*/_jsxDEV(AdminNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex-1 flex flex-col min-h-screen ${user !== null && user !== void 0 && user.isAdmin ? 'lg:ml-72' : ''}`,\n      children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: `nav-modern ${location.pathname.includes('/quiz') || location.pathname.includes('/write-exam') ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98' : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'} backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"relative group flex items-center space-x-2 sm:space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-lg text-white text-sm sm:text-base md:text-lg\",\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black tracking-tight relative z-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent\",\n                    children: \"Brainwave\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.8\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                className: \"group\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-full overflow-hidden border-2 border-blue-200 group-hover:border-blue-400 transition-all duration-300 transform group-hover:scale-110 shadow-md group-hover:shadow-lg cursor-pointer relative bg-white\",\n                  children: [user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profileImage,\n                    alt: \"Profile\",\n                    className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      if (e.target.nextSibling) {\n                        e.target.nextSibling.style.display = 'flex';\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-xs sm:text-sm md:text-base relative overflow-hidden\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative z-10\",\n                      children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-30 blur transition-opacity duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `flex-1 overflow-auto ${user !== null && user !== void 0 && user.isAdmin ? 'bg-gray-100' : 'bg-gradient-to-br from-gray-50 to-blue-50'} ${user !== null && user !== void 0 && user.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"cwn50ZKVcb7yRGicmnB70bd2a+Y=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "_user$name", "_user$name$charAt", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "getUserData", "response", "success", "data", "error", "token", "localStorage", "getItem", "includes", "verifyPaymentStatus", "console", "log", "paymentStatus", "current", "clearInterval", "paymentRequired", "isAdmin", "setInterval", "getButtonClass", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "initial", "y", "opacity", "animate", "div", "scale", "transition", "duration", "delay", "profileImage", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "name", "char<PERSON>t", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* Admin Navigation for admin users */}\r\n      {user?.isAdmin && <AdminNavigation />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className={`flex-1 flex flex-col min-h-screen ${user?.isAdmin ? 'lg:ml-72' : ''}`}>\r\n        {/* Modern Responsive Header - Only show for non-admin users */}\r\n        {!user?.isAdmin && (\r\n          <motion.header\r\n            initial={{ y: -20, opacity: 0 }}\r\n            animate={{ y: 0, opacity: 1 }}\r\n            className={`nav-modern ${\r\n              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n          >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Empty left section for balance */}\r\n              <div className=\"w-16\"></div>\r\n\r\n              {/* Center Section - Brainwave Title with Logo */}\r\n              <div className=\"flex-1 flex justify-center\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.9 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"relative group flex items-center space-x-2 sm:space-x-3\"\r\n                >\r\n                  {/* Brain Logo */}\r\n                  <div className=\"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-lg text-white text-sm sm:text-base md:text-lg\">\r\n                    🧠\r\n                  </div>\r\n\r\n                  {/* Brainwave Text */}\r\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-black tracking-tight relative z-10\">\r\n                    <span className=\"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent\">\r\n                      Brainwave\r\n                    </span>\r\n                  </h1>\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile */}\r\n              <div className=\"flex items-center justify-end\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"group\"\r\n                >\r\n                  <div className=\"w-8 h-8 rounded-full overflow-hidden border-2 border-blue-200 group-hover:border-blue-400 transition-all duration-300 transform group-hover:scale-110 shadow-md group-hover:shadow-lg cursor-pointer relative bg-white\">\r\n                    {user?.profileImage ? (\r\n                      <img\r\n                        src={user.profileImage}\r\n                        alt=\"Profile\"\r\n                        className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\"\r\n                        onError={(e) => {\r\n                          e.target.style.display = 'none';\r\n                          if (e.target.nextSibling) {\r\n                            e.target.nextSibling.style.display = 'flex';\r\n                          }\r\n                        }}\r\n                      />\r\n                    ) : (\r\n                      <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-xs sm:text-sm md:text-base relative overflow-hidden\">\r\n                        <span className=\"relative z-10\">{user?.name?.charAt(0)?.toUpperCase() || 'U'}</span>\r\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                      </div>\r\n                    )}\r\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-30 blur transition-opacity duration-300\"></div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGhD,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM0B,WAAW,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAE0B;EAAiB,CAAC,GAAGtB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGxB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAMrC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlC,WAAW,CAAC,CAAC;MACpC,IAAIkC,QAAQ,CAACC,OAAO,EAAE;QACpBP,QAAQ,CAACzB,OAAO,CAAC+B,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACL1C,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;QAC/BmC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdR,QAAQ,CAAC,QAAQ,CAAC;MAClBnC,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM0C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTL,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLJ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINjC,SAAS,CAAC,MAAM;IACd,IAAIyB,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACoB,QAAQ,CAACV,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEU,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMa,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMN,IAAI,GAAG,MAAM5B,kBAAkB,CAAC,CAAC;MACvCmC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAER,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,KAAK,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIrB,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACnB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAa,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACnB,eAAe,CAAC2B,IAAI,CAAC,CAAC;QAC/B,IAAIb,WAAW,CAACuB,OAAO,EAAE;UACvBC,aAAa,CAACxB,WAAW,CAACuB,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,KAAK,CAAC;MACpDT,QAAQ,CAACnB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/Ba,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED1B,SAAS,CAAC,MAAM;IACd,IAAIuB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIlB,yBAAyB,EAAE;QAC7BiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CrB,WAAW,CAACuB,OAAO,GAAGI,WAAW,CAAC,MAAM;UACtCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCF,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTd,QAAQ,CAAClB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACgB,yBAAyB,CAAC,CAAC;EAE/B9B,SAAS,CAAC,MAAM;IACd,IAAIuB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEY,WAAW,CAAC,CAAC;EAGvB,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAACjC,IAAI,CAAC6B,eAAe,IAAII,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAA5B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqB,aAAa,MAAK,MAAM,IAAI1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACEnC,OAAA;IAAKwC,SAAS,EAAC,0CAA0C;IAAAtC,QAAA,GAEtD,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,OAAO,kBAAIpC,OAAA,CAACF,eAAe;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrC5C,OAAA;MAAKwC,SAAS,EAAG,qCAAoClC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,UAAU,GAAG,EAAG,EAAE;MAAAlC,QAAA,GAEpF,EAACI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,kBACbpC,OAAA,CAACd,MAAM,CAAC2D,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BR,SAAS,EAAG,cACVvB,QAAQ,CAACE,QAAQ,CAACS,QAAQ,CAAC,OAAO,CAAC,IAAIX,QAAQ,CAACE,QAAQ,CAACS,QAAQ,CAAC,aAAa,CAAC,GAC5E,8EAA8E,GAC9E,2DACL,8FAA8F;QAAA1B,QAAA,eAEjGF,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAtC,QAAA,eAC5DF,OAAA;YAAKwC,SAAS,EAAC,wEAAwE;YAAAtC,QAAA,gBAErFF,OAAA;cAAKwC,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG5B5C,OAAA;cAAKwC,SAAS,EAAC,4BAA4B;cAAAtC,QAAA,eACzCF,OAAA,CAACd,MAAM,CAACgE,GAAG;gBACTJ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBACpCF,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,yDAAyD;gBAAAtC,QAAA,gBAGnEF,OAAA;kBAAKwC,SAAS,EAAC,qLAAqL;kBAAAtC,QAAA,EAAC;gBAErM;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAGN5C,OAAA;kBAAIwC,SAAS,EAAC,0EAA0E;kBAAAtC,QAAA,eACtFF,OAAA;oBAAMwC,SAAS,EAAC,2FAA2F;oBAAAtC,QAAA,EAAC;kBAE5G;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL5C,OAAA;kBAAKwC,SAAS,EAAC;gBAA6K;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGN5C,OAAA;cAAKwC,SAAS,EAAC,+BAA+B;cAAAtC,QAAA,eAC5CF,OAAA,CAACd,MAAM,CAACgE,GAAG;gBACTJ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBACpCF,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,OAAO;gBAAAtC,QAAA,eAEjBF,OAAA;kBAAKwC,SAAS,EAAC,wNAAwN;kBAAAtC,QAAA,GACpOI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiD,YAAY,gBACjBvD,OAAA;oBACEwD,GAAG,EAAElD,IAAI,CAACiD,YAAa;oBACvBE,GAAG,EAAC,SAAS;oBACbjB,SAAS,EAAC,oFAAoF;oBAC9FkB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/B,IAAIH,CAAC,CAACC,MAAM,CAACG,WAAW,EAAE;wBACxBJ,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC7C;oBACF;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEF5C,OAAA;oBAAKwC,SAAS,EAAC,2LAA2L;oBAAAtC,QAAA,gBACxMF,OAAA;sBAAMwC,SAAS,EAAC,eAAe;sBAAAtC,QAAA,EAAE,CAAAI,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAE0D,IAAI,cAAA5D,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY6D,MAAM,CAAC,CAAC,CAAC,cAAA5D,iBAAA,uBAArBA,iBAAA,CAAuB6D,WAAW,CAAC,CAAC,KAAI;oBAAG;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpF5C,OAAA;sBAAKwC,SAAS,EAAC;oBAAmI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtJ,CACN,eACD5C,OAAA;oBAAKwC,SAAS,EAAC;kBAAkK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACd,eAGD5C,OAAA;QAAMwC,SAAS,EAAG,wBAChBlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GACT,aAAa,GACb,2CACL,IAAG9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,KAAK,GAAG,eAAgB,EAAE;QAAAlC,QAAA,eAC5CF,OAAA,CAACd,MAAM,CAACgE,GAAG;UACTJ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BK,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,QAAQ;UAAAtC,QAAA,EAEjBA;QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzC,EAAA,CAlNQF,cAAc;EAAA,QACJZ,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAA2E,EAAA,GARrBlE,cAAc;AAoNvB,eAAeA,cAAc;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}