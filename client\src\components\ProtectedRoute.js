import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";
import AdminNavigation from "./AdminNavigation";
import { TbHome } from "react-icons/tb";


function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;





  const getUserData = async () => {
    try {
      const response = await getUserInfo();
      if (response.success) {
        dispatch(SetUser(response.data));
      } else {
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      getUserData();
    } else {
      navigate("/login");
    }
  }, []);



  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };




  return (
    <div className="layout-modern min-h-screen flex flex-col">
      {/* Admin Navigation for admin users */}
      {user?.isAdmin && <AdminNavigation />}

      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col min-h-screen ${user?.isAdmin ? 'lg:ml-72' : ''}`}>
        {/* Modern Responsive Header - Show for all users */}
        {(
          <motion.header
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className={`nav-modern ${
              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')
                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'
                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'
            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}
          >
          <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
            <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
              {/* Left section - Logout button and Hub button */}
              <div className="flex items-center space-x-2">
                {/* BLUE LOGOUT BUTTON WITH CSS CLASS */}
                <button
                  onClick={() => {
                    localStorage.removeItem("token");
                    navigate("/login");
                  }}
                  className="force-blue-logout"
                  title="Click to Logout"
                >
                  <span style={{ fontSize: '16px' }}>🚪</span>
                  <span>Logout</span>
                </button>

                {/* Hub Button for quiz pages */}
                {(location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')) && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/user/hub')}
                    className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-300 text-xs"
                  >
                    <TbHome className="w-3 h-3" />
                    <span className="hidden sm:inline">Hub</span>
                  </motion.button>
                )}
              </div>

              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}
              <div className="flex-1 flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative group flex items-center space-x-3"
                >
                  {/* Tanzania Flag - Using actual flag image */}
                  <div
                    className="rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative"
                    style={{
                      width: '32px',
                      height: '24px'
                    }}
                  >
                    <img
                      src="https://flagcdn.com/w40/tz.png"
                      alt="Tanzania Flag"
                      className="w-full h-full object-cover"
                      style={{ objectFit: 'cover' }}
                      onError={(e) => {
                        // Fallback to another flag source if first fails
                        e.target.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png";
                        e.target.onerror = () => {
                          // Final fallback - hide image and show text
                          e.target.style.display = 'none';
                          e.target.parentElement.innerHTML = '<div class="w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold">TZ</div>';
                        };
                      }}
                    />
                  </div>

                  {/* Amazing Animated Brainwave Text */}
                  <div className="relative brainwave-container">
                    <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                        style={{
                          fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                          letterSpacing: '-0.02em'
                        }}>
                      {/* Brain - with amazing effects */}
                      <motion.span
                        className="relative inline-block"
                        initial={{ opacity: 0, x: -30, scale: 0.8 }}
                        animate={{
                          opacity: 1,
                          x: 0,
                          scale: 1,
                          textShadow: [
                            "0 0 10px rgba(59, 130, 246, 0.5)",
                            "0 0 20px rgba(59, 130, 246, 0.8)",
                            "0 0 10px rgba(59, 130, 246, 0.5)"
                          ]
                        }}
                        transition={{
                          duration: 1,
                          delay: 0.3,
                          textShadow: {
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }
                        }}
                        whileHover={{
                          scale: 1.1,
                          rotate: [0, -2, 2, 0],
                          transition: { duration: 0.3 }
                        }}
                        style={{
                          color: '#1f2937',
                          fontWeight: '900',
                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                        }}
                      >
                        Brain

                        {/* Electric spark */}
                        <motion.div
                          className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                          animate={{
                            opacity: [0, 1, 0],
                            scale: [0.5, 1.2, 0.5],
                            backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            delay: 2
                          }}
                          style={{
                            backgroundColor: '#3b82f6',
                            boxShadow: '0 0 10px #3b82f6'
                          }}
                        />
                      </motion.span>

                      {/* Wave - with flowing effects (no space) */}
                      <motion.span
                        className="relative inline-block"
                        initial={{ opacity: 0, x: 30, scale: 0.8 }}
                        animate={{
                          opacity: 1,
                          x: 0,
                          scale: 1,
                          y: [0, -2, 0, 2, 0],
                          textShadow: [
                            "0 0 10px rgba(16, 185, 129, 0.5)",
                            "0 0 20px rgba(16, 185, 129, 0.8)",
                            "0 0 10px rgba(16, 185, 129, 0.5)"
                          ]
                        }}
                        transition={{
                          duration: 1,
                          delay: 0.5,
                          y: {
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          },
                          textShadow: {
                            duration: 2.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }
                        }}
                        whileHover={{
                          scale: 1.1,
                          rotate: [0, 2, -2, 0],
                          transition: { duration: 0.3 }
                        }}
                        style={{
                          color: '#059669',
                          fontWeight: '900',
                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                        }}
                      >
                        wave

                        {/* Wave particle */}
                        <motion.div
                          className="absolute top-0 left-0 w-1.5 h-1.5 rounded-full"
                          animate={{
                            opacity: [0, 1, 0],
                            x: [0, 40, 80],
                            y: [0, -5, 0, 5, 0],
                            backgroundColor: ['#10b981', '#34d399', '#10b981']
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: 1
                          }}
                          style={{
                            backgroundColor: '#10b981',
                            boxShadow: '0 0 8px #10b981'
                          }}
                        />
                      </motion.span>
                    </h1>

                    {/* Glowing underline effect */}
                    <motion.div
                      className="absolute -bottom-1 left-0 h-1 rounded-full"
                      initial={{ width: 0, opacity: 0 }}
                      animate={{
                        width: '100%',
                        opacity: 1,
                        boxShadow: [
                          '0 0 10px rgba(16, 185, 129, 0.5)',
                          '0 0 20px rgba(59, 130, 246, 0.8)',
                          '0 0 10px rgba(16, 185, 129, 0.5)'
                        ]
                      }}
                      transition={{
                        duration: 1.5,
                        delay: 1.2,
                        boxShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      style={{
                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                      }}
                    />
                  </div>

                  {/* Official Logo - Small like profile */}
                  <div
                    className="rounded-full overflow-hidden border-2 border-white/20 relative"
                    style={{
                      background: '#f0f0f0',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                      width: '32px',
                      height: '32px'
                    }}
                  >
                    <img
                      src="/favicon.png"
                      alt="Brainwave Logo"
                      className="w-full h-full object-cover"
                      style={{ objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                    <div
                      className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold"
                      style={{
                        display: 'none',
                        fontSize: '12px'
                      }}
                    >
                      🧠
                    </div>
                  </div>

                  {/* Modern Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
                </motion.div>
              </div>

              {/* Right Section - User Profile (WhatsApp Style) */}
              <div className="flex items-center justify-end space-x-2 sm:space-x-3">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 group"
                >
                  {/* Small WhatsApp-style Profile Circle - Same as Ranking */}
                  <div
                    className="rounded-full overflow-hidden border-2 border-white/20 relative cursor-pointer"
                    style={{
                      background: '#f0f0f0',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                      width: '32px',
                      height: '32px'
                    }}
                  >
                    {user?.profileImage ? (
                      <img
                        src={user.profileImage}
                        alt={user.name}
                        className="object-cover rounded-full w-full h-full"
                        style={{ objectFit: 'cover' }}
                      />
                    ) : (
                      <div
                        className="rounded-full flex items-center justify-center font-semibold w-full h-full"
                        style={{
                          background: '#25D366',
                          color: '#FFFFFF',
                          fontSize: '12px'
                        }}
                      >
                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                    )}
                  </div>

                  {/* User Name and Class */}
                  <div className="hidden sm:block text-right">
                    <div className="text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300">
                      Class {user?.class}
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.header>
        )}

        {/* Page Content */}
        <main className={`flex-1 overflow-auto ${
          user?.isAdmin
            ? 'bg-gray-100'
            : 'bg-gradient-to-br from-gray-50 to-blue-50'
        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>


      </div>
    </div>
  );
}

export default ProtectedRoute;
