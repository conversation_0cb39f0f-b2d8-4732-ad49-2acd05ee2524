{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto, sendOTP } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userDetails$name, _userDetails$name$cha, _userRankingStats$use, _userRankingStats$use2, _userRankingStats$use3, _userRankingStats$use4, _userRankingStats$use5;\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [userRankingStats, setUserRankingStats] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n\n  // Fetch user ranking data from the ranking system\n  const fetchUserRankingData = async () => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails._id)) return;\n    try {\n      dispatch(ShowLoading());\n\n      // Get user's ranking position and nearby users\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\n      if (rankingResponse.success) {\n        setUserRankingStats(rankingResponse.data);\n      }\n\n      // Also get the full leaderboard to find user's position\n      const leaderboardResponse = await getXPLeaderboard({\n        limit: 1000,\n        levelFilter: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'all'\n      });\n      if (leaderboardResponse.success) {\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\n        if (userIndex >= 0) {\n          const userWithRank = {\n            ...leaderboardResponse.data[userIndex],\n            rank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length\n          };\n          setUserRankingStats(prev => ({\n            ...prev,\n            userRank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length,\n            user: userWithRank\n          }));\n        }\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Error fetching ranking data:', error);\n    }\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const sendOTPRequest = async email => {\n    dispatch(ShowLoading());\n    try {\n      const response = await sendOTP({\n        email\n      });\n      if (response.success) {\n        message.success(\"Please verify new email!\");\n        setEdit(false);\n        setServerGeneratedOTP(response.data);\n      } else {\n        message.error(response.message);\n        discardChanges();\n      }\n    } catch (error) {\n      message.error(error.message);\n      discardChanges();\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    if (!formData.name) return message.error(\"Please enter your name.\");\n    if (!formData.class_) return message.error(\"Please select a class.\");\n    if (!skipOTP && formData.email !== userDetails.email) {\n      return sendOTPRequest(formData.email);\n    }\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserInfo({\n        ...formData,\n        userId: userDetails._id\n      });\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        setServerGeneratedOTP(null);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = async e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        message.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        message.error('Image size should be less than 5MB');\n        return;\n      }\n      setProfileImage(file);\n\n      // Show preview\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n\n      // Auto-upload the image\n      const data = new FormData();\n      data.append(\"profileImage\", file);\n      dispatch(ShowLoading());\n      try {\n        const response = await updateUserPhoto(data);\n        dispatch(HideLoading());\n        if (response.success) {\n          message.success(\"Profile picture updated successfully!\");\n          getUserData(); // Refresh user data to show new image\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message || \"Failed to update profile picture\");\n      }\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const verifyUser = async values => {\n    if (values.otp === serverGeneratedOTP) {\n      handleUpdate({\n        skipOTP: true\n      });\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    getUserData();\n  }, []);\n\n  // Load ranking data when user details are available\n  useEffect(() => {\n    if (userDetails) {\n      fetchUserRankingData();\n    }\n  }, [userDetails]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-gray-900 mb-2\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account settings and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-4 border-blue-200 relative cursor-pointer hover:border-blue-400 transition-colors duration-200\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                    width: '80px',\n                    height: '80px'\n                  },\n                  onClick: () => document.getElementById('profileImageInput').click(),\n                  children: [userDetails !== null && userDetails !== void 0 && userDetails.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: userDetails.profileImage,\n                    alt: \"Profile\",\n                    className: \"object-cover rounded-full w-full h-full\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                    style: {\n                      background: '#25D366',\n                      color: '#FFFFFF',\n                      fontSize: '24px'\n                    },\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$name = userDetails.name) === null || _userDetails$name === void 0 ? void 0 : (_userDetails$name$cha = _userDetails$name.charAt(0)) === null || _userDetails$name$cha === void 0 ? void 0 : _userDetails$name$cha.toUpperCase()) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\",\n                  onClick: () => document.getElementById('profileImageInput').click(),\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 text-white\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 13a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"profileImageInput\",\n                  type: \"file\",\n                  accept: \"image/*\",\n                  className: \"hidden\",\n                  onChange: handleImageChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-600 font-medium\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-green-600 font-medium\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900 truncate max-w-[150px]\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || '<EMAIL>'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-purple-600 font-medium\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this), userRankingStats && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-yellow-600 font-medium\",\n                    children: \"Rank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [\"#\", userRankingStats.userRank || 'N/A', userRankingStats.totalUsers && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"/\", userRankingStats.totalUsers]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-orange-600 font-medium\",\n                    children: \"Total XP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use = userRankingStats.user) === null || _userRankingStats$use === void 0 ? void 0 : (_userRankingStats$use2 = _userRankingStats$use.totalXP) === null || _userRankingStats$use2 === void 0 ? void 0 : _userRankingStats$use2.toLocaleString()) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-indigo-600 font-medium\",\n                    children: \"Avg Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [((_userRankingStats$use3 = userRankingStats.user) === null || _userRankingStats$use3 === void 0 ? void 0 : _userRankingStats$use3.averageScore) || '0', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-pink-600 font-medium\",\n                    children: \"Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use4 = userRankingStats.user) === null || _userRankingStats$use4 === void 0 ? void 0 : _userRankingStats$use4.totalQuizzesTaken) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-teal-600 font-medium\",\n                    children: \"Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use5 = userRankingStats.user) === null || _userRankingStats$use5 === void 0 ? void 0 : _userRankingStats$use5.currentStreak) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 325,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"GY61rAw3X1yB6R9HmgbBBdM+X+0=\", false, function () {\n  return [useDispatch];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "sendOTP", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsForRanking", "getUserRanking", "getXPLeaderboard", "jsxDEV", "_jsxDEV", "Profile", "_s", "_userDetails$name", "_userDetails$name$cha", "_userRankingStats$use", "_userRankingStats$use2", "_userRankingStats$use3", "_userRankingStats$use4", "_userRankingStats$use5", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userRankingStats", "setUserRankingStats", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "serverGeneratedOTP", "setServerGeneratedOTP", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "fetchUserRankingData", "rankingResponse", "leaderboardResponse", "limit", "levelFilter", "userIndex", "findIndex", "userWithRank", "rank", "totalUsers", "length", "prev", "userRank", "console", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "discardChanges", "sendOTPRequest", "handleUpdate", "skipOTP", "levelChanged", "setTimeout", "window", "location", "reload", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "append", "handleImageUpload", "verifyUser", "values", "otp", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "boxShadow", "width", "height", "onClick", "document", "getElementById", "click", "src", "alt", "onError", "display", "nextS<PERSON>ling", "color", "fontSize", "char<PERSON>t", "toUpperCase", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "id", "accept", "onChange", "totalXP", "toLocaleString", "averageScore", "totalQuizzesTaken", "currentStreak", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    if (!formData.name) return message.error(\"Please enter your name.\");\r\n    if (!formData.class_) return message.error(\"Please select a class.\");\r\n\r\n    if (\r\n      !skipOTP &&\r\n      formData.email !== userDetails.email\r\n    ) {\r\n      return sendOTPRequest(formData.email);\r\n    }\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      });\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* Profile Picture - Clickable for Change */}\r\n                <div className=\"relative mb-4\">\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-4 border-blue-200 relative cursor-pointer hover:border-blue-400 transition-colors duration-200\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\r\n                      width: '80px',\r\n                      height: '80px'\r\n                    }}\r\n                    onClick={() => document.getElementById('profileImageInput').click()}\r\n                  >\r\n                    {userDetails?.profileImage ? (\r\n                      <img\r\n                        src={userDetails.profileImage}\r\n                        alt=\"Profile\"\r\n                        className=\"object-cover rounded-full w-full h-full\"\r\n                        onError={(e) => {\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                    ) : null}\r\n                    <div\r\n                      className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\r\n                      style={{\r\n                        background: '#25D366',\r\n                        color: '#FFFFFF',\r\n                        fontSize: '24px'\r\n                      }}\r\n                    >\r\n                      {userDetails?.name?.charAt(0)?.toUpperCase() || 'U'}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Camera Icon Overlay */}\r\n                  <div className=\"absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                       onClick={() => document.getElementById('profileImageInput').click()}>\r\n                    <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                    </svg>\r\n                  </div>\r\n\r\n                  {/* Hidden File Input */}\r\n                  <input\r\n                    id=\"profileImageInput\"\r\n                    type=\"file\"\r\n                    accept=\"image/*\"\r\n                    className=\"hidden\"\r\n                    onChange={handleImageChange}\r\n                  />\r\n                </div>\r\n\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Email</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.email || '<EMAIL>'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.name || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.email || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.school || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.level || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.class || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.phoneNumber || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center\">\r\n                <button className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\">\r\n                  Edit Profile\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,EACfC,OAAO,QACF,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtG,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC;IACvC0C,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACoD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAMwD,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAE9B,MAAM8C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM5C,uBAAuB,CAAC,CAAC;MAChD,IAAI4C,QAAQ,CAACC,OAAO,EAAE;QACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLrD,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;MACjC;MACAiD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;MAC5BiD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMkD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAGjC,WAAW,CACxBkC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3C,WAAW,CAAC4C,GAAG,CAAC,CAAC;IAC/DvC,cAAc,CAAC8B,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,EAAC7C,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE4C,GAAG,GAAE;IAEvB,IAAI;MACFhB,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAM6D,eAAe,GAAG,MAAM3D,cAAc,CAACa,WAAW,CAAC4C,GAAG,EAAE,CAAC,CAAC;MAEhE,IAAIE,eAAe,CAACf,OAAO,EAAE;QAC3BxB,mBAAmB,CAACuC,eAAe,CAACd,IAAI,CAAC;MAC3C;;MAEA;MACA,MAAMe,mBAAmB,GAAG,MAAM3D,gBAAgB,CAAC;QACjD4D,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,CAAAjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;MACrC,CAAC,CAAC;MAEF,IAAI8B,mBAAmB,CAAChB,OAAO,EAAE;QAC/B,MAAMmB,SAAS,GAAGH,mBAAmB,CAACf,IAAI,CAACmB,SAAS,CAACd,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK5C,WAAW,CAAC4C,GAAG,CAAC;QAC1F,IAAIM,SAAS,IAAI,CAAC,EAAE;UAClB,MAAME,YAAY,GAAG;YACnB,GAAGL,mBAAmB,CAACf,IAAI,CAACkB,SAAS,CAAC;YACtCG,IAAI,EAAEH,SAAS,GAAG,CAAC;YACnBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB;UACvC,CAAC;UACDhD,mBAAmB,CAACiD,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACPC,QAAQ,EAAEP,SAAS,GAAG,CAAC;YACvBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB,MAAM;YAC3ClB,IAAI,EAAEe;UACR,CAAC,CAAC,CAAC;QACL;MACF;MAEAxB,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACdL,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;MACvB0E,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED9D,SAAS,CAAC,MAAM;IACd,IAAI+B,WAAW,IAAIF,WAAW,EAAE;MAC9BkC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChC,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM2D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B/B,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMxD,WAAW,CAAC,CAAC;MACpC,IAAIwD,QAAQ,CAACC,OAAO,EAAE;QACpB9B,cAAc,CAAC6B,QAAQ,CAACE,IAAI,CAAC;QAC7BnB,WAAW,CAAC;UACVC,IAAI,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEe,QAAQ,CAACE,IAAI,CAACjB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAAChB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEY,QAAQ,CAACE,IAAI,CAAC4B,KAAK,IAAI,EAAE;UACjC3C,KAAK,EAAEa,QAAQ,CAACE,IAAI,CAACf,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEW,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACZ,YAAY,EAAE;UAC9BC,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACZ,YAAY,CAAC;QAC7C;QACAS,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLlD,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRiD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAI0F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElD,IAAI;MAAEmD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIpD,IAAI,KAAK,aAAa,IAAImD,KAAK,CAACV,MAAM,GAAG,EAAE,EAAE;IACjD,IAAIzC,IAAI,KAAK,OAAO,IAAImD,KAAK,MAAKjE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAIgD,KAAK,KAAK,EAAE,EAAE;MACpEtC,qBAAqB,CAACsC,KAAK,CAAC;MAC5BxC,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC1C,IAAI,GAAGmD,KAAK;MACb,IAAInD,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiD,cAAc,GAAGA,CAAA,KAAM;IAC3BtD,WAAW,CAAC;MACVC,IAAI,EAAEd,WAAW,CAACc,IAAI;MACtBC,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBC,MAAM,EAAEhB,WAAW,CAACgB,MAAM;MAC1BE,MAAM,EAAElB,WAAW,CAAC4D,KAAK;MACzB3C,KAAK,EAAEjB,WAAW,CAACiB,KAAK;MACxBE,WAAW,EAAEnB,WAAW,CAACmB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAM2D,cAAc,GAAG,MAAOrD,KAAK,IAAK;IACtCa,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMrD,OAAO,CAAC;QAAEsC;MAAM,CAAC,CAAC;MACzC,IAAIe,QAAQ,CAACC,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAAC,0BAA0B,CAAC;QAC3CtB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAACO,QAAQ,CAACE,IAAI,CAAC;MACtC,CAAC,MAAM;QACLrD,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;QAC/BwF,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;MAC5BwF,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACRvC,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMqF,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/C,IAAI,CAAC1D,QAAQ,CAACE,IAAI,EAAE,OAAOnC,OAAO,CAACsD,KAAK,CAAC,yBAAyB,CAAC;IACnE,IAAI,CAACrB,QAAQ,CAACM,MAAM,EAAE,OAAOvC,OAAO,CAACsD,KAAK,CAAC,wBAAwB,CAAC;IAEpE,IACE,CAACqC,OAAO,IACR1D,QAAQ,CAACG,KAAK,KAAKf,WAAW,CAACe,KAAK,EACpC;MACA,OAAOqD,cAAc,CAACxD,QAAQ,CAACG,KAAK,CAAC;IACvC;IAEAa,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMvD,cAAc,CAAC;QACpC,GAAGqC,QAAQ;QACX8B,MAAM,EAAE1C,WAAW,CAAC4C;MACtB,CAAC,CAAC;MACF,IAAId,QAAQ,CAACC,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAACD,QAAQ,CAACnD,OAAO,CAAC;QACjC8B,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAAC,IAAI,CAAC;QAC3BoC,WAAW,CAAC,CAAC;QACb,IAAI7B,QAAQ,CAACyC,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACLhG,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRiD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM4F,wBAAwB,GAAGA,CAAA,KAAM;IACrC/D,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvC,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkD,uBAAuB,GAAGA,CAAA,KAAM;IACpCpD,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMmD,iBAAiB,GAAG,MAAOd,CAAC,IAAK;IACrC,MAAMe,IAAI,GAAGf,CAAC,CAACE,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCvG,OAAO,CAACsD,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACF;;MAEA;MACA,IAAI8C,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BxG,OAAO,CAACsD,KAAK,CAAC,oCAAoC,CAAC;QACnD;MACF;MAEAZ,eAAe,CAAC0D,IAAI,CAAC;;MAErB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM3E,eAAe,CAACyE,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;MAE1B;MACA,MAAM/C,IAAI,GAAG,IAAIyD,QAAQ,CAAC,CAAC;MAC3BzD,IAAI,CAAC0D,MAAM,CAAC,cAAc,EAAEX,IAAI,CAAC;MACjCnD,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI;QACF,MAAM6C,QAAQ,GAAG,MAAMtD,eAAe,CAACwD,IAAI,CAAC;QAC5CJ,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;QACvB,IAAI8C,QAAQ,CAACC,OAAO,EAAE;UACpBpD,OAAO,CAACoD,OAAO,CAAC,uCAAuC,CAAC;UACxD4B,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACLhF,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACdL,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;QACvBL,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,IAAI,kCAAkC,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAMgH,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAM3D,IAAI,GAAG,IAAIyD,QAAQ,CAAC,CAAC;IAC3BzD,IAAI,CAAC0D,MAAM,CAAC,cAAc,EAAEtE,YAAY,CAAC;IACzCQ,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMtD,eAAe,CAACwD,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAAC,6BAA6B,CAAC;QAC9C4B,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLhF,OAAO,CAACsD,KAAK,CAACH,QAAQ,CAACnD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRiD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM4G,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACC,GAAG,KAAKxE,kBAAkB,EAAE;MACrC+C,YAAY,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACL3F,OAAO,CAACsD,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;;EAED;EACA9D,SAAS,CAAC,MAAM;IACdwF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACd,IAAI6B,WAAW,EAAE;MACf6C,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC7C,WAAW,CAAC,CAAC;EAEjB,oBACEV,OAAA;IAAKyG,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjF1G,OAAA;MAAKyG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1G,OAAA;QAAKyG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC1G,OAAA;UAAKyG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B1G,OAAA;YAAIyG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE9G,OAAA;YAAGyG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAGN9G,OAAA;UAAKyG,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7D1G,OAAA;YAAKyG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB1G,OAAA;cAAKyG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE9C1G,OAAA;gBAAKyG,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B1G,OAAA;kBACEyG,SAAS,EAAC,oIAAoI;kBAC9IM,KAAK,EAAE;oBACLC,UAAU,EAAE,SAAS;oBACrBC,SAAS,EAAE,6BAA6B;oBACxCC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBACFC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;kBAAAb,QAAA,GAEnEhG,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoB,YAAY,gBACxB9B,OAAA;oBACEwH,GAAG,EAAE9G,WAAW,CAACoB,YAAa;oBAC9B2F,GAAG,EAAC,SAAS;oBACbhB,SAAS,EAAC,yCAAyC;oBACnDiB,OAAO,EAAGhD,CAAC,IAAK;sBACdA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACY,OAAO,GAAG,MAAM;sBAC/BjD,CAAC,CAACE,MAAM,CAACgD,WAAW,CAACb,KAAK,CAACY,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACA,IAAI,eACR9G,OAAA;oBACEyG,SAAS,EAAC,2EAA2E;oBACrFM,KAAK,EAAE;sBACLC,UAAU,EAAE,SAAS;sBACrBa,KAAK,EAAE,SAAS;sBAChBC,QAAQ,EAAE;oBACZ,CAAE;oBAAApB,QAAA,EAED,CAAAhG,WAAW,aAAXA,WAAW,wBAAAP,iBAAA,GAAXO,WAAW,CAAEc,IAAI,cAAArB,iBAAA,wBAAAC,qBAAA,GAAjBD,iBAAA,CAAmB4H,MAAM,CAAC,CAAC,CAAC,cAAA3H,qBAAA,uBAA5BA,qBAAA,CAA8B4H,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN9G,OAAA;kBAAKyG,SAAS,EAAC,kIAAkI;kBAC5IW,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;kBAAAb,QAAA,eACvE1G,OAAA;oBAAKyG,SAAS,EAAC,oBAAoB;oBAACwB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAzB,QAAA,gBACvF1G,OAAA;sBAAMoI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkK;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1O9G,OAAA;sBAAMoI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN9G,OAAA;kBACEwI,EAAE,EAAC,mBAAmB;kBACtB7C,IAAI,EAAC,MAAM;kBACX8C,MAAM,EAAC,SAAS;kBAChBhC,SAAS,EAAC,QAAQ;kBAClBiC,QAAQ,EAAElD;gBAAkB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9G,OAAA;gBAAKyG,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE1G,OAAA;kBAAKyG,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF1G,OAAA;oBAAGyG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzD9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAM;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBACrF1G,OAAA;oBAAGyG,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D9G,OAAA;oBAAGyG,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,EAAE,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI;kBAAkB;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF1G,OAAA;oBAAGyG,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAK;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL9F,gBAAgB,iBACfhB,OAAA;gBAAKyG,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC9D1G,OAAA;kBAAKyG,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF1G,OAAA;oBAAGyG,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,GAC5C,EAAC1F,gBAAgB,CAACmD,QAAQ,IAAI,KAAK,EACnCnD,gBAAgB,CAACgD,UAAU,iBAC1BhE,OAAA;sBAAMyG,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAAC1F,gBAAgB,CAACgD,UAAU;oBAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAC7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF1G,OAAA;oBAAGyG,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/D9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAArG,qBAAA,GAAAW,gBAAgB,CAAC+B,IAAI,cAAA1C,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBsI,OAAO,cAAArI,sBAAA,uBAA9BA,sBAAA,CAAgCsI,cAAc,CAAC,CAAC,KAAI;kBAAG;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF1G,OAAA;oBAAGyG,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChE9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAC3C,EAAAnG,sBAAA,GAAAS,gBAAgB,CAAC+B,IAAI,cAAAxC,sBAAA,uBAArBA,sBAAA,CAAuBsI,YAAY,KAAI,GAAG,EAAC,GAC9C;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF1G,OAAA;oBAAGyG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAlG,sBAAA,GAAAQ,gBAAgB,CAAC+B,IAAI,cAAAvC,sBAAA,uBAArBA,sBAAA,CAAuBsI,iBAAiB,KAAI;kBAAG;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN9G,OAAA;kBAAKyG,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF1G,OAAA;oBAAGyG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D9G,OAAA;oBAAGyG,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAjG,sBAAA,GAAAO,gBAAgB,CAAC+B,IAAI,cAAAtC,sBAAA,uBAArBA,sBAAA,CAAuBsI,aAAa,KAAI;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN9G,OAAA;cAAKyG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD1G,OAAA;gBAAKyG,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5E9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAc;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI;kBAAc;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9E9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI;kBAAc;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9G,OAAA;gBAAKyG,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;kBAAc;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAc;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAOyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpF9G,OAAA;oBAAKyG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAhG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAAc;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9G,OAAA;cAAKyG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvC1G,OAAA;gBAAQyG,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EAAC;cAE7H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5G,EAAA,CAzeID,OAAO;EAAA,QAmBMR,WAAW;AAAA;AAAAuJ,EAAA,GAnBxB/I,OAAO;AA2eb,eAAeA,OAAO;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}