{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbSearch, TbFilter, TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX, TbStar, TbHome, TbBolt } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport './responsive.css';\nimport './style.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getUserResults = async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId) return;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: report.verdict,\n              percentage: report.percentage,\n              correctAnswers: report.correctAnswers,\n              wrongAnswers: report.wrongAnswers,\n              totalQuestions: report.totalQuestions,\n              obtainedMarks: report.obtainedMarks,\n              totalMarks: report.totalMarks,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  };\n  useEffect(() => {\n    const getExams = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getAllExams();\n        dispatch(HideLoading());\n        if (response.success) {\n          // Filter exams by user's level\n          const userLevelExams = response.data.filter(exam => {\n            if (!exam.level || !user.level) return false;\n            return exam.level.toLowerCase() === user.level.toLowerCase();\n          });\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n          setExams(sortedExams);\n\n          // Set default class filter to user's class\n          if (user !== null && user !== void 0 && user.class) {\n            setSelectedClass(String(user.class));\n          }\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    getExams();\n    getUserResults();\n  }, [dispatch, user === null || user === void 0 ? void 0 : user.level, user === null || user === void 0 ? void 0 : user.class, user === null || user === void 0 ? void 0 : user._id]);\n\n  // Real-time updates for quiz completion\n  useEffect(() => {\n    // Listen for real-time updates from quiz completion\n    const handleRankingUpdate = () => {\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\n      getUserResults(); // Refresh user results to show updated XP\n    };\n\n    // Listen for window focus to refresh data when returning from quiz\n    const handleWindowFocus = () => {\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\n      getUserResults();\n    };\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    window.addEventListener('focus', handleWindowFocus);\n    return () => {\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n      window.removeEventListener('focus', handleWindowFocus);\n    };\n  }, []);\n  useEffect(() => {\n    let filtered = exams;\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$name, _exam$subject;\n        return ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    navigate(`/quiz/${quiz._id}/play`);\n  };\n\n  // Custom Quiz Card Component\n  const QuizCard = ({\n    quiz,\n    userResult,\n    onStart\n  }) => {\n    var _quiz$questions;\n    const getStatusInfo = () => {\n      if (!userResult) {\n        return {\n          status: 'not-attempted',\n          color: 'bg-blue-100 text-blue-800',\n          icon: TbTarget,\n          text: 'Not Attempted',\n          xpEarned: 0\n        };\n      }\n      if (userResult.verdict === 'Pass') {\n        return {\n          status: 'passed',\n          color: 'bg-green-100 text-green-800',\n          icon: TbCheck,\n          text: `Passed (${userResult.percentage}%)`,\n          xpEarned: userResult.xpEarned || quiz.xpPoints || 100\n        };\n      } else {\n        return {\n          status: 'failed',\n          color: 'bg-red-100 text-red-800',\n          icon: TbX,\n          text: `Failed (${userResult.percentage}%)`,\n          xpEarned: userResult.xpEarned || 0\n        };\n      }\n    };\n    const statusInfo = getStatusInfo();\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      whileHover: {\n        y: -8,\n        scale: 1.02\n      },\n      transition: {\n        duration: 0.3\n      },\n      className: \"bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden group\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white bg-opacity-20 rounded-lg p-2\",\n              children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1\",\n                children: [/*#__PURE__*/_jsxDEV(StatusIcon, {\n                  className: \"w-3 h-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), statusInfo.text]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold mb-2 line-clamp-2\",\n            children: quiz.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap items-center gap-2 text-sm opacity-90\",\n            children: [quiz.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\",\n              children: [\"\\uD83D\\uDCDA \", quiz.subject]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), quiz.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\",\n              children: [\"\\uD83C\\uDFAF \", quiz.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\",\n              children: [\"Class \", quiz.class]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-2 mb-1\",\n              children: /*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                className: \"w-4 h-4 text-blue-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm font-bold text-gray-900\",\n              children: ((_quiz$questions = quiz.questions) === null || _quiz$questions === void 0 ? void 0 : _quiz$questions.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 rounded-lg p-2 mb-1\",\n              children: /*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-4 h-4 text-green-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm font-bold text-gray-900\",\n              children: quiz.duration || 30\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Minutes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-2 mb-1 border border-yellow-200\",\n              children: /*#__PURE__*/_jsxDEV(TbBolt, {\n                className: \"w-4 h-4 text-yellow-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm font-bold text-yellow-700\",\n              children: quiz.xpPoints || 100\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-yellow-600 font-medium\",\n              children: \"XP Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 rounded-lg p-2 mb-1\",\n              children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-4 h-4 text-purple-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm font-bold text-gray-900\",\n              children: [quiz.passingMarks || 70, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Pass Mark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), userResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Your Result\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold\",\n              children: [userResult.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-2 rounded-full transition-all duration-500 ${userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'}`,\n              style: {\n                width: `${userResult.percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [userResult.correctAnswers || 0, \" correct \\u2022 \", userResult.wrongAnswers || 0, \" wrong\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n                className: \"w-3 h-3 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-bold text-yellow-700\",\n                children: [\"+\", statusInfo.xpEarned, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onStart(quiz),\n          className: \"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group\",\n          children: [/*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n            className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), userResult ? 'Retake Quiz' : 'Start Quiz']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0 flex justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 sm:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden shadow-lg border-2 border-white\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-black/20\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-1/3 bg-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold truncate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-black\",\n                  children: \"Brain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: \"wave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden shadow-lg border-2 border-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm\",\n                  style: {\n                    display: 'none'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-1 text-sm sm:text-base hidden sm:block absolute top-12 left-1/2 transform -translate-x-1/2\",\n              children: \"Challenge your brain, Beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => navigate('/user/hub'),\n              className: \"hub-button flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-300 text-xs sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                className: \"w-3 h-3 sm:w-4 sm:h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Hub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right hidden md:block\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900 truncate max-w-32 lg:max-w-none\",\n                children: user === null || user === void 0 ? void 0 : user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [user === null || user === void 0 ? void 0 : user.level, \" - Class \", user === null || user === void 0 ? void 0 : user.class]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-semibold text-xs sm:text-sm\",\n                children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://flagcdn.com/w40/tz.png\",\n              alt: \"Tanzania\",\n              className: \"w-5 h-3 sm:w-6 sm:h-4 rounded-sm flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: \"Challenge Your Brain, Beat the Rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\",\n          children: [\"Test your knowledge with our comprehensive quizzes designed for Class \", (user === null || user === void 0 ? void 0 : user.class) || 'All Classes']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [filteredExams.length, \" Available Quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"max-w-4xl mx-auto mb-8 sm:mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-4 sm:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sm:w-48 md:w-64\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"quiz-listing-content\",\n        children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12 sm:py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No Quizzes Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm sm:text-base\",\n              children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-grid-container grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6\",\n          children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: Math.min(index * 0.1, 0.8)\n            },\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(QuizCard, {\n              quiz: quiz,\n              userResult: userResults[quiz._id],\n              onStart: handleQuizStart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n_s(Quiz, \"smeEF9GR69BdE1ZVtjkix2mojGc=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "motion", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Quiz", "_s", "_user$name", "_user$name$charAt", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "timeTaken", "completedAt", "error", "console", "getExams", "userLevelExams", "filter", "level", "toLowerCase", "sortedExams", "sort", "a", "b", "class", "String", "handleRankingUpdate", "log", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "QuizCard", "userResult", "onStart", "_quiz$questions", "getStatusInfo", "status", "color", "icon", "text", "xpEarned", "xpPoints", "statusInfo", "StatusIcon", "div", "initial", "opacity", "y", "animate", "whileHover", "scale", "transition", "duration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "topic", "questions", "length", "passingMarks", "style", "width", "onClick", "src", "alt", "onError", "target", "display", "nextS<PERSON>ling", "button", "whileTap", "char<PERSON>t", "toUpperCase", "delay", "type", "placeholder", "value", "onChange", "index", "Math", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { motion } from 'framer-motion';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON><PERSON>ch,\r\n  Tb<PERSON>ilter,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './responsive.css';\r\nimport './style.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId) return;\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: report.verdict,\r\n              percentage: report.percentage,\r\n              correctAnswers: report.correctAnswers,\r\n              wrongAnswers: report.wrongAnswers,\r\n              totalQuestions: report.totalQuestions,\r\n              obtainedMarks: report.obtainedMarks,\r\n              totalMarks: report.totalMarks,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const getExams = async () => {\r\n      try {\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          // Filter exams by user's level\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    getExams();\r\n    getUserResults();\r\n  }, [dispatch, user?.level, user?.class, user?._id]);\r\n\r\n  // Real-time updates for quiz completion\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for window focus to refresh data when returning from quiz\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\r\n      getUserResults();\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    navigate(`/quiz/${quiz._id}/play`);\r\n  };\r\n\r\n  // Custom Quiz Card Component\r\n  const QuizCard = ({ quiz, userResult, onStart }) => {\r\n    const getStatusInfo = () => {\r\n      if (!userResult) {\r\n        return {\r\n          status: 'not-attempted',\r\n          color: 'bg-blue-100 text-blue-800',\r\n          icon: TbTarget,\r\n          text: 'Not Attempted',\r\n          xpEarned: 0\r\n        };\r\n      }\r\n\r\n      if (userResult.verdict === 'Pass') {\r\n        return {\r\n          status: 'passed',\r\n          color: 'bg-green-100 text-green-800',\r\n          icon: TbCheck,\r\n          text: `Passed (${userResult.percentage}%)`,\r\n          xpEarned: userResult.xpEarned || quiz.xpPoints || 100\r\n        };\r\n      } else {\r\n        return {\r\n          status: 'failed',\r\n          color: 'bg-red-100 text-red-800',\r\n          icon: TbX,\r\n          text: `Failed (${userResult.percentage}%)`,\r\n          xpEarned: userResult.xpEarned || 0\r\n        };\r\n      }\r\n    };\r\n\r\n    const statusInfo = getStatusInfo();\r\n    const StatusIcon = statusInfo.icon;\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -8, scale: 1.02 }}\r\n        transition={{ duration: 0.3 }}\r\n        className=\"bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden group\"\r\n      >\r\n        {/* Header with gradient */}\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16\"></div>\r\n          <div className=\"relative z-10\">\r\n            <div className=\"flex items-start justify-between mb-4\">\r\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-2\">\r\n                <TbBrain className=\"w-6 h-6\" />\r\n              </div>\r\n              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`}>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <StatusIcon className=\"w-3 h-3\" />\r\n                  {statusInfo.text}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <h3 className=\"text-xl font-bold mb-2 line-clamp-2\">{quiz.name}</h3>\r\n            <div className=\"flex flex-wrap items-center gap-2 text-sm opacity-90\">\r\n              {quiz.subject && (\r\n                <span className=\"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\">📚 {quiz.subject}</span>\r\n              )}\r\n              {quiz.topic && (\r\n                <span className=\"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\">🎯 {quiz.topic}</span>\r\n              )}\r\n              <span className=\"bg-white bg-opacity-20 px-2 py-1 rounded text-xs\">Class {quiz.class}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6\">\r\n          {/* Quiz Stats */}\r\n          <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6\">\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-blue-50 rounded-lg p-2 mb-1\">\r\n                <TbQuestionMark className=\"w-4 h-4 text-blue-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-sm font-bold text-gray-900\">{quiz.questions?.length || 0}</div>\r\n              <div className=\"text-xs text-gray-500\">Questions</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-green-50 rounded-lg p-2 mb-1\">\r\n                <TbClock className=\"w-4 h-4 text-green-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-sm font-bold text-gray-900\">{quiz.duration || 30}</div>\r\n              <div className=\"text-xs text-gray-500\">Minutes</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-2 mb-1 border border-yellow-200\">\r\n                <TbBolt className=\"w-4 h-4 text-yellow-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-sm font-bold text-yellow-700\">{quiz.xpPoints || 100}</div>\r\n              <div className=\"text-xs text-yellow-600 font-medium\">XP Points</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-purple-50 rounded-lg p-2 mb-1\">\r\n                <TbTarget className=\"w-4 h-4 text-purple-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-sm font-bold text-gray-900\">{quiz.passingMarks || 70}%</div>\r\n              <div className=\"text-xs text-gray-500\">Pass Mark</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Progress Bar (if attempted) */}\r\n          {userResult && (\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between text-sm mb-2\">\r\n                <span className=\"text-gray-600\">Your Result</span>\r\n                <span className=\"font-semibold\">{userResult.percentage}%</span>\r\n              </div>\r\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                  className={`h-2 rounded-full transition-all duration-500 ${\r\n                    userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'\r\n                  }`}\r\n                  style={{ width: `${userResult.percentage}%` }}\r\n                ></div>\r\n              </div>\r\n              <div className=\"flex justify-between text-xs text-gray-500 mt-2\">\r\n                <span>{userResult.correctAnswers || 0} correct • {userResult.wrongAnswers || 0} wrong</span>\r\n                <div className=\"flex items-center gap-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-200\">\r\n                  <TbBolt className=\"w-3 h-3 text-yellow-600\" />\r\n                  <span className=\"font-bold text-yellow-700\">+{statusInfo.xpEarned} XP</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Action Button */}\r\n          <button\r\n            onClick={() => onStart(quiz)}\r\n            className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group\"\r\n          >\r\n            <TbPlayerPlay className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\r\n            {userResult ? 'Retake Quiz' : 'Start Quiz'}\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive\">\r\n      {/* Top Header with User Info */}\r\n      <div className=\"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-40\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex-1 min-w-0 flex justify-center\">\r\n              <div className=\"flex items-center space-x-2 sm:space-x-3\">\r\n                {/* Tanzania Flag */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <div className=\"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\">\r\n                    <div className=\"absolute inset-0 bg-black/20\"></div>\r\n                    <div className=\"absolute top-0 left-0 w-full h-1/3 bg-green-500\"></div>\r\n                    <div className=\"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"></div>\r\n                    <div className=\"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Brainwave Text */}\r\n                <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold truncate\">\r\n                  <span className=\"text-black\">Brain</span>\r\n                  <span className=\"text-green-600\">wave</span>\r\n                </h1>\r\n\r\n                {/* Official Logo */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm\" style={{display: 'none'}}>\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-600 mt-1 text-sm sm:text-base hidden sm:block absolute top-12 left-1/2 transform -translate-x-1/2\">Challenge your brain, Beat the rest</p>\r\n            </div>\r\n\r\n            {/* Hub Button and User Profile */}\r\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\r\n              {/* Hub Button */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => navigate('/user/hub')}\r\n                className=\"hub-button flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-300 text-xs sm:text-sm\"\r\n              >\r\n                <TbHome className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                <span className=\"hidden sm:inline\">Hub</span>\r\n              </motion.button>\r\n\r\n              <div className=\"text-right hidden md:block\">\r\n                <p className=\"text-sm font-medium text-gray-900 truncate max-w-32 lg:max-w-none\">{user?.name}</p>\r\n                <p className=\"text-xs text-gray-500\">{user?.level} - Class {user?.class}</p>\r\n              </div>\r\n              <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                <span className=\"text-blue-600 font-semibold text-xs sm:text-sm\">\r\n                  {user?.name?.charAt(0)?.toUpperCase()}\r\n                </span>\r\n              </div>\r\n              <img\r\n                src=\"https://flagcdn.com/w40/tz.png\"\r\n                alt=\"Tanzania\"\r\n                className=\"w-5 h-3 sm:w-6 sm:h-4 rounded-sm flex-shrink-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Hero Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-8 sm:mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\">\r\n            Test your knowledge with our comprehensive quizzes designed for Class {user?.class || 'All Classes'}\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Search and Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"max-w-4xl mx-auto mb-8 sm:mb-12\"\r\n        >\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\">\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n              <div className=\"sm:w-48 md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Quiz Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"quiz-listing-content\"\r\n        >\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-12 sm:py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"quiz-grid-container grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <motion.div\r\n                  key={quiz._id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: Math.min(index * 0.1, 0.8) }}\r\n                  className=\"h-full\"\r\n                >\r\n                  <QuizCard\r\n                    quiz={quiz}\r\n                    userResult={userResults[quiz._id]}\r\n                    onStart={handleQuizStart}\r\n                  />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,kBAAkB;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM0C,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyC;EAAK,CAAC,GAAGxC,WAAW,CAAEyC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAI,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAM3B,mBAAmB,CAAC;QAAE4B,MAAM,EAAEL,IAAI,CAACG;MAAI,CAAC,CAAC;MAChE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,GAAG;UAC/B,IAAI,CAACS,MAAM,EAAE;UACb,IAAI,CAACL,UAAU,CAACK,MAAM,CAAC,IAAI,IAAIE,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,GAAG,IAAID,IAAI,CAACP,UAAU,CAACK,MAAM,CAAC,CAACG,SAAS,CAAC,EAAE;YAC9FR,UAAU,CAACK,MAAM,CAAC,GAAG;cACnBI,OAAO,EAAEN,MAAM,CAACM,OAAO;cACvBC,UAAU,EAAEP,MAAM,CAACO,UAAU;cAC7BC,cAAc,EAAER,MAAM,CAACQ,cAAc;cACrCC,YAAY,EAAET,MAAM,CAACS,YAAY;cACjCC,cAAc,EAAEV,MAAM,CAACU,cAAc;cACrCC,aAAa,EAAEX,MAAM,CAACW,aAAa;cACnCC,UAAU,EAAEZ,MAAM,CAACY,UAAU;cAC7BC,SAAS,EAAEb,MAAM,CAACa,SAAS;cAC3BC,WAAW,EAAEd,MAAM,CAACK;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFpB,cAAc,CAACY,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAEDpE,SAAS,CAAC,MAAM;IACd,MAAMsE,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF5B,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMyB,QAAQ,GAAG,MAAM5B,WAAW,CAAC,CAAC;QACpCuB,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI0B,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMsB,cAAc,GAAGxB,QAAQ,CAACI,IAAI,CAACqB,MAAM,CAAChB,IAAI,IAAI;YAClD,IAAI,CAACA,IAAI,CAACiB,KAAK,IAAI,CAAC9B,IAAI,CAAC8B,KAAK,EAAE,OAAO,KAAK;YAC5C,OAAOjB,IAAI,CAACiB,KAAK,CAACC,WAAW,CAAC,CAAC,KAAK/B,IAAI,CAAC8B,KAAK,CAACC,WAAW,CAAC,CAAC;UAC9D,CAAC,CAAC;UAEF,MAAMC,WAAW,GAAGJ,cAAc,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrB,IAAI,CAACqB,CAAC,CAACpB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACoB,CAAC,CAACnB,SAAS,CAAC,CAAC;UAChG5B,QAAQ,CAAC6C,WAAW,CAAC;;UAErB;UACA,IAAIhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoC,KAAK,EAAE;YACf3C,gBAAgB,CAAC4C,MAAM,CAACrC,IAAI,CAACoC,KAAK,CAAC,CAAC;UACtC;QACF,CAAC,MAAM;UACL1E,OAAO,CAAC+D,KAAK,CAACrB,QAAQ,CAAC1C,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;QACd1B,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAAC;QACvBhB,OAAO,CAAC+D,KAAK,CAACA,KAAK,CAAC/D,OAAO,CAAC;MAC9B,CAAC,SAAS;QACRmC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8B,QAAQ,CAAC,CAAC;IACVzB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,QAAQ,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,EAAE9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK,EAAEpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC;;EAEnD;EACA9C,SAAS,CAAC,MAAM;IACd;IACA,MAAMiF,mBAAmB,GAAGA,CAAA,KAAM;MAChCZ,OAAO,CAACa,GAAG,CAAC,4DAA4D,CAAC;MACzErC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMsC,iBAAiB,GAAGA,CAAA,KAAM;MAC9Bd,OAAO,CAACa,GAAG,CAAC,sDAAsD,CAAC;MACnErC,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDuC,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;IAEnD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;MAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENnF,SAAS,CAAC,MAAM;IACd,IAAIuF,QAAQ,GAAG1D,KAAK;IACpB,IAAII,UAAU,EAAE;MACdsD,QAAQ,GAAGA,QAAQ,CAACf,MAAM,CAAChB,IAAI;QAAA,IAAAgC,UAAA,EAAAC,aAAA;QAAA,OAC7B,EAAAD,UAAA,GAAAhC,IAAI,CAACkC,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWd,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAAC1D,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,OAAAe,aAAA,GAC3DjC,IAAI,CAACoC,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcf,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAAC1D,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;IACA,IAAIvC,aAAa,EAAE;MACjBoD,QAAQ,GAAGA,QAAQ,CAACf,MAAM,CAAChB,IAAI,IAAIwB,MAAM,CAACxB,IAAI,CAACuB,KAAK,CAAC,KAAKC,MAAM,CAAC7C,aAAa,CAAC,CAAC;IAClF;IACAoD,QAAQ,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrB,IAAI,CAACqB,CAAC,CAACpB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACoB,CAAC,CAACnB,SAAS,CAAC,CAAC;IACtE1B,gBAAgB,CAACuD,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAAC1D,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAM0D,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACjE,KAAK,CAACkE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,KAAK,CAAC,CAACP,MAAM,CAACyB,OAAO,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC;EAErF,MAAMsB,eAAe,GAAIC,IAAI,IAAK;IAChC1D,QAAQ,CAAE,SAAQ0D,IAAI,CAACrD,GAAI,OAAM,CAAC;EACpC,CAAC;;EAED;EACA,MAAMsD,QAAQ,GAAGA,CAAC;IAAED,IAAI;IAAEE,UAAU;IAAEC;EAAQ,CAAC,KAAK;IAAA,IAAAC,eAAA;IAClD,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI,CAACH,UAAU,EAAE;QACf,OAAO;UACLI,MAAM,EAAE,eAAe;UACvBC,KAAK,EAAE,2BAA2B;UAClCC,IAAI,EAAE9F,QAAQ;UACd+F,IAAI,EAAE,eAAe;UACrBC,QAAQ,EAAE;QACZ,CAAC;MACH;MAEA,IAAIR,UAAU,CAAC1C,OAAO,KAAK,MAAM,EAAE;QACjC,OAAO;UACL8C,MAAM,EAAE,QAAQ;UAChBC,KAAK,EAAE,6BAA6B;UACpCC,IAAI,EAAE7F,OAAO;UACb8F,IAAI,EAAG,WAAUP,UAAU,CAACzC,UAAW,IAAG;UAC1CiD,QAAQ,EAAER,UAAU,CAACQ,QAAQ,IAAIV,IAAI,CAACW,QAAQ,IAAI;QACpD,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLL,MAAM,EAAE,QAAQ;UAChBC,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAE5F,GAAG;UACT6F,IAAI,EAAG,WAAUP,UAAU,CAACzC,UAAW,IAAG;UAC1CiD,QAAQ,EAAER,UAAU,CAACQ,QAAQ,IAAI;QACnC,CAAC;MACH;IACF,CAAC;IAED,MAAME,UAAU,GAAGP,aAAa,CAAC,CAAC;IAClC,MAAMQ,UAAU,GAAGD,UAAU,CAACJ,IAAI;IAElC,oBACEnF,OAAA,CAACpB,MAAM,CAAC6G,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEF,CAAC,EAAE,CAAC,CAAC;QAAEG,KAAK,EAAE;MAAK,CAAE;MACnCC,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BC,SAAS,EAAC,8FAA8F;MAAAC,QAAA,gBAGxGnG,OAAA;QAAKkG,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGnG,OAAA;UAAKkG,SAAS,EAAC;QAAiF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvGvG,OAAA;UAAKkG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnG,OAAA;YAAKkG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnG,OAAA;cAAKkG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpDnG,OAAA,CAACZ,OAAO;gBAAC8G,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAG,gDAA+CX,UAAU,CAACL,KAAM,WAAW;cAAAiB,QAAA,eAC1FnG,OAAA;gBAAKkG,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCnG,OAAA,CAACwF,UAAU;kBAACU,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACjChB,UAAU,CAACH,IAAI;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvG,OAAA;YAAIkG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAExB,IAAI,CAACT;UAAI;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpEvG,OAAA;YAAKkG,SAAS,EAAC,sDAAsD;YAAAC,QAAA,GAClExB,IAAI,CAACP,OAAO,iBACXpE,OAAA;cAAMkG,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAAC,eAAG,EAACxB,IAAI,CAACP,OAAO;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAC3F,EACA5B,IAAI,CAAC6B,KAAK,iBACTxG,OAAA;cAAMkG,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAAC,eAAG,EAACxB,IAAI,CAAC6B,KAAK;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACzF,eACDvG,OAAA;cAAMkG,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAAC,QAAM,EAACxB,IAAI,CAACpB,KAAK;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvG,OAAA;QAAKkG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBnG,OAAA;UAAKkG,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDnG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAKkG,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7CnG,OAAA,CAACf,cAAc;gBAACiH,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAE,EAAApB,eAAA,GAAAJ,IAAI,CAAC8B,SAAS,cAAA1B,eAAA,uBAAdA,eAAA,CAAgB2B,MAAM,KAAI;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpFvG,OAAA;cAAKkG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAKkG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,eAC9CnG,OAAA,CAAChB,OAAO;gBAACkH,SAAS,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAExB,IAAI,CAACsB,QAAQ,IAAI;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5EvG,OAAA;cAAKkG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAKkG,SAAS,EAAC,4FAA4F;cAAAC,QAAA,eACzGnG,OAAA,CAACN,MAAM;gBAACwG,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAExB,IAAI,CAACW,QAAQ,IAAI;YAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/EvG,OAAA;cAAKkG,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAKkG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/CnG,OAAA,CAACX,QAAQ;gBAAC6G,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,GAAExB,IAAI,CAACgC,YAAY,IAAI,EAAE,EAAC,GAAC;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjFvG,OAAA;cAAKkG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL1B,UAAU,iBACT7E,OAAA;UAAKkG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnG,OAAA;YAAKkG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnG,OAAA;cAAMkG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDvG,OAAA;cAAMkG,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAEtB,UAAU,CAACzC,UAAU,EAAC,GAAC;YAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDnG,OAAA;cACEkG,SAAS,EAAG,gDACVrB,UAAU,CAAC1C,OAAO,KAAK,MAAM,GAAG,cAAc,GAAG,YAClD,EAAE;cACHyE,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAEhC,UAAU,CAACzC,UAAW;cAAG;YAAE;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DnG,OAAA;cAAAmG,QAAA,GAAOtB,UAAU,CAACxC,cAAc,IAAI,CAAC,EAAC,kBAAW,EAACwC,UAAU,CAACvC,YAAY,IAAI,CAAC,EAAC,QAAM;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5FvG,OAAA;cAAKkG,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBACrInG,OAAA,CAACN,MAAM;gBAACwG,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CvG,OAAA;gBAAMkG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,GAAC,GAAC,EAACZ,UAAU,CAACF,QAAQ,EAAC,KAAG;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDvG,OAAA;UACE8G,OAAO,EAAEA,CAAA,KAAMhC,OAAO,CAACH,IAAI,CAAE;UAC7BuB,SAAS,EAAC,8OAA8O;UAAAC,QAAA,gBAExPnG,OAAA,CAACb,YAAY;YAAC+G,SAAS,EAAC;UAAoD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC9E1B,UAAU,GAAG,aAAa,GAAG,YAAY;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB,CAAC;EAED,IAAIxF,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKkG,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnG,OAAA;UAAKkG,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvG,OAAA;UAAGkG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvG,OAAA;IAAKkG,SAAS,EAAC,2EAA2E;IAAAC,QAAA,gBAExFnG,OAAA;MAAKkG,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5EnG,OAAA;QAAKkG,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEnG,OAAA;UAAKkG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnG,OAAA;YAAKkG,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjDnG,OAAA;cAAKkG,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBAEvDnG,OAAA;gBAAKkG,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,eACjGnG,OAAA;kBAAKkG,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,gBAChGnG,OAAA;oBAAKkG,SAAS,EAAC;kBAA8B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDvG,OAAA;oBAAKkG,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEvG,OAAA;oBAAKkG,SAAS,EAAC;kBAAoD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1EvG,OAAA;oBAAKkG,SAAS,EAAC;kBAAmD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvG,OAAA;gBAAIkG,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBAChEnG,OAAA;kBAAMkG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCvG,OAAA;kBAAMkG,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAGLvG,OAAA;gBAAKkG,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,gBACjGnG,OAAA;kBACE+G,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBd,SAAS,EAAC,4BAA4B;kBACtCe,OAAO,EAAGzC,CAAC,IAAK;oBACdA,CAAC,CAAC0C,MAAM,CAACN,KAAK,CAACO,OAAO,GAAG,MAAM;oBAC/B3C,CAAC,CAAC0C,MAAM,CAACE,WAAW,CAACR,KAAK,CAACO,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvG,OAAA;kBAAKkG,SAAS,EAAC,8GAA8G;kBAACU,KAAK,EAAE;oBAACO,OAAO,EAAE;kBAAM,CAAE;kBAAAhB,QAAA,EAAC;gBAExJ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvG,OAAA;cAAGkG,SAAS,EAAC,6GAA6G;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/J,CAAC,eAGNvG,OAAA;YAAKkG,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBAErEnG,OAAA,CAACpB,MAAM,CAACyI,MAAM;cACZvB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BuB,QAAQ,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC1Be,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,WAAW,CAAE;cACrCiF,SAAS,EAAC,uNAAuN;cAAAC,QAAA,gBAEjOnG,OAAA,CAACP,MAAM;gBAACyG,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5CvG,OAAA;gBAAMkG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eAEhBvG,OAAA;cAAKkG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCnG,OAAA;gBAAGkG,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAEhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGvG,OAAA;gBAAGkG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,EAAC,WAAS,EAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FnG,OAAA;gBAAMkG,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAC7DhF,IAAI,aAAJA,IAAI,wBAAAhB,UAAA,GAAJgB,IAAI,CAAE+C,IAAI,cAAA/D,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYoH,MAAM,CAAC,CAAC,CAAC,cAAAnH,iBAAA,uBAArBA,iBAAA,CAAuBoH,WAAW,CAAC;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNvG,OAAA;cACE+G,GAAG,EAAC,gCAAgC;cACpCC,GAAG,EAAC,UAAU;cACdd,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvG,OAAA;MAAKkG,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAE1EnG,OAAA,CAACpB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAErCnG,OAAA;UAAKkG,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,eAClKnG,OAAA,CAACZ,OAAO;YAAC8G,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNvG,OAAA;UAAIkG,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EAAC;QAEvG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvG,OAAA;UAAGkG,SAAS,EAAC,mFAAmF;UAAAC,QAAA,GAAC,wEACzB,EAAC,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK,KAAI,aAAa;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eACJvG,OAAA;UAAKkG,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBAC9GnG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnG,OAAA;cAAKkG,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDvG,OAAA;cAAAmG,QAAA,GAAO5F,aAAa,CAACmG,MAAM,EAAC,oBAAkB;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNvG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnG,OAAA;cAAKkG,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDvG,OAAA;cAAAmG,QAAA,GAAM,SAAO,EAAC,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI,YAAY;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbvG,OAAA,CAACpB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEyB,KAAK,EAAE;QAAI,CAAE;QAC3BvB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAE3CnG,OAAA;UAAKkG,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxDnG,OAAA;YAAKkG,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDnG,OAAA;cAAKkG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BnG,OAAA;gBAAKkG,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,eAC3FnG,OAAA,CAAClB,QAAQ;kBAACoH,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNvG,OAAA;gBACE0H,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAEnH,UAAW;gBAClBoH,QAAQ,EAAGrD,CAAC,IAAK9D,aAAa,CAAC8D,CAAC,CAAC0C,MAAM,CAACU,KAAK,CAAE;gBAC/C1B,SAAS,EAAC;cAAmO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9O,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvG,OAAA;cAAKkG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BnG,OAAA;gBAAKkG,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBnG,OAAA;kBAAKkG,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3FnG,OAAA,CAACjB,QAAQ;oBAACmH,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNvG,OAAA;kBACE4H,KAAK,EAAEjH,aAAc;kBACrBkH,QAAQ,EAAGrD,CAAC,IAAK5D,gBAAgB,CAAC4D,CAAC,CAAC0C,MAAM,CAACU,KAAK,CAAE;kBAClD1B,SAAS,EAAC,2OAA2O;kBAAAC,QAAA,gBAErPnG,OAAA;oBAAQ4H,KAAK,EAAC,EAAE;oBAAAzB,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpClC,gBAAgB,CAACE,GAAG,CAAE2B,SAAS,iBAC9BlG,OAAA;oBAAwB4H,KAAK,EAAE1B,SAAU;oBAAAC,QAAA,GAAC,QAAM,EAACD,SAAS;kBAAA,GAA7CA,SAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6C,CACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbvG,OAAA,CAACpB,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEyB,KAAK,EAAE;QAAI,CAAE;QAC3BvB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAE/B5F,aAAa,CAACmG,MAAM,KAAK,CAAC,gBACzB1G,OAAA;UAAKkG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzCnG,OAAA;YAAKkG,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1EnG,OAAA,CAACX,QAAQ;cAAC6G,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7EvG,OAAA;cAAIkG,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFvG,OAAA;cAAGkG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9C1F,UAAU,IAAIE,aAAa,GACxB,+CAA+C,GAC/C;YAAwD;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENvG,OAAA;UAAKkG,SAAS,EAAC,kHAAkH;UAAAC,QAAA,EAC9H5F,aAAa,CAACgE,GAAG,CAAC,CAACI,IAAI,EAAEmD,KAAK,kBAC7B9H,OAAA,CAACpB,MAAM,CAAC6G,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEyB,KAAK,EAAEM,IAAI,CAACC,GAAG,CAACF,KAAK,GAAG,GAAG,EAAE,GAAG;YAAE,CAAE;YAClD5B,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAElBnG,OAAA,CAAC4E,QAAQ;cACPD,IAAI,EAAEA,IAAK;cACXE,UAAU,EAAEhE,WAAW,CAAC8D,IAAI,CAACrD,GAAG,CAAE;cAClCwD,OAAO,EAAEJ;YAAgB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAVG5B,IAAI,CAACrD,GAAG;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWH,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrG,EAAA,CAhdID,IAAI;EAAA,QAOSxB,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAsJ,EAAA,GATxBhI,IAAI;AAkdV,eAAeA,IAAI;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}