/* Admin Users Page Styles */

.badge-modern {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

/* Subscription Status Badges */
.subscription-badge-on-plan {
  @apply bg-green-100 text-green-800 border border-green-200;
}

.subscription-badge-expired {
  @apply bg-orange-100 text-orange-800 border border-orange-200;
}

.subscription-badge-no-plan {
  @apply bg-gray-100 text-gray-800 border border-gray-200;
}

/* User Card Enhancements */
.user-card {
  transition: all 0.3s ease;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Filter Section */
.filter-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

/* Stats Cards */
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* Subscription Details */
.subscription-details {
  background: #f8fafc;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 0.5rem;
  border-left: 3px solid #3b82f6;
}

.subscription-details.expired {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.subscription-details.no-plan {
  border-left-color: #6b7280;
  background: #f9fafb;
}

/* Filter Pills */
.filter-pill {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-card {
    padding: 1rem;
  }
  
  .filter-section {
    padding: 1rem;
  }
  
  .badge-modern {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Action Buttons */
.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: scale(1.05);
}

.action-button:active {
  transform: scale(0.95);
}

/* Search Input Enhancement */
.search-input {
  position: relative;
}

.search-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-input:focus-within::before {
  opacity: 1;
}

/* Custom Scrollbar */
.users-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.users-container::-webkit-scrollbar {
  width: 6px;
}

.users-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.users-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.users-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
