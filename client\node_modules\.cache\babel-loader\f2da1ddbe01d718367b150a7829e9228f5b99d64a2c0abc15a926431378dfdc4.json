{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbBrain, TbHome, TbRefresh, TbMedal, TbRocket, TbDiamond, TbAward, TbShield, TbUsers } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users\n  if (user) {\n    console.log('🔍 User Data Structure:', {\n      userId: user._id,\n      name: user.name,\n      username: user.username,\n      totalXP: user.totalXP,\n      currentLevel: user.currentLevel,\n      currentStreak: user.currentStreak,\n      averageScore: user.averageScore,\n      quizzesCompleted: user.quizzesCompleted,\n      totalQuizzesTaken: user.totalQuizzesTaken,\n      profilePicture: user.profilePicture,\n      allFields: Object.keys(user)\n    });\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && {\n            _t: Date.now()\n          })\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            var _event$detail;\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if ((_event$detail = event.detail) !== null && _event$detail !== void 0 && _event$detail.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user._id)) return;\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 887,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1072,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1074,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1071,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1115,\n                  columnNumber: 17\n                }, this), userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"bg-gradient-to-br from-red-600/20 via-orange-600/20 to-yellow-600/20 backdrop-blur-lg rounded-2xl p-4 border border-red-400/30 shadow-2xl max-w-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-white text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold mb-2\",\n                      children: \"Profile Debug\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1159,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm space-y-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"User exists: \", user ? '✅ YES' : '❌ NO']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 23\n                      }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"Name: \", user.name || user.username || 'No name']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1164,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"ID: \", user._id || user.id || 'No ID']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1165,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"XP: \", user.totalXP || user.xp || user.points || user.totalPoints || 'No XP']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1166,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"All fields: \", Object.keys(user).join(', ')]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1167,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1158,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1153,\n                  columnNumber: 17\n                }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-16 h-16 rounded-full overflow-hidden border-3 border-yellow-400 shadow-lg\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: user.profilePicture || user.profileImage || '/default-avatar.png',\n                          alt: user.name || user.username,\n                          className: \"w-full h-full object-cover\",\n                          onError: e => {\n                            e.target.src = '/default-avatar.png';\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1185,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1184,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1183,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-1 truncate\",\n                        children: user.name || user.username || 'User'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1198,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-yellow-300 mb-1\",\n                        children: [\"ID: \", String(user._id || user.id || '').slice(-6), \" |\", user.totalXP ? ` XP: ${user.totalXP}` : ' No XP', \" |\", currentUserRank ? ` Rank: #${currentUserRank}` : ' No Rank']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1203,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-green-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-green-300 text-xs\",\n                            children: \"Total XP\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1212,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple XP field names for migrated users\n                              const xp = user.totalXP || user.xp || user.points || user.totalPoints || 0;\n                              return xp.toLocaleString();\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1213,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1211,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-purple-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-purple-300 text-xs\",\n                            children: \"Rank\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1223,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try to find user in ranking data\n                              const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n                              return userInRanking ? `#${userInRanking.rank}` : currentUserRank ? `#${currentUserRank}` : 'N/A';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1224,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1222,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-blue-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-blue-300 text-xs\",\n                            children: \"League\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1234,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold text-xs\",\n                            children: (() => {\n                              // Find user's league with icon - try multiple XP sources\n                              const userXP = user.totalXP || user.xp || user.points || user.totalPoints || 0;\n                              for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                var _leagueData$users2;\n                                const userInLeague = (_leagueData$users2 = leagueData.users) === null || _leagueData$users2 === void 0 ? void 0 : _leagueData$users2.find(u => String(u._id) === String(user._id));\n                                if (userInLeague) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                }\n                              }\n                              // Fallback: calculate league from XP even if not in league data\n                              if (userXP > 0) {\n                                const leagueInfo = getUserLeague(userXP);\n                                return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                              }\n                              return '🔰 Unranked';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1235,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1233,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-orange-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-orange-300 text-xs\",\n                            children: \"Quizzes\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1257,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple quiz count field names\n                              return user.quizzesCompleted || user.totalQuizzesTaken || user.quizzesTaken || user.totalQuizzes || 0;\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1258,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1256,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1210,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 mt-2 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-yellow-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-yellow-300 text-xs\",\n                            children: \"Level\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1270,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: user.currentLevel || user.level || 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1271,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1269,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-red-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-red-300 text-xs\",\n                            children: \"Streak\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1277,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: user.currentStreak || user.streak || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1278,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1276,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-cyan-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-cyan-300 text-xs\",\n                            children: \"Avg Score\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1284,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: [(() => {\n                              const avgScore = user.averageScore || user.avgScore || 0;\n                              return Math.round(avgScore);\n                            })(), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1285,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1283,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1268,\n                        columnNumber: 25\n                      }, this), (() => {\n                        // Find user's position in their league\n                        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                          var _leagueData$users3;\n                          const userIndex = (_leagueData$users3 = leagueData.users) === null || _leagueData$users3 === void 0 ? void 0 : _leagueData$users3.findIndex(u => String(u._id) === String(user._id));\n                          if (userIndex !== -1 && userIndex !== undefined) {\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"mt-2 text-center\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-yellow-300 text-xs\",\n                                  children: \"League Position\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1303,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-white font-bold text-sm\",\n                                  children: [\"#\", userIndex + 1, \" of \", leagueData.users.length]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1304,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1302,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1301,\n                              columnNumber: 33\n                            }, this);\n                          }\n                        }\n                        return null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1197,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1332,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1395,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1422,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1397,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1438,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1360,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1455,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1451,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1355,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1348,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1472,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1330,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1490,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1491,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1480,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1104,\n          columnNumber: 9\n        }, this), false && ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1514,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1512,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1508,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1506,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1533,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1554,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1573,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1574,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1599,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1622,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1616,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1684,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1685,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1689,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1700,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1672,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1632,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1538,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1537,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1532,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1525,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1725,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1730,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1720,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${isCurrentUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1805,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1819,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1822,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1844,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1850,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1834,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1833,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1865,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1872,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1877,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1880,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1876,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1816,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1809,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1766,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${isCurrentUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '320px',\n                    transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1932,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1931,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1941,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1936,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1958,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1961,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1984,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1990,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1974,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2010,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2003,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1973,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2017,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2028,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2016,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2057,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2043,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2062,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2073,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2074,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2072,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2078,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2071,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2082,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2083,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2081,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2087,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2080,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2070,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2061,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1952,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1945,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1891,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${isCurrentUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2137,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2136,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2151,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2154,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2176,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2182,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2166,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2165,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2197,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2204,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2209,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2212,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2208,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2148,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2141,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2098,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1763,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1746,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2244,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2258,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2261,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2243,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                        filter: isCurrentUser ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: isCurrentUser ? '4px solid #FFD700' : 'none',\n                        borderRadius: isCurrentUser ? '16px' : '0px',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2320,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2326,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2325,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full w-full h-full\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2351,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2357,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2341,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2378,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2371,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2340,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2323,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2389,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2400,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2388,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2418,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2386,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2385,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2427,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2447,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2448,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2440,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2457,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2458,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2450,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2439,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2425,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2313,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2307,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2279,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2273,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2236,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2483,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2497,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2482,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2523,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2534,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2544,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2533,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2522,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2549,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2521,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          \"data-user-id\": champion._id,\n                          \"data-user-rank\": leagueRank,\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2589,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2592,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                                  style: {\n                                    background: '#f0f0f0',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    width: '40px',\n                                    height: '40px'\n                                  },\n                                  children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: champion.profilePicture,\n                                    alt: champion.name,\n                                    className: \"object-cover rounded-full w-full h-full\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2619,\n                                    columnNumber: 47\n                                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                    style: {\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    },\n                                    children: champion.name.charAt(0).toUpperCase()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2625,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2609,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2645,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2638,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2604,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2657,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2651,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2661,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2666,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2669,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2665,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2586,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2580,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2566,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2560,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2683,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2682,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2510,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2503,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2474,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2708,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2715,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2718,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2714,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2721,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2724,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2720,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2727,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2730,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2726,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2713,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2733,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2707,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2701,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2749,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2754,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2759,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2748,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2742,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2782,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2778,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2784,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2789,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2797,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2777,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2771,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2815,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2816,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2821,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2810,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1742,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1736,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1102,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1069,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"UH7MdPhjf7aKdBvLF5Dzg6Z9jFc=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbRocket", "TbDiamond", "TbAward", "TbShield", "TbUsers", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "reduxUser", "user", "localStorageUser", "userData", "localStorage", "getItem", "JSON", "parse", "tokenUser", "token", "payload", "atob", "split", "console", "log", "redux", "final", "userId", "_id", "name", "username", "totalXP", "currentLevel", "currentStreak", "averageScore", "quizzesCompleted", "totalQuizzesTaken", "profilePicture", "allFields", "Object", "keys", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "push", "tier", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "forceRefresh", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "_t", "Date", "now", "success", "data", "filteredData", "transformedData", "map", "index", "email", "class", "profileImage", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "reports", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "userIdType", "isAdmin", "role", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "removeItem", "refreshWithRetry", "attempts", "i", "_event$detail", "newTotalXP", "updatedUser", "Promise", "resolve", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "userInLeague", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "position", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "scrollToUser", "podiumSection", "userInCurrentView", "userElement", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "y", "x", "delay", "left", "top", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "points", "join", "src", "alt", "onError", "e", "target", "toLocaleString", "userInRanking", "_leagueData$users2", "leagueInfo", "toUpperCase", "quizzesTaken", "streak", "avgScore", "_leagueData$users3", "undefined", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_leagueGroups$leagueK2", "isSelected", "userCount", "border", "zIndex", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "stat", "ref", "height", "borderRadius", "width", "char<PERSON>t", "createElement", "h2", "champion", "actualRank", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbRocket,\n  TbDiamond,\n  TbAward,\n  TbShield,\n  TbUsers\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users\n  if (user) {\n    console.log('🔍 User Data Structure:', {\n      userId: user._id,\n      name: user.name,\n      username: user.username,\n      totalXP: user.totalXP,\n      currentLevel: user.currentLevel,\n      currentStreak: user.currentStreak,\n      averageScore: user.averageScore,\n      quizzesCompleted: user.quizzesCompleted,\n      totalQuizzesTaken: user.totalQuizzesTaken,\n      profilePicture: user.profilePicture,\n      allFields: Object.keys(user)\n    });\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && { _t: Date.now() })\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if (event.detail?.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!user?._id) return;\n\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\">\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n                {/* User Profile Window - Always Show for Testing */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"bg-gradient-to-br from-red-600/20 via-orange-600/20 to-yellow-600/20 backdrop-blur-lg rounded-2xl p-4 border border-red-400/30 shadow-2xl max-w-sm\"\n                >\n                  <div className=\"text-white text-center\">\n                    <h3 className=\"text-lg font-bold mb-2\">Profile Debug</h3>\n                    <div className=\"text-sm space-y-1\">\n                      <div>User exists: {user ? '✅ YES' : '❌ NO'}</div>\n                      {user && (\n                        <>\n                          <div>Name: {user.name || user.username || 'No name'}</div>\n                          <div>ID: {user._id || user.id || 'No ID'}</div>\n                          <div>XP: {user.totalXP || user.xp || user.points || user.totalPoints || 'No XP'}</div>\n                          <div>All fields: {Object.keys(user).join(', ')}</div>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </motion.div>\n\n                {/* Original User Profile Window */}\n                {user && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\"\n                  >\n                    <div className=\"flex items-center gap-4\">\n                      {/* Profile Picture */}\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-16 h-16 rounded-full overflow-hidden border-3 border-yellow-400 shadow-lg\">\n                          <img\n                            src={user.profilePicture || user.profileImage || '/default-avatar.png'}\n                            alt={user.name || user.username}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              e.target.src = '/default-avatar.png';\n                            }}\n                          />\n                        </div>\n                      </div>\n\n                      {/* User Details */}\n                      <div className=\"flex-grow\">\n                        <h3 className=\"text-lg font-bold text-white mb-1 truncate\">\n                          {user.name || user.username || 'User'}\n                        </h3>\n\n                        {/* Debug Info for Migrated Users */}\n                        <div className=\"text-xs text-yellow-300 mb-1\">\n                          ID: {String(user._id || user.id || '').slice(-6)} |\n                          {user.totalXP ? ` XP: ${user.totalXP}` : ' No XP'} |\n                          {currentUserRank ? ` Rank: #${currentUserRank}` : ' No Rank'}\n                        </div>\n\n                        {/* Stats Grid */}\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                          <div className=\"bg-green-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-green-300 text-xs\">Total XP</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple XP field names for migrated users\n                                const xp = user.totalXP || user.xp || user.points || user.totalPoints || 0;\n                                return xp.toLocaleString();\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-purple-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-purple-300 text-xs\">Rank</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try to find user in ranking data\n                                const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n                                return userInRanking ? `#${userInRanking.rank}` : (currentUserRank ? `#${currentUserRank}` : 'N/A');\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-blue-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-blue-300 text-xs\">League</div>\n                            <div className=\"text-white font-bold text-xs\">\n                              {(() => {\n                                // Find user's league with icon - try multiple XP sources\n                                const userXP = user.totalXP || user.xp || user.points || user.totalPoints || 0;\n                                for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                  const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n                                  if (userInLeague) {\n                                    const leagueInfo = getUserLeague(userXP);\n                                    return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                  }\n                                }\n                                // Fallback: calculate league from XP even if not in league data\n                                if (userXP > 0) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                                }\n                                return '🔰 Unranked';\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-orange-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-orange-300 text-xs\">Quizzes</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple quiz count field names\n                                return user.quizzesCompleted || user.totalQuizzesTaken || user.quizzesTaken || user.totalQuizzes || 0;\n                              })()}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Additional Stats Row */}\n                        <div className=\"grid grid-cols-3 gap-2 mt-2 text-xs\">\n                          <div className=\"bg-yellow-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-yellow-300 text-xs\">Level</div>\n                            <div className=\"text-white font-bold\">\n                              {user.currentLevel || user.level || 1}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-red-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-red-300 text-xs\">Streak</div>\n                            <div className=\"text-white font-bold\">\n                              {user.currentStreak || user.streak || 0}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-cyan-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-cyan-300 text-xs\">Avg Score</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                const avgScore = user.averageScore || user.avgScore || 0;\n                                return Math.round(avgScore);\n                              })()}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* League Position */}\n                        {(() => {\n                          // Find user's position in their league\n                          for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                            const userIndex = leagueData.users?.findIndex(u => String(u._id) === String(user._id));\n                            if (userIndex !== -1 && userIndex !== undefined) {\n                              return (\n                                <div className=\"mt-2 text-center\">\n                                  <div className=\"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\">\n                                    <div className=\"text-yellow-300 text-xs\">League Position</div>\n                                    <div className=\"text-white font-bold text-sm\">\n                                      #{userIndex + 1} of {leagueData.users.length}\n                                    </div>\n                                  </div>\n                                </div>\n                              );\n                            }\n                          }\n                          return null;\n                        })()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n\n\n\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice - DISABLED FOR TESTING */}\n        {false && (user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          isCurrentUser(topPerformers[1]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[1]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          isCurrentUser(topPerformers[0]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '320px',\n                          transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[0]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          isCurrentUser(topPerformers[2]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[2]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n\n\n\n\n\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                isCurrentUser\n                                  ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                  : ''\n                              }`}\n                              style={{\n                                transform: isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                                filter: isCurrentUser\n                                  ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                  : 'none',\n                                transition: 'all 0.3s ease',\n                                border: isCurrentUser ? '4px solid #FFD700' : 'none',\n                                borderRadius: isCurrentUser ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                        style={{\n                                          background: '#f0f0f0',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      >\n                                        {champion.profilePicture ? (\n                                          <img\n                                            src={champion.profilePicture}\n                                            alt={champion.name}\n                                            className=\"object-cover rounded-full w-full h-full\"\n                                          />\n                                        ) : (\n                                          <div\n                                            className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                            style={{\n                                              background: '#25D366',\n                                              color: '#FFFFFF',\n                                              fontSize: '12px'\n                                            }}\n                                          >\n                                            {champion.name.charAt(0).toUpperCase()}\n                                          </div>\n                                        )}\n                                      </div>\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    data-user-id={champion._id}\n                                    data-user-rank={leagueRank}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      isCurrentUser\n                                        ? 'ring-2 ring-yellow-400/60'\n                                        : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser\n                                            ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                            : ''\n                                        }`}>\n                                          <div\n                                            className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                            style={{\n                                              background: '#f0f0f0',\n                                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          >\n                                            {champion.profilePicture ? (\n                                              <img\n                                                src={champion.profilePicture}\n                                                alt={champion.name}\n                                                className=\"object-cover rounded-full w-full h-full\"\n                                              />\n                                            ) : (\n                                              <div\n                                                className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                                style={{\n                                                  background: '#25D366',\n                                                  color: '#FFFFFF',\n                                                  fontSize: '16px'\n                                                }}\n                                              >\n                                                {champion.name.charAt(0).toUpperCase()}\n                                              </div>\n                                            )}\n                                          </div>\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,SAAS,GAAGH,SAAS,CAACI,IAAI,IAAI,IAAI;;EAExC;EACA,MAAMC,gBAAgB,GAAG,CAAC,MAAM;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;EAEJ,MAAMK,SAAS,GAAG,CAAC,MAAM;IACvB,IAAI;MACF,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAII,KAAK,EAAE;QACT,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACI,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,OAAOF,OAAO;MAChB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMT,IAAI,GAAGD,SAAS,IAAIE,gBAAgB,IAAIM,SAAS;;EAEvD;EACAK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;IACnCC,KAAK,EAAEf,SAAS;IAChBI,YAAY,EAAEF,gBAAgB;IAC9BO,KAAK,EAAED,SAAS;IAChBQ,KAAK,EAAEf;EACT,CAAC,CAAC;;EAEF;EACA,IAAIA,IAAI,EAAE;IACRY,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrCG,MAAM,EAAEhB,IAAI,CAACiB,GAAG;MAChBC,IAAI,EAAElB,IAAI,CAACkB,IAAI;MACfC,QAAQ,EAAEnB,IAAI,CAACmB,QAAQ;MACvBC,OAAO,EAAEpB,IAAI,CAACoB,OAAO;MACrBC,YAAY,EAAErB,IAAI,CAACqB,YAAY;MAC/BC,aAAa,EAAEtB,IAAI,CAACsB,aAAa;MACjCC,YAAY,EAAEvB,IAAI,CAACuB,YAAY;MAC/BC,gBAAgB,EAAExB,IAAI,CAACwB,gBAAgB;MACvCC,iBAAiB,EAAEzB,IAAI,CAACyB,iBAAiB;MACzCC,cAAc,EAAE1B,IAAI,CAAC0B,cAAc;MACnCC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAAC7B,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,MAAM8B,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwE,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoF,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM0F,UAAU,GAAGxF,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMyF,SAAS,GAAGzF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM0F,cAAc,GAAG1F,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2F,aAAa,GAAG3F,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM4F,WAAW,GAAG5F,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM6F,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhG,OAAO;MACbiG,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExF,SAAS;MACfyF,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEtF,QAAQ;MACduF,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEvF,OAAO;MACbwF,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEjG,QAAQ;MACdkG,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE1F,OAAO;MACb2F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/F,MAAM;MACZgG,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEzF,QAAQ;MACd0F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAI7D,MAAM,CAAC8D,OAAO,CAAC7B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAI7F,KAAK,IAAK;IACpC,MAAM8F,OAAO,GAAG,CAAC,CAAC;IAElB9F,KAAK,CAAC+F,OAAO,CAAC7F,IAAI,IAAI;MACpB,MAAM8F,UAAU,GAAGR,aAAa,CAACtF,IAAI,CAACoB,OAAO,CAAC;MAC9C,IAAI,CAACwE,OAAO,CAACE,UAAU,CAACN,MAAM,CAAC,EAAE;QAC/BI,OAAO,CAACE,UAAU,CAACN,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEK,UAAU;UAClBhG,KAAK,EAAE;QACT,CAAC;MACH;MACA8F,OAAO,CAACE,UAAU,CAACN,MAAM,CAAC,CAAC1F,KAAK,CAACiG,IAAI,CAAC;QACpC,GAAG/F,IAAI;QACPgG,IAAI,EAAEF,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAlE,MAAM,CAACC,IAAI,CAAC+D,OAAO,CAAC,CAACC,OAAO,CAACI,SAAS,IAAI;MACxCL,OAAO,CAACK,SAAS,CAAC,CAACnG,KAAK,CAACoG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChF,OAAO,GAAG+E,CAAC,CAAC/E,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOwE,OAAO;EAChB,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMT,UAAU,GAAGR,aAAa,CAACiB,WAAW,CAACnF,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAM2B,WAAW,GAAGuD,QAAQ,CAACE,MAAM,CAACxG,IAAI,IAAI;MAC1C,MAAMwF,MAAM,GAAGF,aAAa,CAACtF,IAAI,CAACoB,OAAO,CAAC;MAC1C,OAAOoE,MAAM,CAACA,MAAM,KAAKM,UAAU,CAACN,MAAM;IAC5C,CAAC,CAAC,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChF,OAAO,GAAG+E,CAAC,CAAC/E,OAAO,CAAC;IAExC,OAAO;MACLoE,MAAM,EAAEM,UAAU;MAClBhG,KAAK,EAAEiD,WAAW;MAClB0D,QAAQ,EAAE1D,WAAW,CAAC2D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1F,GAAG,KAAKsF,WAAW,CAACtF,GAAG,CAAC,GAAG,CAAC;MACnE2F,aAAa,EAAE7D,WAAW,CAAC8D;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIb,SAAS,IAAK;IAAA,IAAAc,qBAAA;IACxCnG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoF,SAAS,CAAC;;IAE7C;IACA7C,iBAAiB,CAAC6C,SAAS,CAAC;IAC5B/C,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAA+D,qBAAA,GAAA1D,YAAY,CAAC4C,SAAS,CAAC,cAAAc,qBAAA,uBAAvBA,qBAAA,CAAyBjH,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACAkH,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBlB,SAAU,IAAG,CAAC,IACvDiB,QAAQ,CAACE,cAAc,CAAE,UAASnB,SAAU,EAAC,CAAC,IAC9C1C,UAAU,CAAC8D,OAAO,CAACpB,SAAS,CAAC;MAElD,IAAIgB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACvB,MAAM,CAAChB,MAAM,IAAInC,YAAY,CAACmC,MAAM,CAAC,IAAInC,YAAY,CAACmC,MAAM,CAAC,CAAC1F,KAAK,CAAC+G,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAID;EACA,MAAMmB,gBAAgB,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACvD,IAAI;MACF/F,UAAU,CAAC,IAAI,CAAC;MAChBtB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEoH,YAAY,GAAG,iBAAiB,GAAG,EAAE,CAAC;;MAE7F;MACA,IAAI;QACFrH,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMqH,qBAAqB,GAAG,MAAM/I,gBAAgB,CAAC;UACnDgJ,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAApI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqI,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE,KAAK;UACtB;UACA,IAAIL,YAAY,IAAI;YAAEM,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;UAAE,CAAC;QACxC,CAAC,CAAC;QAEF7H,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqH,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACQ,OAAO,IAAIR,qBAAqB,CAACS,IAAI,EAAE;UACxF/H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAM+H,YAAY,GAAGV,qBAAqB,CAACS,IAAI,CAACnC,MAAM,CAACtG,QAAQ,IAC5DA,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACkB,OAAO,GAAG,CAAC,IACxClB,QAAQ,CAACuB,iBAAiB,IAAIvB,QAAQ,CAACuB,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMoH,eAAe,GAAGD,YAAY,CAACE,GAAG,CAAC,CAAC5I,QAAQ,EAAE6I,KAAK,MAAM;YAC7D9H,GAAG,EAAEf,QAAQ,CAACe,GAAG;YACjBC,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,IAAI,oBAAoB;YAC3C8H,KAAK,EAAE9I,QAAQ,CAAC8I,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE/I,QAAQ,CAAC+I,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEnI,QAAQ,CAACmI,KAAK,IAAI,EAAE;YAC3B3G,cAAc,EAAExB,QAAQ,CAACgJ,YAAY,IAAI,EAAE;YAC3C9H,OAAO,EAAElB,QAAQ,CAACkB,OAAO,IAAI,CAAC;YAC9BK,iBAAiB,EAAEvB,QAAQ,CAACuB,iBAAiB,IAAI,CAAC;YAClDF,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,CAAC;YACxCD,aAAa,EAAEpB,QAAQ,CAACoB,aAAa,IAAI,CAAC;YAC1C6H,UAAU,EAAEjJ,QAAQ,CAACiJ,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAElJ,QAAQ,CAACkJ,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEN,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEV,aAAa,CAACpF,QAAQ,CAACkB,OAAO,IAAI,CAAC,CAAC;YAC1CkI,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAErJ,QAAQ,CAACqJ,YAAY,IAAI,CAAC;YACxC;YACAlI,YAAY,EAAEnB,QAAQ,CAACmB,YAAY,IAAI,CAAC;YACxCmI,aAAa,EAAEtJ,QAAQ,CAACsJ,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEvJ,QAAQ,CAACuJ,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAExJ,QAAQ,CAACwJ,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAEzJ,QAAQ,CAACyJ,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEH5H,cAAc,CAAC6G,eAAe,CAAC;;UAE/B;UACA,MAAMgB,aAAa,GAAGhB,eAAe,CAACnC,SAAS,CAACoD,IAAI,IAAIA,IAAI,CAAC7I,GAAG,MAAKjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG,EAAC;UAC/EmB,kBAAkB,CAACyH,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAI7J,IAAI,EAAE;YACR,MAAM+J,cAAc,GAAG1D,wBAAwB,CAACwC,eAAe,EAAE7I,IAAI,CAAC;YACtE8C,oBAAoB,CAACiH,cAAc,CAAC;YACpC/G,cAAc,CAAC,CAAA+G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjK,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAMkK,OAAO,GAAGrE,kBAAkB,CAACkD,eAAe,CAAC;UACnDvF,eAAe,CAAC0G,OAAO,CAAC;UAExB9H,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO+H,OAAO,EAAE;QAChBrJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoJ,OAAO,CAAC;MACpE;;MAEA;MACArJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqJ,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqJ,eAAe,GAAG,MAAMhL,uBAAuB,CAAC,CAAC;QACjD0B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsJ,aAAa,GAAG,MAAM9K,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAO+K,KAAK,EAAE;QACdxJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuJ,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAM9K,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOgL,SAAS,EAAE;UAClBzJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwJ,SAAS,CAAC;QACpD;MACF;MAEA,IAAIxB,eAAe,GAAG,EAAE;MAExB,IAAIsB,aAAa,IAAIA,aAAa,CAACzB,OAAO,IAAIyB,aAAa,CAACxB,IAAI,EAAE;QAChE/H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyJ,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAACxB,OAAO,IAAIwB,eAAe,CAACvB,IAAI,EAAE;UACtEuB,eAAe,CAACvB,IAAI,CAAC9C,OAAO,CAACiE,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMvJ,MAAM,GAAG,EAAAuJ,UAAA,GAAAT,IAAI,CAAC9J,IAAI,cAAAuK,UAAA,uBAATA,UAAA,CAAWtJ,GAAG,KAAI6I,IAAI,CAAC9I,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVsJ,cAAc,CAACtJ,MAAM,CAAC,GAAG8I,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEA3B,eAAe,GAAGsB,aAAa,CAACxB,IAAI,CACjCnC,MAAM,CAACtG,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACe,GAAG,CAAC,CAAC;QAAA,CAC7C6H,GAAG,CAAC,CAAC5I,QAAQ,EAAE6I,KAAK,KAAK;UACxB;UACA,MAAM0B,WAAW,GAAGH,cAAc,CAACpK,QAAQ,CAACe,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIyJ,YAAY,GAAGD,WAAW,CAAC5D,MAAM,IAAI3G,QAAQ,CAACuB,iBAAiB,IAAI,CAAC;UACxE,IAAIkJ,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIxJ,YAAY,GAAGmJ,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAGxK,QAAQ,CAACqB,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAACkJ,WAAW,CAAC5D,MAAM,IAAI3G,QAAQ,CAACgL,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACnL,QAAQ,CAACgL,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAACjH,GAAG,CAAC,EAAE,EAAEiH,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIlL,QAAQ,CAACgL,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/B5J,YAAY,GAAGyJ,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAAC1J,YAAY,GAAGmJ,YAAY,CAAC;YAEpD9J,OAAO,CAACC,GAAG,CAAE,0BAAyBX,QAAQ,CAACgB,IAAK,KAAIiK,gBAAiB,aAAYG,gBAAiB,cAAapL,QAAQ,CAACgL,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI9J,OAAO,GAAGlB,QAAQ,CAACkB,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIlB,QAAQ,CAACgL,WAAW,EAAE;cACxB;cACA9J,OAAO,GAAG4J,IAAI,CAACK,KAAK,CAClBnL,QAAQ,CAACgL,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrBnJ,YAAY,GAAG,EAAE,GAAGmJ,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7CnJ,YAAY,GAAG,EAAE,GAAGmJ,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAtJ,OAAO,GAAG4J,IAAI,CAACK,KAAK,CACjB9J,YAAY,GAAGmJ,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrBnJ,YAAY,GAAG,EAAE,GAAGmJ,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAIpJ,aAAa,GAAGpB,QAAQ,CAACoB,aAAa,IAAI,CAAC;UAC/C,IAAI6H,UAAU,GAAGjJ,QAAQ,CAACiJ,UAAU,IAAI,CAAC;UAEzC,IAAIsB,WAAW,CAAC5D,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI0E,UAAU,GAAG,CAAC;YAClBd,WAAW,CAAC5E,OAAO,CAACiF,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZpC,UAAU,GAAG6B,IAAI,CAACI,GAAG,CAACjC,UAAU,EAAEoC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFjK,aAAa,GAAGiK,UAAU;UAC5B,CAAC,MAAM,IAAIrL,QAAQ,CAACgL,WAAW,IAAI,CAAC5J,aAAa,EAAE;YACjD;YACA,MAAMkK,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAGxK,QAAQ,CAACgL,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBlK,aAAa,GAAG0J,IAAI,CAACjH,GAAG,CAAC2G,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxErC,UAAU,GAAG6B,IAAI,CAACI,GAAG,CAAC9J,aAAa,EAAE0J,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLvK,GAAG,EAAEf,QAAQ,CAACe,GAAG;YACjBC,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,IAAI,oBAAoB;YAC3C8H,KAAK,EAAE9I,QAAQ,CAAC8I,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE/I,QAAQ,CAAC+I,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEnI,QAAQ,CAACmI,KAAK,IAAI,EAAE;YAC3B3G,cAAc,EAAExB,QAAQ,CAACwB,cAAc,IAAI,EAAE;YAC7CN,OAAO,EAAEA,OAAO;YAChBK,iBAAiB,EAAEiJ,YAAY;YAC/BnJ,YAAY,EAAEA,YAAY;YAC1BD,aAAa,EAAEA,aAAa;YAC5B6H,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAElJ,QAAQ,CAACkJ,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEN,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEV,aAAa,CAAClE,OAAO,CAAC;YAC5BkI,UAAU,EAAE,IAAI;YAChB;YACAmC,cAAc,EAAEvL,QAAQ,CAACgL,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAAC5D,MAAM,GAAG,CAAC;YAClC+C,UAAU,EAAEa,WAAW,CAAC5D,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG3G,QAAQ,CAACgL,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACArC,eAAe,CAAC3C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChF,OAAO,GAAG+E,CAAC,CAAC/E,OAAO,CAAC;;QAErD;QACAyH,eAAe,CAAChD,OAAO,CAAC,CAAC7F,IAAI,EAAE+I,KAAK,KAAK;UACvC/I,IAAI,CAACqJ,IAAI,GAAGN,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEF/G,cAAc,CAAC6G,eAAe,CAAC;;QAE/B;QACA,IAAIpC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIzG,IAAI,EAAE;UACR;UACAyG,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACoD,IAAI,IAAIA,IAAI,CAAC7I,GAAG,KAAKjB,IAAI,CAACiB,GAAG,CAAC;;UAEnE;UACA,IAAIwF,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACoD,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAAC7I,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIwF,QAAQ,KAAK,CAAC,CAAC,IAAIzG,IAAI,CAACkB,IAAI,EAAE;YAChCuF,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACoD,IAAI,IAAIA,IAAI,CAAC5I,IAAI,KAAKlB,IAAI,CAACkB,IAAI,CAAC;UACvE;QACF;QAEAkB,kBAAkB,CAACqE,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIzG,IAAI,EAAE;UACR,MAAM+J,cAAc,GAAG1D,wBAAwB,CAACwC,eAAe,EAAE7I,IAAI,CAAC;UACtE8C,oBAAoB,CAACiH,cAAc,CAAC;UACpC/G,cAAc,CAAC,CAAA+G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjK,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAMkK,OAAO,GAAGrE,kBAAkB,CAACkD,eAAe,CAAC;QACnDvF,eAAe,CAAC0G,OAAO,CAAC;;QAExB;QACApJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C0F,WAAW,EAAEvG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI;UACvBF,MAAM,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG;UACjB2K,UAAU,EAAE,QAAO5L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG;UAC5B4K,OAAO,EAAE,CAAA7L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8L,IAAI,MAAK,OAAO,KAAI9L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6L,OAAO;UAChDE,MAAM,EAAE/L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,OAAO;UACrByI,aAAa,EAAEpD,QAAQ;UACvBuF,gBAAgB,EAAEvF,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrDwF,gBAAgB,EAAEpD,eAAe,CAAChC,MAAM;UACxCqF,eAAe,EAAErD,eAAe,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrD,GAAG,CAACnC,CAAC,KAAK;YAAEyF,EAAE,EAAEzF,CAAC,CAAC1F,GAAG;YAAEoL,IAAI,EAAE,OAAO1F,CAAC,CAAC1F,GAAG;YAAEC,IAAI,EAAEyF,CAAC,CAACzF;UAAK,CAAC,CAAC,CAAC;UACxGoL,UAAU,EAAEzD,eAAe,CAAC0D,IAAI,CAACzC,IAAI,IAAIA,IAAI,CAAC7I,GAAG,MAAKjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG,EAAC;UAChEuL,WAAW,EAAE3D,eAAe,CAAC0D,IAAI,CAACzC,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAAC7I,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG,CAAC,CAAC;UACjFwL,SAAS,EAAE5D,eAAe,CAAC0D,IAAI,CAACzC,IAAI,IAAIA,IAAI,CAAC5I,IAAI,MAAKlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMwL,WAAW,GAAG;UAClBlC,OAAO,EAAE3B,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,SAAS,CAAC,CAAC/C,MAAM;UACvE8F,aAAa,EAAE9D,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,eAAe,CAAC,CAAC/C,MAAM;UACnF+F,SAAS,EAAE/D,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,WAAW,CAAC,CAAC/C;QACvE,CAAC;QAEDjG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEgI,eAAe,CAAChC,MAAM,EAAE,gBAAgB,CAAC;QACxFjG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6L,WAAW,CAAC;QAC5C9L,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgI,eAAe,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrD,GAAG,CAACnC,CAAC,KAAK;UACvEzF,IAAI,EAAEyF,CAAC,CAACzF,IAAI;UACZqE,EAAE,EAAEoB,CAAC,CAACvF,OAAO;UACbyL,OAAO,EAAElG,CAAC,CAAClF,iBAAiB;UAC5BqL,GAAG,EAAEnG,CAAC,CAACpF,YAAY;UACnBwL,MAAM,EAAEpG,CAAC,CAACiD;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLhJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCmB,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBhE,OAAO,CAAC4O,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdxJ,OAAO,CAACwJ,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhM,OAAO,CAACgM,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACApE,SAAS,CAAC,MAAM;IACdkK,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMiF,WAAW,GAAGrJ,kBAAkB,CAACoH,IAAI,CAACK,KAAK,CAACL,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAGtJ,kBAAkB,CAACiD,MAAM,CAAC,CAAC;IAC7FjE,oBAAoB,CAACqK,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvC1K,iBAAiB,CAAC2K,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B1M,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DmH,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMuF,mBAAmB,GAAIC,KAAK,IAAK;MACrC5M,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE2M,KAAK,CAACC,MAAM,CAAC;;MAEnE;MACAtN,YAAY,CAACuN,UAAU,CAAC,cAAc,CAAC;MACvCvN,YAAY,CAACuN,UAAU,CAAC,qBAAqB,CAAC;MAC9CvN,YAAY,CAACuN,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,GAAG,CAAC,KAAK;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UACjC,IAAI;YAAA,IAAAC,aAAA;YACFlN,OAAO,CAACC,GAAG,CAAE,uCAAsCgN,CAAC,GAAG,CAAE,IAAGD,QAAS,GAAE,CAAC;YACxE,MAAM5F,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;YAE9B;YACA,IAAI,CAAA8F,aAAA,GAAAN,KAAK,CAACC,MAAM,cAAAK,aAAA,eAAZA,aAAA,CAAcC,UAAU,IAAI/N,IAAI,EAAE;cACpC,MAAMgO,WAAW,GAAGjM,WAAW,CAACwK,IAAI,CAAC5F,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;cAC7E,IAAI+M,WAAW,IAAIA,WAAW,CAAC5M,OAAO,IAAIoM,KAAK,CAACC,MAAM,CAACM,UAAU,EAAE;gBACjEnN,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD;cACF;YACF;;YAEA;YACA,IAAIgN,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIlH,UAAU,CAACkH,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;YACdxJ,OAAO,CAACwJ,KAAK,CAAE,6BAA4ByD,CAAC,GAAG,CAAE,UAAS,EAAEzD,KAAK,CAAC;YAClE,IAAIyD,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIlH,UAAU,CAACkH,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF;QACF;MACF,CAAC;;MAED;MACAlH,UAAU,CAAC,MAAM;QACf2G,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDQ,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEd,iBAAiB,CAAC;IACnDa,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEb,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXc,aAAa,CAAClB,cAAc,CAAC;MAC7B;MACAgB,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEhB,iBAAiB,CAAC;MACtDa,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEf,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzP,SAAS,CAAC,MAAM;IACd,IAAIkC,IAAI,IAAIqD,YAAY,IAAIzB,MAAM,CAACC,IAAI,CAACwB,YAAY,CAAC,CAACwD,MAAM,GAAG,CAAC,IAAI,CAAC1D,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAAC8C,SAAS,EAAEsI,UAAU,CAAC,IAAI3M,MAAM,CAAC8D,OAAO,CAACrC,YAAY,CAAC,EAAE;QAClE,MAAMmL,YAAY,GAAGD,UAAU,CAACzO,KAAK,CAACyM,IAAI,CAAC5F,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;QACnF,IAAIuN,YAAY,EAAE;UAChB5N,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoF,SAAS,CAAC;UACxD7C,iBAAiB,CAAC6C,SAAS,CAAC;UAC5B/C,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAACuL,UAAU,CAACzO,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACE,IAAI,EAAEqD,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAMsL,aAAa,GAAG1M,WAAW,CAACoK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMuC,eAAe,GAAG3M,WAAW,CAACoK,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMwC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAAC3O,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiB,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAM2N,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAInD,MAAM,CAACmD,SAAS,CAAC7N,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;IAC9F,IAAI2N,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAC/H,SAAS,CAACoI,SAAS,IAAInD,MAAM,CAACmD,SAAS,CAAC7N,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACLoL,IAAI,EAAE,QAAQ;QACd2C,QAAQ,EAAED,cAAc;QACxBvJ,MAAM,EAAE,iBAAiB;QACzBS,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEsI,UAAU,CAAC,IAAI3M,MAAM,CAAC8D,OAAO,CAACrC,YAAY,CAAC,EAAE;MAAA,IAAA4L,iBAAA;MAClE,MAAMT,YAAY,IAAAS,iBAAA,GAAGV,UAAU,CAACzO,KAAK,cAAAmP,iBAAA,uBAAhBA,iBAAA,CAAkB1C,IAAI,CAAC5F,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;MACpF,IAAIuN,YAAY,EAAE;QAChB,MAAMQ,QAAQ,GAAGT,UAAU,CAACzO,KAAK,CAAC4G,SAAS,CAACC,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACLoL,IAAI,EAAE,QAAQ;UACd2C,QAAQ,EAAEA,QAAQ;UAClBxJ,MAAM,EAAE+I,UAAU,CAAChK,KAAK;UACxB0B,SAAS,EAAEA,SAAS;UACpBiJ,UAAU,EAAEX,UAAU,CAACzO,KAAK,CAAC+G;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMsI,cAAc,GAAGR,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMS,aAAa,GAAIpO,MAAM,IAAK;IAChC,OAAOhB,IAAI,IAAI2L,MAAM,CAAC3K,MAAM,CAAC,KAAK2K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC;EACpD,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd,IAAI,EAACkC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiB,GAAG,GAAE;IAEhB,MAAMoO,YAAY,GAAGA,CAAA,KAAM;MACzB;MACA,MAAMT,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAInD,MAAM,CAACmD,SAAS,CAAC7N,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;MAE9F,IAAI2N,UAAU,EAAE;QACd;QACA,MAAMU,aAAa,GAAGpI,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvE,IAAImI,aAAa,EAAE;UACjBtI,UAAU,CAAC,MAAM;YACfsI,aAAa,CAAChI,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM,IAAI1E,WAAW,CAAC8D,MAAM,GAAG,CAAC,EAAE;QACjC;QACA,MAAM0I,iBAAiB,GAAGxM,WAAW,CAAC8L,IAAI,CAAClI,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;QACnF,IAAIsO,iBAAiB,EAAE;UACrB;UACA,MAAMC,WAAW,GAAGtI,QAAQ,CAACC,aAAa,CAAE,kBAAiBnH,IAAI,CAACiB,GAAI,IAAG,CAAC;UAC1E,IAAIuO,WAAW,EAAE;YACfxI,UAAU,CAAC,MAAM;cACfwI,WAAW,CAAClI,cAAc,CAAC;gBACzBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE,QAAQ;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMgI,KAAK,GAAGzI,UAAU,CAACqI,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMK,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACzP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG,EAAEwN,aAAa,EAAE1L,WAAW,CAAC,CAAC;;EAE3C;EACA,MAAM4M,oBAAoB,GAAGA,CAACvG,kBAAkB,EAAEwG,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMtH,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMwH,OAAO,GAAGJ,mBAAmB,GAAG,IAAIpH,IAAI,CAACoH,mBAAmB,CAAC,GAAG,IAAI;IAE1EhP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCuI,kBAAkB;MAClBwG,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfE,OAAO;MACPvH,GAAG;MACHwH,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGvH,GAAG;MAClCsH;IACF,CAAC,CAAC;;IAEF;IACA,IAAI3G,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC4G,OAAO,IAAIA,OAAO,GAAGvH,GAAG,EAAE;QAC7B;QACA,OAAO;UACLyH,IAAI,EAAE,WAAW;UACjBlM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACLyL,IAAI,EAAE,SAAS;UACflM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACLyL,IAAI,EAAE,SAAS;QACflM,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAIxC,OAAO,IAAIF,WAAW,CAAC8E,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEtH,OAAA;MAAK4Q,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzH7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvB7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzB7I,UAAU,EAAE;YAAE8I,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF1R,OAAA;UAAG4Q,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE1R,OAAA,CAAAE,SAAA;IAAA2Q,QAAA,gBACE7Q,OAAA;MAAA6Q,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACV1R,OAAA;MAAK4Q,SAAS,EAAC,kIAAkI;MAAAC,QAAA,gBAEjJ7Q,OAAA;QAAK4Q,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C7Q,OAAA;UAAK4Q,SAAS,EAAC;QAA2H;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJ1R,OAAA;UAAK4Q,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtK1R,OAAA;UAAK4Q,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnK1R,OAAA;UAAK4Q,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGN1R,OAAA;QAAK4Q,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGc,KAAK,CAAC,EAAE,CAAC,CAAC,CAACpI,GAAG,CAAC,CAACqI,CAAC,EAAEtD,CAAC,kBACvBtO,OAAA,CAACvB,MAAM,CAACqS,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAErG,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCqD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACF3I,UAAU,EAAE;YACV8I,QAAQ,EAAE,CAAC,GAAG1F,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/ByD,MAAM,EAAEC,QAAQ;YAChBU,KAAK,EAAEtG,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFxF,KAAK,EAAE;YACL6J,IAAI,EAAG,GAAEvG,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BsE,GAAG,EAAG,GAAExG,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGW,CAAC;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1R,OAAA;QAAK4Q,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5B7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCZ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE8I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtE7Q,OAAA;YAAK4Q,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC7Q,OAAA;cAAK4Q,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnI7Q,OAAA;gBAAK4Q,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrG7Q,OAAA,CAACvB,MAAM,CAACyT,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM/P,QAAQ,CAAC,WAAW,CAAE;kBACrCqO,SAAS,EAAC,gNAAgN;kBAC1NzI,KAAK,EAAE;oBACLoK,QAAQ,EAAE3D,MAAM,CAAC4D,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA3B,QAAA,gBAEF7Q,OAAA,CAACb,MAAM;oBAACyR,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5C1R,OAAA;oBAAA6Q,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGf9B,cAAc,iBACb5P,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAG,CAAE;kBAC/BZ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAE,CAAE;kBAC9BjB,SAAS,EAAC,mJAAmJ;kBAC7JzI,KAAK,EAAE;oBACLsK,UAAU,EAAE7C,cAAc,CAAC9C,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/CrI,KAAK,EAAEmL,cAAc,CAAC9C,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/DxE,SAAS,EAAE,oCAAoC;oBAC/CiK,QAAQ,EAAE3D,MAAM,CAAC4D,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAA3B,QAAA,gBAEF7Q,OAAA,CAAClB,QAAQ;oBAAC8R,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C1R,OAAA;oBAAA6Q,QAAA,EACGjB,cAAc,CAAC9C,IAAI,KAAK,QAAQ,GAC5B,cAAa8C,cAAc,CAACH,QAAS,EAAC,GACtC,GAAEG,cAAc,CAAC3J,MAAO,KAAI2J,cAAc,CAACH,QAAS;kBAAC;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,eAGD1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAG,CAAE;kBAC/BZ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAE,CAAE;kBAC9BjB,SAAS,EAAC,oJAAoJ;kBAAAC,QAAA,eAE9J7Q,OAAA;oBAAK4Q,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC7Q,OAAA;sBAAI4Q,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzD1R,OAAA;sBAAK4Q,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC7Q,OAAA;wBAAA6Q,QAAA,GAAK,eAAa,EAACpQ,IAAI,GAAG,OAAO,GAAG,MAAM;sBAAA;wBAAA8Q,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAChDjR,IAAI,iBACHT,OAAA,CAAAE,SAAA;wBAAA2Q,QAAA,gBACE7Q,OAAA;0BAAA6Q,QAAA,GAAK,QAAM,EAACpQ,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACmB,QAAQ,IAAI,SAAS;wBAAA;0BAAA2P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1D1R,OAAA;0BAAA6Q,QAAA,GAAK,MAAI,EAACpQ,IAAI,CAACiB,GAAG,IAAIjB,IAAI,CAACoM,EAAE,IAAI,OAAO;wBAAA;0BAAA0E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC/C1R,OAAA;0BAAA6Q,QAAA,GAAK,MAAI,EAACpQ,IAAI,CAACoB,OAAO,IAAIpB,IAAI,CAACuF,EAAE,IAAIvF,IAAI,CAACiS,MAAM,IAAIjS,IAAI,CAACkL,WAAW,IAAI,OAAO;wBAAA;0BAAA4F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtF1R,OAAA;0BAAA6Q,QAAA,GAAK,cAAY,EAACxO,MAAM,CAACC,IAAI,CAAC7B,IAAI,CAAC,CAACkS,IAAI,CAAC,IAAI,CAAC;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACrD,CACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EAGZjR,IAAI,iBACHT,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAG,CAAE;kBAC/BZ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAE,CAAE;kBAC9BjB,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,eAEhK7Q,OAAA;oBAAK4Q,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBAEtC7Q,OAAA;sBAAK4Q,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7Q,OAAA;wBAAK4Q,SAAS,EAAC,6EAA6E;wBAAAC,QAAA,eAC1F7Q,OAAA;0BACE4S,GAAG,EAAEnS,IAAI,CAAC0B,cAAc,IAAI1B,IAAI,CAACkJ,YAAY,IAAI,qBAAsB;0BACvEkJ,GAAG,EAAEpS,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACmB,QAAS;0BAChCgP,SAAS,EAAC,4BAA4B;0BACtCkC,OAAO,EAAGC,CAAC,IAAK;4BACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,qBAAqB;0BACtC;wBAAE;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN1R,OAAA;sBAAK4Q,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB7Q,OAAA;wBAAI4Q,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,EACvDpQ,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACmB,QAAQ,IAAI;sBAAM;wBAAA2P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eAGL1R,OAAA;wBAAK4Q,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,GAAC,MACxC,EAACzE,MAAM,CAAC3L,IAAI,CAACiB,GAAG,IAAIjB,IAAI,CAACoM,EAAE,IAAI,EAAE,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,IACjD,EAACnM,IAAI,CAACoB,OAAO,GAAI,QAAOpB,IAAI,CAACoB,OAAQ,EAAC,GAAG,QAAQ,EAAC,IAClD,EAACe,eAAe,GAAI,WAAUA,eAAgB,EAAC,GAAG,UAAU;sBAAA;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C7Q,OAAA;0BAAK4Q,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzD7Q,OAAA;4BAAK4Q,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAM7K,EAAE,GAAGvF,IAAI,CAACoB,OAAO,IAAIpB,IAAI,CAACuF,EAAE,IAAIvF,IAAI,CAACiS,MAAM,IAAIjS,IAAI,CAACkL,WAAW,IAAI,CAAC;8BAC1E,OAAO3F,EAAE,CAACiN,cAAc,CAAC,CAAC;4BAC5B,CAAC,EAAE;0BAAC;4BAAA1B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D7Q,OAAA;4BAAK4Q,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAI;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAMqC,aAAa,GAAG1Q,WAAW,CAACwK,IAAI,CAAC5F,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;8BAC/E,OAAOwR,aAAa,GAAI,IAAGA,aAAa,CAACpJ,IAAK,EAAC,GAAIlH,eAAe,GAAI,IAAGA,eAAgB,EAAC,GAAG,KAAM;4BACrG,CAAC,EAAE;0BAAC;4BAAA2O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,gBACxD7Q,OAAA;4BAAK4Q,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnD1R,OAAA;4BAAK4Q,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAC1C,CAAC,MAAM;8BACN;8BACA,MAAMrE,MAAM,GAAG/L,IAAI,CAACoB,OAAO,IAAIpB,IAAI,CAACuF,EAAE,IAAIvF,IAAI,CAACiS,MAAM,IAAIjS,IAAI,CAACkL,WAAW,IAAI,CAAC;8BAC9E,KAAK,MAAM,CAACjF,SAAS,EAAEsI,UAAU,CAAC,IAAI3M,MAAM,CAAC8D,OAAO,CAACrC,YAAY,CAAC,EAAE;gCAAA,IAAAqP,kBAAA;gCAClE,MAAMlE,YAAY,IAAAkE,kBAAA,GAAGnE,UAAU,CAACzO,KAAK,cAAA4S,kBAAA,uBAAhBA,kBAAA,CAAkBnG,IAAI,CAAC5F,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;gCACpF,IAAIuN,YAAY,EAAE;kCAChB,MAAMmE,UAAU,GAAGrN,aAAa,CAACyG,MAAM,CAAC;kCACxC,OAAQ,GAAE4G,UAAU,CAAChO,UAAW,IAAGsB,SAAS,CAAC2M,WAAW,CAAC,CAAE,EAAC;gCAC9D;8BACF;8BACA;8BACA,IAAI7G,MAAM,GAAG,CAAC,EAAE;gCACd,MAAM4G,UAAU,GAAGrN,aAAa,CAACyG,MAAM,CAAC;gCACxC,OAAQ,GAAE4G,UAAU,CAAChO,UAAW,IAAGgO,UAAU,CAACnN,MAAM,CAACoN,WAAW,CAAC,CAAE,EAAC;8BACtE;8BACA,OAAO,aAAa;4BACtB,CAAC,EAAE;0BAAC;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D7Q,OAAA;4BAAK4Q,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAO;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,OAAOpQ,IAAI,CAACwB,gBAAgB,IAAIxB,IAAI,CAACyB,iBAAiB,IAAIzB,IAAI,CAAC6S,YAAY,IAAI7S,IAAI,CAAC0K,YAAY,IAAI,CAAC;4BACvG,CAAC,EAAE;0BAAC;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClD7Q,OAAA;0BAAK4Q,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,gBAC5D7Q,OAAA;4BAAK4Q,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAK;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClCpQ,IAAI,CAACqB,YAAY,IAAIrB,IAAI,CAACqI,KAAK,IAAI;0BAAC;4BAAAyI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzD7Q,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClCpQ,IAAI,CAACsB,aAAa,IAAItB,IAAI,CAAC8S,MAAM,IAAI;0BAAC;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D7Q,OAAA;4BAAK4Q,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAS;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtD1R,OAAA;4BAAK4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,GAClC,CAAC,MAAM;8BACN,MAAM2C,QAAQ,GAAG/S,IAAI,CAACuB,YAAY,IAAIvB,IAAI,CAAC+S,QAAQ,IAAI,CAAC;8BACxD,OAAO/H,IAAI,CAACC,KAAK,CAAC8H,QAAQ,CAAC;4BAC7B,CAAC,EAAE,CAAC,EAAC,GACP;0BAAA;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGL,CAAC,MAAM;wBACN;wBACA,KAAK,MAAM,CAAChL,SAAS,EAAEsI,UAAU,CAAC,IAAI3M,MAAM,CAAC8D,OAAO,CAACrC,YAAY,CAAC,EAAE;0BAAA,IAAA2P,kBAAA;0BAClE,MAAMjD,SAAS,IAAAiD,kBAAA,GAAGzE,UAAU,CAACzO,KAAK,cAAAkT,kBAAA,uBAAhBA,kBAAA,CAAkBtM,SAAS,CAACC,CAAC,IAAIgF,MAAM,CAAChF,CAAC,CAAC1F,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,CAAC;0BACtF,IAAI8O,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAKkD,SAAS,EAAE;4BAC/C,oBACE1T,OAAA;8BAAK4Q,SAAS,EAAC,kBAAkB;8BAAAC,QAAA,eAC/B7Q,OAAA;gCAAK4Q,SAAS,EAAC,uEAAuE;gCAAAC,QAAA,gBACpF7Q,OAAA;kCAAK4Q,SAAS,EAAC,yBAAyB;kCAAAC,QAAA,EAAC;gCAAe;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eAC9D1R,OAAA;kCAAK4Q,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,GAAC,GAC3C,EAACL,SAAS,GAAG,CAAC,EAAC,MAAI,EAACxB,UAAU,CAACzO,KAAK,CAAC+G,MAAM;gCAAA;kCAAAiK,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACzC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAEV;wBACF;wBACA,OAAO,IAAI;sBACb,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb,eAaD1R,OAAA;kBAAK4Q,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpI7Q,OAAA,CAACvB,MAAM,CAACkV,EAAE;oBACR/C,SAAS,EAAC,sCAAsC;oBAChDzI,KAAK,EAAE;sBACLsK,UAAU,EAAE,mDAAmD;sBAC/DmB,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCC,UAAU,EAAE,6BAA6B;sBACzC7M,MAAM,EAAE;oBACV,CAAE;oBACFgK,OAAO,EAAE;sBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjC/J,UAAU,EAAE;sBAAE8I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAR,QAAA,EAC/C;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZ1R,OAAA;oBAAK4Q,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvEtI,iBAAiB,CAAC,CAAC,CAACgB,GAAG,CAAE7C,SAAS,IAAK;sBAAA,IAAAqN,sBAAA;sBACtC,MAAM9N,MAAM,GAAG3B,YAAY,CAACoC,SAAS,CAAC;sBACtC,MAAMsN,UAAU,GAAGpQ,cAAc,KAAK8C,SAAS;sBAC/C,MAAMuN,SAAS,GAAG,EAAAF,sBAAA,GAAAjQ,YAAY,CAAC4C,SAAS,CAAC,cAAAqN,sBAAA,uBAAvBA,sBAAA,CAAyBxT,KAAK,CAAC+G,MAAM,KAAI,CAAC;sBAE5D,oBACEtH,OAAA,CAACvB,MAAM,CAACqS,GAAG;wBAETF,SAAS,EAAC,kCAAkC;wBAC5CuB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAAvB,QAAA,gBAE5B7Q,OAAA,CAACvB,MAAM,CAACyT,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCQ,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BE,OAAO,EAAEA,CAAA,KAAM/K,kBAAkB,CAACb,SAAS,CAAE;0BAC7CkK,SAAS,EAAG,+GACVoD,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACH7L,KAAK,EAAE;4BACLsK,UAAU,EAAEuB,UAAU,GACjB,2BAA0B/N,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7EuP,MAAM,EAAG,aAAYF,UAAU,GAAG,SAAS,GAAG/N,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzEoD,SAAS,EAAE0L,UAAU,GAChB,YAAW/N,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxCuD,SAAS,EAAE4L,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjD/M,MAAM,EAAE+M,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACF/C,OAAO,EAAE+C,UAAU,GAAG;4BACpB1L,SAAS,EAAE,CACR,YAAWrC,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACDuN,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACP/J,UAAU,EAAE;4BACV8I,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAE4C,UAAU,GAAG3C,QAAQ,GAAG,CAAC;4BACjCC,IAAI,EAAE;0BACR,CAAE;0BACFtM,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAWiP,SAAU,SAAS;0BAAApD,QAAA,gBAEnE7Q,OAAA;4BAAM4Q,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE5K,MAAM,CAACb;0BAAU;4BAAAmM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChEsC,UAAU,iBACThU,OAAA,CAACvB,MAAM,CAACqS,GAAG;4BACTC,OAAO,EAAE;8BAAEqB,KAAK,EAAE,CAAC;8BAAElB,MAAM,EAAE,CAAC,GAAG;8BAAEF,OAAO,EAAE;4BAAE,CAAE;4BAChDC,OAAO,EAAE;8BACPmB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClBlB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBF,OAAO,EAAE,CAAC;8BACV1I,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACV+J,KAAK,EAAE;gCAAEjB,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BAC3DJ,MAAM,EAAE;gCAAEC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAS,CAAC;8BACzDhJ,SAAS,EAAE;gCAAE6I,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BACjEN,OAAO,EAAE;gCAAEG,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFP,SAAS,EAAC,8KAA8K;4BACxLzI,KAAK,EAAE;8BACLsK,UAAU,EAAE,mDAAmD;8BAC/DyB,MAAM,EAAE,iBAAiB;8BACzBC,MAAM,EAAE;4BACV,CAAE;4BAAAtD,QAAA,eAEF7Q,OAAA,CAACvB,MAAM,CAAC2V,IAAI;8BACVxD,SAAS,EAAC,kCAAkC;8BAC5CK,OAAO,EAAE;gCACPmB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClBlB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACF7I,UAAU,EAAE;gCACV8I,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChBC,IAAI,EAAE;8BACR,CAAE;8BAAAT,QAAA,EACH;4BAED;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACD1R,OAAA;4BACE4Q,SAAS,EAAC,2HAA2H;4BACrIzI,KAAK,EAAE;8BACLsK,UAAU,EAAExM,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChB8N,QAAQ,EAAE;4BACZ,CAAE;4BAAA1B,QAAA,EAEDoD;0BAAS;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhB1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;0BACTF,SAAS,EAAC,aAAa;0BACvBuB,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAAvB,QAAA,eAE5B7Q,OAAA;4BACE4Q,SAAS,EAAC,mDAAmD;4BAC7DzI,KAAK,EAAE;8BACL1D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBkP,UAAU,EAAG,eAAc7N,MAAM,CAACpB,WAAY,EAAC;8BAC/C4N,UAAU,EAAG,GAAExM,MAAM,CAACf,WAAY,IAAG;8BACrCgP,MAAM,EAAG,aAAYjO,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAA2L,QAAA,EAED5K,MAAM,CAACjB;0BAAK;4BAAAuM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GRhL,SAAS;wBAAA6K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEN1R,OAAA;oBAAG4Q,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAKN1R,OAAA,CAACvB,MAAM,CAACyT,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAElB,MAAM,EAAE;kBAAI,CAAE;kBACzCmB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE7J,gBAAiB;kBAC1B4L,QAAQ,EAAE3R,OAAQ;kBAClBkO,SAAS,EAAC,qNAAqN;kBAC/NzI,KAAK,EAAE;oBACLoK,QAAQ,EAAE3D,MAAM,CAAC4D,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA3B,QAAA,gBAEF7Q,OAAA,CAACZ,SAAS;oBAACwR,SAAS,EAAG,yBAAwBlO,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAA6O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClF1R,OAAA;oBAAA6Q,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,KAAK,KAAK,CAAAjR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8L,IAAI,MAAK,OAAO,KAAI9L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6L,OAAO,EAAC,iBACjDtM,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCZ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE8I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C7Q,OAAA;YAAK4Q,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC7Q,OAAA;cAAK4Q,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7H7Q,OAAA;gBAAK4Q,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC7Q,OAAA;kBAAK4Q,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClF7Q,OAAA;oBAAM4Q,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN1R,OAAA;kBAAA6Q,QAAA,gBACE7Q,OAAA;oBAAI4Q,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpD1R,OAAA;oBAAG4Q,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGD1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCZ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE8I,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzC7Q,OAAA;YAAK4Q,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9G7Q,OAAA;cAAK4Q,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnG1R,OAAA;cAAK4Q,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrG1R,OAAA;cAAK4Q,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxF7Q,OAAA;gBAAK4Q,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5C7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTG,OAAO,EAAE;oBACPmB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBkC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFjM,UAAU,EAAE;oBACV8I,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExB7Q,OAAA;oBAAI4Q,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7G7Q,OAAA,CAACvB,MAAM,CAAC2V,IAAI;sBACVnD,OAAO,EAAE;wBACPsD,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFlM,UAAU,EAAE;wBACV8I,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIzI,KAAK,EAAE;wBACLqM,cAAc,EAAE,WAAW;wBAC3BZ,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClC5M,MAAM,EAAE;sBACV,CAAE;sBAAA4J,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACd1R,OAAA;sBAAAuR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN1R,OAAA,CAACvB,MAAM,CAAC2V,IAAI;sBACVnD,OAAO,EAAE;wBACP6C,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACFzL,UAAU,EAAE;wBACV8I,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFnJ,KAAK,EAAE;wBACL1D,KAAK,EAAE,SAAS;wBAChBgQ,UAAU,EAAE,KAAK;wBACjBX,UAAU,EAAE;sBACd,CAAE;sBAAAjD,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGb1R,OAAA,CAACvB,MAAM,CAACiW,CAAC;kBACP3D,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAG,CAAE;kBAC/BZ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAE,CAAE;kBAC9BxJ,UAAU,EAAE;oBAAE0J,KAAK,EAAE,GAAG;oBAAEZ,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHzI,KAAK,EAAE;oBACL1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCrB,UAAU,EAAE,0CAA0C;oBACtDmB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAhD,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGX1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEoB,KAAK,EAAE;kBAAI,CAAE;kBACpCnB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEoB,KAAK,EAAE;kBAAE,CAAE;kBAClC/J,UAAU,EAAE;oBAAE0J,KAAK,EAAE,GAAG;oBAAEZ,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExB7Q,OAAA;oBAAG4Q,SAAS,EAAC,6JAA6J;oBACvKzI,KAAK,EAAE;sBACL2L,UAAU,EAAE,6BAA6B;sBACzCa,SAAS,EAAE;oBACb,CAAE;oBAAA9D,QAAA,EACFzN;kBAAiB;oBAAAmO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGb1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAG,CAAE;kBAC/BZ,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE;kBAAE,CAAE;kBAC9BxJ,UAAU,EAAE;oBAAE0J,KAAK,EAAE,CAAC;oBAAEZ,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACE9L,IAAI,EAAErF,OAAO;oBACbkV,KAAK,EAAEpS,WAAW,CAAC8E,MAAM;oBACzBuN,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB7P,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEjG,QAAQ;oBACd8V,KAAK,EAAE1F,aAAa,CAAC5H,MAAM;oBAC3BuN,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpB7P,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE9F,OAAO;oBACb2V,KAAK,EAAEpS,WAAW,CAACyE,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACrF,aAAa,GAAG,CAAC,CAAC,CAACuF,MAAM;oBAC1DuN,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpB7P,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE/F,MAAM;oBACZ4V,KAAK,EAAEpS,WAAW,CAAC6I,MAAM,CAAC,CAACC,GAAG,EAAElE,CAAC,KAAKkE,GAAG,IAAIlE,CAAC,CAACvF,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACoR,cAAc,CAAC,CAAC;oBACjF4B,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB7P,WAAW,EAAE;kBACf,CAAC,CACF,CAACqE,GAAG,CAAC,CAACyL,IAAI,EAAExL,KAAK,kBAChBxJ,OAAA,CAACvB,MAAM,CAACqS,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEoB,KAAK,EAAE;oBAAI,CAAE;oBACpCnB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEoB,KAAK,EAAE;oBAAE,CAAE;oBAClC/J,UAAU,EAAE;sBAAE0J,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,GAAG;sBAAE2H,QAAQ,EAAE;oBAAI,CAAE;oBACxDgB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCjB,SAAS,EAAG,qBAAoBoE,IAAI,CAACF,UAAW,8EAA8E;oBAC9H3M,KAAK,EAAE;sBACL+L,MAAM,EAAG,aAAYc,IAAI,CAAC9P,WAAY,IAAG;sBACzCoD,SAAS,EAAG,cAAa0M,IAAI,CAAC9P,WAAY;oBAC5C,CAAE;oBAAA2L,QAAA,gBAEF7Q,OAAA;sBAAK4Q,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF1R,OAAA,CAACgV,IAAI,CAACjQ,IAAI;sBACR6L,SAAS,EAAC,kDAAkD;sBAC5DzI,KAAK,EAAE;wBAAE1D,KAAK,EAAEuQ,IAAI,CAACD,SAAS;wBAAE9N,MAAM,EAAE;sBAAyC;oBAAE;sBAAAsK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACF1R,OAAA;sBACE4Q,SAAS,EAAC,0EAA0E;sBACpFzI,KAAK,EAAE;wBACL1D,KAAK,EAAEuQ,IAAI,CAACD,SAAS;wBACrBjB,UAAU,EAAG,6BAA4B;wBACzC7M,MAAM,EAAE,oCAAoC;wBAC5CsL,QAAQ,EAAE;sBACZ,CAAE;sBAAA1B,QAAA,EAEDmE,IAAI,CAACJ;oBAAK;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACN1R,OAAA;sBACE4Q,SAAS,EAAC,4CAA4C;sBACtDzI,KAAK,EAAE;wBACL1D,KAAK,EAAE,SAAS;wBAChBqP,UAAU,EAAE,6BAA6B;wBACzCvB,QAAQ,EAAE;sBACZ,CAAE;sBAAA1B,QAAA,EAEDmE,IAAI,CAACH;oBAAK;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDlI,KAAK;oBAAA+H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZhP,OAAO,iBACN1C,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3D7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzB7I,UAAU,EAAE;cAAE8I,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACF1R,OAAA;YAAG4Q,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAAChP,OAAO,iBACP1C,OAAA,CAACvB,MAAM,CAACqS,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE;UAAG,CAAE;UAC/BZ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEa,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE0J,KAAK,EAAE,GAAG;YAAEZ,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjE7Q,OAAA;YAAK4Q,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/B3B,aAAa,CAAC5H,MAAM,GAAG,CAAC,iBACvBtH,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAI,CAAE;cACpCnB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cAClC/J,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjB7Q,OAAA;gBAAI4Q,SAAS,EAAC,gGAAgG;gBAACzI,KAAK,EAAE;kBACpHsK,UAAU,EAAE,mDAAmD;kBAC/DmB,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzC7M,MAAM,EAAE;gBACV,CAAE;gBAAA4J,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGL1R,OAAA;gBAAK4Q,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/G3B,aAAa,CAAC,CAAC,CAAC,iBACflP,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBAETmE,GAAG,EAAExU,IAAI,IAAI2L,MAAM,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,GAAGyC,aAAa,GAAG,IAAK;kBACtF,gBAAc+K,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI;kBACnC,kBAAgB,CAAE;kBAClBqP,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCZ,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVc,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBkC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFjM,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVZ,QAAQ,EAAE,GAAG;oBACbiB,KAAK,EAAE;sBAAEjB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DgD,OAAO,EAAE;sBAAEnD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFa,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCjB,SAAS,EAAG,oBACVf,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHyG,KAAK,EAAE;oBACL+M,MAAM,EAAE,OAAO;oBACf9M,SAAS,EAAEyH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EuF,MAAM,EAAE4I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACV2G,UAAU,EAAE,eAAe;oBAC3B6L,MAAM,EAAErE,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EyT,YAAY,EAAEtF,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE+Q,UAAU,EAAE5C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAmP,QAAA,gBAGF7Q,OAAA;oBAAK4Q,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnK7Q,OAAA;sBAAM4Q,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGN1R,OAAA;oBACE4Q,SAAS,EAAG,8BAA6B1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAChC,KAAM,mBAAkByK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC3B,IAAK,kBAAkB;oBACpIqD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4G,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,IAAG;sBAC9DuQ,KAAK,EAAE;oBACT,CAAE;oBAAAvE,QAAA,eAEF7Q,OAAA;sBACE4Q,SAAS,EAAG,GAAE1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC/B,OAAQ,uEAAuE;sBAAAmM,QAAA,gBAEnH7Q,OAAA;wBAAK4Q,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF1R,OAAA;wBACE4Q,SAAS,EAAC,oMAAoM;wBAC9MzI,KAAK,EAAE;0BACL1D,KAAK,EAAE,SAAS;0BAChByP,MAAM,EAAE;wBACV,CAAE;wBAAArD,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAG,yBAAwBnQ,IAAI,IAAIyO,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,KAAKjB,IAAI,CAACiB,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAmP,QAAA,eACnI7Q,OAAA;0BACE4Q,SAAS,EAAC,wEAAwE;0BAClFzI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvC8M,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAArE,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAc,gBAC9BnC,OAAA;4BACE4S,GAAG,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAe;4BACrC0Q,GAAG,EAAE3D,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAK;4BAC3BiP,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF1R,OAAA;4BACE4Q,SAAS,EAAC,2EAA2E;4BACrFzI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBhO,KAAK,EAAE,SAAS;8BAChB8N,QAAQ,EAAE;4BACZ,CAAE;4BAAA1B,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAI,CAAC0T,MAAM,CAAC,CAAC,CAAC,CAAChC,WAAW,CAAC;0BAAC;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN1R,OAAA;wBACE4Q,SAAS,EAAC,iCAAiC;wBAC3CzI,KAAK,EAAE;0BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC7B;wBAAU,CAAE;wBAAAiM,QAAA,EAEjD3B,aAAa,CAAC,CAAC,CAAC,CAACvN;sBAAI;wBAAA4P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL1R,OAAA;wBAAK4Q,SAAS,EAAC,yBAAyB;wBAACzI,KAAK,EAAE;0BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;wBAAU,CAAE;wBAAAkM,QAAA,GACxF3B,aAAa,CAAC,CAAC,CAAC,CAACrN,OAAO,CAACoR,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN1R,OAAA;wBAAK4Q,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD7Q,OAAA;0BAAMmI,KAAK,EAAE;4BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;0BAAU,CAAE;0BAAAkM,QAAA,GAAC,eACpD,EAAC3B,aAAa,CAAC,CAAC,CAAC,CAAChN,iBAAiB;wBAAA;0BAAAqP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP1R,OAAA;0BAAMmI,KAAK,EAAE;4BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;0BAAU,CAAE;0BAAAkM,QAAA,GAAC,eACpD,EAAC3B,aAAa,CAAC,CAAC,CAAC,CAACnN,aAAa;wBAAA;0BAAAwP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtHA,UAASxC,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI,EAAC;kBAAA6P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuH3B,CACb,EAGAxC,aAAa,CAAC,CAAC,CAAC,iBACflP,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBAETmE,GAAG,EAAExU,IAAI,IAAI2L,MAAM,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,GAAGyC,aAAa,GAAG,IAAK;kBACtF,gBAAc+K,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI;kBACnC,kBAAgB,CAAE;kBAClBqP,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE,CAAC,GAAG;oBAAEO,KAAK,EAAE;kBAAI,CAAE;kBAC7CnB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVa,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC;oBACRkC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxBzC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFxJ,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVZ,QAAQ,EAAE,GAAG;oBACbmD,OAAO,EAAE;sBAAEnD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DO,CAAC,EAAE;sBAAEV,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFa,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCjB,SAAS,EAAG,yBACVf,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHyG,KAAK,EAAE;oBACL+M,MAAM,EAAE,OAAO;oBACf9M,SAAS,EAAEyH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EuF,MAAM,EAAE4I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACV2G,UAAU,EAAE,eAAe;oBAC3B6L,MAAM,EAAErE,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EyT,YAAY,EAAEtF,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE+Q,UAAU,EAAE5C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAAmP,QAAA,gBAIrB7Q,OAAA;oBAAK4Q,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzK7Q,OAAA;sBAAM4Q,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGN1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDxJ,UAAU,EAAE;sBAAE8I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErE7Q,OAAA,CAACjB,OAAO;sBAAC6R,SAAS,EAAC;oBAA0C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGb1R,OAAA;oBACE4Q,SAAS,EAAG,8BAA6B1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAChC,KAAM,sBAAqByK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC3B,IAAK,uCAAuC;oBAC5JqD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4G,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,qCAAoC;sBAC/FuQ,KAAK,EAAE;oBACT,CAAE;oBAAAvE,QAAA,eAEF7Q,OAAA;sBACE4Q,SAAS,EAAG,GAAE1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC/B,OAAQ,uEAAuE;sBACnHyD,KAAK,EAAE;wBACLsK,UAAU,EAAG,GAAEvD,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC/B,OAAQ;sBAC/C,CAAE;sBAAAmM,QAAA,gBAEF7Q,OAAA;wBAAK4Q,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlG1R,OAAA;wBACE4Q,SAAS,EAAC,wMAAwM;wBAClNzI,KAAK,EAAE;0BACL1D,KAAK,EAAE,SAAS;0BAChBqP,UAAU,EAAE,6BAA6B;0BACzCI,MAAM,EAAE;wBACV,CAAE;wBAAArD,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAG,yBAAwBnQ,IAAI,IAAIyO,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,KAAKjB,IAAI,CAACiB,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAmP,QAAA,gBACnI7Q,OAAA;0BACE4Q,SAAS,EAAC,wEAAwE;0BAClFzI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvC8M,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAArE,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAc,gBAC9BnC,OAAA;4BACE4S,GAAG,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAe;4BACrC0Q,GAAG,EAAE3D,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAK;4BAC3BiP,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF1R,OAAA;4BACE4Q,SAAS,EAAC,2EAA2E;4BACrFzI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBhO,KAAK,EAAE,SAAS;8BAChB8N,QAAQ,EAAE;4BACZ,CAAE;4BAAA1B,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAI,CAAC0T,MAAM,CAAC,CAAC,CAAC,CAAChC,WAAW,CAAC;0BAAC;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLjR,IAAI,IAAIyO,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,KAAKjB,IAAI,CAACiB,GAAG,iBACxC1B,OAAA;0BACE4Q,SAAS,EAAC,4DAA4D;0BACtEzI,KAAK,EAAE;4BACLsK,UAAU,EAAE,0CAA0C;4BACtDnK,SAAS,EAAE;0BACb,CAAE;0BAAAuI,QAAA,eAEF7Q,OAAA,CAAChB,MAAM;4BAAC4R,SAAS,EAAC;0BAAuB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1D7Q,OAAA;0BACE4Q,SAAS,EAAC,6BAA6B;0BACvCzI,KAAK,EAAE;4BACL1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC7B,SAAS;4BACtCkP,UAAU,EAAG,eAAc5E,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,EAAC;4BAC9DoC,MAAM,EAAE;0BACV,CAAE;0BAAA4J,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAACvN;wBAAI;0BAAA4P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJ7B,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,iBAClC1B,OAAA;0BACE4Q,SAAS,EAAC,yDAAyD;0BACnEzI,KAAK,EAAE;4BACLsK,UAAU,EAAE,0CAA0C;4BACtDhO,KAAK,EAAE,SAAS;4BAChB6D,SAAS,EAAE,+BAA+B;4BAC1C4L,MAAM,EAAE,mBAAmB;4BAC3B3B,QAAQ,EAAE;0BACZ,CAAE;0BAAA1B,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAEN1R,OAAA;wBACE4Q,SAAS,EAAG,6DAA4D1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAChC,KAAM,qDAAqD;wBACzJ0D,KAAK,EAAE;0BACLsK,UAAU,EAAG,2BAA0BvD,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAACvB,WAAY,KAAIgK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChBqP,UAAU,EAAE,6BAA6B;0BACzCxL,SAAS,EAAG,cAAa4G,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,IAAG;0BAC9DqP,MAAM,EAAE;wBACV,CAAE;wBAAArD,QAAA,GAED3B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC1B,IAAI,iBAAI1G,KAAK,CAACiX,aAAa,CAACpG,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC1B,IAAI,EAAE;0BAC7E6L,SAAS,EAAE,SAAS;0BACpBzI,KAAK,EAAE;4BAAE1D,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACFzE,OAAA;0BAAMmI,KAAK,EAAE;4BAAE1D,KAAK,EAAE;0BAAU,CAAE;0BAAAoM,QAAA,EAAE3B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAACzB;wBAAK;0BAAAuM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC7Q,OAAA;0BAAK4Q,SAAS,EAAC,oBAAoB;0BAACzI,KAAK,EAAE;4BACzC1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC7B,SAAS;4BACtCkP,UAAU,EAAG,eAAc5E,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,EAAC;4BAC9DoC,MAAM,EAAE;0BACV,CAAE;0BAAA4J,QAAA,GACC3B,aAAa,CAAC,CAAC,CAAC,CAACrN,OAAO,CAACoR,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN1R,OAAA;0BAAK4Q,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChD7Q,OAAA;4BAAK4Q,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B7Q,OAAA;8BAAK4Q,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrD7Q,OAAA,CAACd,OAAO;gCAAC0R,SAAS,EAAC,SAAS;gCAACzI,KAAK,EAAE;kCAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;gCAAU;8BAAE;gCAAA4M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClF1R,OAAA;gCAAM4Q,SAAS,EAAC,WAAW;gCAACzI,KAAK,EAAE;kCAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;gCAAU,CAAE;gCAAAkM,QAAA,EAC3E3B,aAAa,CAAC,CAAC,CAAC,CAAChN;8BAAiB;gCAAAqP,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN1R,OAAA;8BAAK4Q,SAAS,EAAC,oBAAoB;8BAACzI,KAAK,EAAE;gCAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;8BAAU,CAAE;8BAAAkM,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACN1R,OAAA;4BAAK4Q,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B7Q,OAAA;8BAAK4Q,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrD7Q,OAAA,CAACf,OAAO;gCAAC2R,SAAS,EAAC,SAAS;gCAACzI,KAAK,EAAE;kCAAE1D,KAAK,EAAE;gCAAU;8BAAE;gCAAA8M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5D1R,OAAA;gCAAM4Q,SAAS,EAAC,WAAW;gCAACzI,KAAK,EAAE;kCAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;gCAAU,CAAE;gCAAAkM,QAAA,EAC3E3B,aAAa,CAAC,CAAC,CAAC,CAACnN;8BAAa;gCAAAwP,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN1R,OAAA;8BAAK4Q,SAAS,EAAC,oBAAoB;8BAACzI,KAAK,EAAE;gCAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;8BAAU,CAAE;8BAAAkM,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxMA,SAAQxC,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI,EAAC;kBAAA6P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyM1B,CACb,EAGAxC,aAAa,CAAC,CAAC,CAAC,iBACflP,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBAETmE,GAAG,EAAExU,IAAI,IAAI2L,MAAM,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC,GAAGyC,aAAa,GAAG,IAAK;kBACtF,gBAAc+K,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI;kBACnC,kBAAgB,CAAE;kBAClBqP,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCZ,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVc,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBkC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFjM,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVZ,QAAQ,EAAE,GAAG;oBACbiB,KAAK,EAAE;sBAAEjB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DgD,OAAO,EAAE;sBAAEnD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFa,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCjB,SAAS,EAAG,oBACVf,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHyG,KAAK,EAAE;oBACL+M,MAAM,EAAE,OAAO;oBACf9M,SAAS,EAAEyH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EuF,MAAM,EAAE4I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACV2G,UAAU,EAAE,eAAe;oBAC3B6L,MAAM,EAAErE,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EyT,YAAY,EAAEtF,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClE+Q,UAAU,EAAE5C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAmP,QAAA,gBAGF7Q,OAAA;oBAAK4Q,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtK7Q,OAAA;sBAAM4Q,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGN1R,OAAA;oBACE4Q,SAAS,EAAG,8BAA6B1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAChC,KAAM,mBAAkByK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC3B,IAAK,kBAAkB;oBACpIqD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4G,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC5B,WAAY,IAAG;sBAC9DuQ,KAAK,EAAE;oBACT,CAAE;oBAAAvE,QAAA,eAEF7Q,OAAA;sBACE4Q,SAAS,EAAG,GAAE1B,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC/B,OAAQ,uEAAuE;sBAAAmM,QAAA,gBAEnH7Q,OAAA;wBAAK4Q,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF1R,OAAA;wBACE4Q,SAAS,EAAC,sMAAsM;wBAChNzI,KAAK,EAAE;0BACL1D,KAAK,EAAE,SAAS;0BAChByP,MAAM,EAAE;wBACV,CAAE;wBAAArD,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN1R,OAAA;wBAAK4Q,SAAS,EAAG,yBAAwBnQ,IAAI,IAAIyO,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAG,KAAKjB,IAAI,CAACiB,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAmP,QAAA,eACnI7Q,OAAA;0BACE4Q,SAAS,EAAC,wEAAwE;0BAClFzI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvC8M,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAArE,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAc,gBAC9BnC,OAAA;4BACE4S,GAAG,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAAC/M,cAAe;4BACrC0Q,GAAG,EAAE3D,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAK;4BAC3BiP,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF1R,OAAA;4BACE4Q,SAAS,EAAC,2EAA2E;4BACrFzI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBhO,KAAK,EAAE,SAAS;8BAChB8N,QAAQ,EAAE;4BACZ,CAAE;4BAAA1B,QAAA,EAED3B,aAAa,CAAC,CAAC,CAAC,CAACvN,IAAI,CAAC0T,MAAM,CAAC,CAAC,CAAC,CAAChC,WAAW,CAAC;0BAAC;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN1R,OAAA;wBACE4Q,SAAS,EAAC,iCAAiC;wBAC3CzI,KAAK,EAAE;0BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC7B;wBAAU,CAAE;wBAAAiM,QAAA,EAEjD3B,aAAa,CAAC,CAAC,CAAC,CAACvN;sBAAI;wBAAA4P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL1R,OAAA;wBAAK4Q,SAAS,EAAC,yBAAyB;wBAACzI,KAAK,EAAE;0BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;wBAAU,CAAE;wBAAAkM,QAAA,GACxF3B,aAAa,CAAC,CAAC,CAAC,CAACrN,OAAO,CAACoR,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN1R,OAAA;wBAAK4Q,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD7Q,OAAA;0BAAMmI,KAAK,EAAE;4BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;0BAAU,CAAE;0BAAAkM,QAAA,GAAC,eACpD,EAAC3B,aAAa,CAAC,CAAC,CAAC,CAAChN,iBAAiB;wBAAA;0BAAAqP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP1R,OAAA;0BAAMmI,KAAK,EAAE;4BAAE1D,KAAK,EAAEyK,aAAa,CAAC,CAAC,CAAC,CAACzI,IAAI,CAAC9B;0BAAU,CAAE;0BAAAkM,QAAA,GAAC,eACpD,EAAC3B,aAAa,CAAC,CAAC,CAAC,CAACnN,aAAa;wBAAA;0BAAAwP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtHA,SAAQxC,aAAa,CAAC,CAAC,CAAC,CAACxN,GAAI,EAAC;kBAAA6P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuH1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASI,CACb,EAGA9N,cAAc,GACb;YACAJ,WAAW,CAAC8D,MAAM,GAAG,CAAC,iBACpBtH,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtC7Q,OAAA;gBAAK4Q,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC7Q,OAAA,CAACvB,MAAM,CAAC8W,EAAE;kBACR3E,SAAS,EAAC,kDAAkD;kBAC5DzI,KAAK,EAAE;oBACLsK,UAAU,EAAG,0BAAyBnO,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY,KAAIZ,YAAY,CAACV,cAAc,CAAC,CAACe,SAAU,GAAE;oBAC5HiP,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzC7M,MAAM,EAAG,wBAAuB3C,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY;kBAC3E,CAAE;kBACF+L,OAAO,EAAE;oBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE8I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,GAE7CvM,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU,EAAC,GAAC,EAACd,YAAY,CAACV,cAAc,CAAC,CAACoB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU;gBAAA;kBAAAmM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZ1R,OAAA;kBAAG4Q,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1DrN,WAAW,CAAC8D,MAAM,EAAC,2BACtB;gBAAA;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ1R,OAAA,CAACvB,MAAM,CAACyT,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMzO,iBAAiB,CAAC,IAAI,CAAE;kBACvC+M,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGN1R,OAAA;gBAAK4Q,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrC7Q,OAAA;kBAAK4Q,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjCrN,WAAW,CAAC+F,GAAG,CAAC,CAACiM,QAAQ,EAAEhM,KAAK,KAAK;oBACpC,MAAMiM,UAAU,GAAGjM,KAAK,GAAG,CAAC;oBAC5B,MAAMqG,aAAa,GAAGpP,IAAI,IAAI2L,MAAM,CAACoJ,QAAQ,CAAC9T,GAAG,CAAC,KAAK0K,MAAM,CAAC3L,IAAI,CAACiB,GAAG,CAAC;oBAEvE,oBACE1B,OAAA,CAACvB,MAAM,CAACqS,GAAG;sBAETmE,GAAG,EAAEpF,aAAa,GAAGzL,WAAW,GAAG,IAAK;sBACxC,gBAAcoR,QAAQ,CAAC9T,GAAI;sBAC3B,kBAAgB+T,UAAW;sBAC3B1E,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEa,CAAC,EAAE;sBAAG,CAAE;sBAC/BZ,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEa,CAAC,EAAE;sBAAE,CAAE;sBAC9BxJ,UAAU,EAAE;wBAAE0J,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,IAAI;wBAAE2H,QAAQ,EAAE;sBAAI,CAAE;sBACzDgB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEP,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCjB,SAAS,EAAG,+BACVf,aAAa,GACT,yCAAyC,GACzC,EACL,EAAE;sBACH1H,KAAK,EAAE;wBACLC,SAAS,EAAEyH,aAAa,GAAG,aAAa,GAAG,UAAU;wBACrD5I,MAAM,EAAE4I,aAAa,GACjB,2EAA2E,GAC3E,MAAM;wBACVxH,UAAU,EAAE,eAAe;wBAC3B6L,MAAM,EAAErE,aAAa,GAAG,mBAAmB,GAAG,MAAM;wBACpDsF,YAAY,EAAEtF,aAAa,GAAG,MAAM,GAAG,KAAK;wBAC5C4C,UAAU,EAAE5C,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvHJ,QAAQ,EAAE,UAAU;wBACpB0E,MAAM,EAAEtE,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAgB,QAAA,eAGF7Q,OAAA;wBACE4Q,SAAS,EAAG,oBAAmB4E,QAAQ,CAAC/O,IAAI,CAAChC,KAAM,sBAAqB+Q,QAAQ,CAAC/O,IAAI,CAAC3B,IAAK,uDAAuD;wBAClJqD,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAakN,QAAQ,CAAC/O,IAAI,CAAC5B,WAAY;wBACrD,CAAE;wBAAAgM,QAAA,eAEF7Q,OAAA;0BACE4Q,SAAS,EAAG,GAAE4E,QAAQ,CAAC/O,IAAI,CAAC/B,OAAQ,oFAAoF;0BACxHyD,KAAK,EAAE;4BACL+L,MAAM,EAAG,aAAYsB,QAAQ,CAAC/O,IAAI,CAACvB,WAAY;0BACjD,CAAE;0BAAA2L,QAAA,gBAGF7Q,OAAA;4BAAK4Q,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjG1R,OAAA;4BAAK4Q,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpD7Q,OAAA;8BAAK4Q,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvB7Q,OAAA;gCACE4Q,SAAS,EAAC,kJAAkJ;gCAC5JzI,KAAK,EAAE;kCACL1D,KAAK,EAAE,SAAS;kCAChBqP,UAAU,EAAE,6BAA6B;kCACzCI,MAAM,EAAE,iCAAiC;kCACzC5L,SAAS,EAAE;gCACb,CAAE;gCAAAuI,QAAA,GACH,GACE,EAAC4E,UAAU;8BAAA;gCAAAlE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGN1R,OAAA;8BAAK4Q,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvB7Q,OAAA;gCACE4Q,SAAS,EAAC,gEAAgE;gCAC1EzI,KAAK,EAAE;kCACLsK,UAAU,EAAE,SAAS;kCACrBnK,SAAS,EAAE,4BAA4B;kCACvC8M,KAAK,EAAE,MAAM;kCACbF,MAAM,EAAE;gCACV,CAAE;gCAAArE,QAAA,EAED2E,QAAQ,CAACrT,cAAc,gBACtBnC,OAAA;kCACE4S,GAAG,EAAE4C,QAAQ,CAACrT,cAAe;kCAC7B0Q,GAAG,EAAE2C,QAAQ,CAAC7T,IAAK;kCACnBiP,SAAS,EAAC;gCAAyC;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACpD,CAAC,gBAEF1R,OAAA;kCACE4Q,SAAS,EAAC,2EAA2E;kCACrFzI,KAAK,EAAE;oCACLsK,UAAU,EAAE,SAAS;oCACrBhO,KAAK,EAAE,SAAS;oCAChB8N,QAAQ,EAAE;kCACZ,CAAE;kCAAA1B,QAAA,EAED2E,QAAQ,CAAC7T,IAAI,CAAC0T,MAAM,CAAC,CAAC,CAAC,CAAChC,WAAW,CAAC;gCAAC;kCAAA9B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAEL7B,aAAa,iBACZ7P,OAAA;gCACE4Q,SAAS,EAAC,8FAA8F;gCACxGzI,KAAK,EAAE;kCACLsK,UAAU,EAAE,0CAA0C;kCACtDnK,SAAS,EAAE;gCACb,CAAE;gCAAAuI,QAAA,eAEF7Q,OAAA,CAAChB,MAAM;kCAAC4R,SAAS,EAAC;gCAA2B;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN1R,OAAA;4BAAK4Q,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClC7Q,OAAA;8BAAK4Q,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExB7Q,OAAA;gCAAK4Q,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3C7Q,OAAA;kCACE4Q,SAAS,EAAC,yCAAyC;kCACnDzI,KAAK,EAAE;oCACL1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC7B,SAAS;oCAC9BkP,UAAU,EAAG,eAAc0B,QAAQ,CAAC/O,IAAI,CAAC5B,WAAY,EAAC;oCACtDoC,MAAM,EAAE;kCACV,CAAE;kCAAA4J,QAAA,EAED2E,QAAQ,CAAC7T;gCAAI;kCAAA4P,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJ7B,aAAa,iBACZ7P,OAAA;kCACE4Q,SAAS,EAAC,yDAAyD;kCACnEzI,KAAK,EAAE;oCACLsK,UAAU,EAAE,mDAAmD;oCAC/DhO,KAAK,EAAE,SAAS;oCAChB6D,SAAS,EAAE,8DAA8D;oCACzE4L,MAAM,EAAE,mBAAmB;oCAC3BJ,UAAU,EAAE,6BAA6B;oCACzCvB,QAAQ,EAAE,MAAM;oCAChBkC,UAAU,EAAE;kCACd,CAAE;kCAAA5D,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN1R,OAAA;gCAAK4Q,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1C2E,QAAQ,CAAC1M,KAAK,EAAC,gBAAS,EAAC0M,QAAQ,CAAC9L,KAAK;8BAAA;gCAAA6H,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN1R,OAAA;4BAAK4Q,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1D7Q,OAAA;8BACE4Q,SAAS,EAAC,oCAAoC;8BAC9CzI,KAAK,EAAE;gCACL1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC7B,SAAS;gCAC9BkP,UAAU,EAAG,eAAc0B,QAAQ,CAAC/O,IAAI,CAAC5B,WAAY,EAAC;gCACtDoC,MAAM,EAAE;8BACV,CAAE;8BAAA4J,QAAA,GAED2E,QAAQ,CAAC3T,OAAO,CAACoR,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGN1R,OAAA;8BAAK4Q,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9C7Q,OAAA;gCACE4Q,SAAS,EAAC,8CAA8C;gCACxDzI,KAAK,EAAE;kCACLuN,eAAe,EAAG,GAAEF,QAAQ,CAAC/O,IAAI,CAACvB,WAAY,IAAG;kCACjDT,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC9B;gCACvB,CAAE;gCAAAkM,QAAA,gBAEF7Q,OAAA,CAACd,OAAO;kCAAC0R,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B1R,OAAA;kCAAM4Q,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE2E,QAAQ,CAACtT;gCAAiB;kCAAAqP,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACN1R,OAAA;gCACE4Q,SAAS,EAAC,8CAA8C;gCACxDzI,KAAK,EAAE;kCACLuN,eAAe,EAAE,WAAW;kCAC5BjR,KAAK,EAAE;gCACT,CAAE;gCAAAoM,QAAA,gBAEF7Q,OAAA,CAACf,OAAO;kCAAC2R,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B1R,OAAA;kCAAM4Q,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE2E,QAAQ,CAACzT;gCAAa;kCAAAwP,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvLD8D,QAAQ,CAAC9T,GAAG;sBAAA6P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwLP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACArP,MAAM,CAACC,IAAI,CAACwB,YAAY,CAAC,CAACwD,MAAM,GAAG,CAAC,iBAClCtH,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cACtC/D,EAAE,EAAC,yBAAyB;cAAAgE,QAAA,gBAG5B7Q,OAAA;gBAAK4Q,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC7Q,OAAA,CAACvB,MAAM,CAAC8W,EAAE;kBACR3E,SAAS,EAAC,kDAAkD;kBAC5DzI,KAAK,EAAE;oBACLsK,UAAU,EAAE,mDAAmD;oBAC/DmB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzC7M,MAAM,EAAE;kBACV,CAAE;kBACFgK,OAAO,EAAE;oBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE8I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ1R,OAAA;kBAAG4Q,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN1R,OAAA;gBAAK4Q,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CtI,iBAAiB,CAAC,CAAC,CAACgB,GAAG,CAAE7C,SAAS,IAAK;kBACtC,MAAMT,MAAM,GAAG3B,YAAY,CAACoC,SAAS,CAAC;kBACtC,MAAMsI,UAAU,GAAGlL,YAAY,CAAC4C,SAAS,CAAC;kBAC1C,MAAMiP,QAAQ,GAAG3G,UAAU,CAACzO,KAAK,CAACqM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACE5M,OAAA,CAACvB,MAAM,CAACqS,GAAG;oBAETmE,GAAG,EAAGW,EAAE,IAAM5R,UAAU,CAAC8D,OAAO,CAACpB,SAAS,CAAC,GAAGkP,EAAI;oBAClD7E,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEa,CAAC,EAAE;oBAAG,CAAE;oBAC/BZ,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEa,CAAC,EAAE;oBAAE,CAAE;oBAC9BxJ,UAAU,EAAE;sBAAE0J,KAAK,EAAE,GAAG;sBAAEZ,QAAQ,EAAE;oBAAI,CAAE;oBAC1CP,SAAS,EAAC,mGAAmG;oBAC7G/D,EAAE,EAAG,UAASnG,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAmK,QAAA,gBAGvB7Q,OAAA;sBAAK4Q,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD7Q,OAAA;wBAAK4Q,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC7Q,OAAA;0BACE4Q,SAAS,EAAC,gEAAgE;0BAC1EzI,KAAK,EAAE;4BACLsK,UAAU,EAAG,2BAA0BxM,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrFuP,MAAM,EAAG,aAAYjO,MAAM,CAACf,WAAY,IAAG;4BAC3CoD,SAAS,EAAG,cAAarC,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAgM,QAAA,EAED5K,MAAM,CAACb;wBAAU;0BAAAmM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACN1R,OAAA;0BAAA6Q,QAAA,gBACE7Q,OAAA;4BACE4Q,SAAS,EAAC,0BAA0B;4BACpCzI,KAAK,EAAE;8BACL1D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBkP,UAAU,EAAG,eAAc7N,MAAM,CAACpB,WAAY,EAAC;8BAC/CoC,MAAM,EAAE;4BACV,CAAE;4BAAA4J,QAAA,GAED5K,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAAuM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL1R,OAAA;4BAAG4Q,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjC7B,UAAU,CAACzO,KAAK,CAAC+G,MAAM,EAAC,oBAAa,EAACrB,MAAM,CAAChB,WAAW;0BAAA;4BAAAsM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN1R,OAAA,CAACvB,MAAM,CAACyT,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BE,OAAO,EAAEA,CAAA,KAAM/K,kBAAkB,CAACb,SAAS,CAAE;wBAC7CkK,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAAC7B,UAAU,CAACzO,KAAK,CAAC+G,MAAM,EAAC,GACrC;sBAAA;wBAAAiK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGN1R,OAAA;sBAAK4Q,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClE8E,QAAQ,CAACpM,GAAG,CAAC,CAACiM,QAAQ,EAAEhM,KAAK,KAAK;wBACjC,MAAMqG,aAAa,GAAGpP,IAAI,IAAI+U,QAAQ,CAAC9T,GAAG,KAAKjB,IAAI,CAACiB,GAAG;wBACvD,MAAMmU,UAAU,GAAGrM,KAAK,GAAG,CAAC;wBAE5B,oBACExJ,OAAA,CAACvB,MAAM,CAACqS,GAAG;0BAET,gBAAc0E,QAAQ,CAAC9T,GAAI;0BAC3B,kBAAgBmU,UAAW;0BAC3B9E,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEoB,KAAK,EAAE;0BAAI,CAAE;0BACpCnB,OAAO,EAAE;4BAAED,OAAO,EAAE,CAAC;4BAAEoB,KAAK,EAAE;0BAAE,CAAE;0BAClC/J,UAAU,EAAE;4BAAE0J,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,GAAG;4BAAE2H,QAAQ,EAAE;0BAAI,CAAE;0BACxDgB,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnCjB,SAAS,EAAG,YACVf,aAAa,GACT,2BAA2B,GAC3B,EACL,EAAE;0BAAAgB,QAAA,eAEH7Q,OAAA;4BACE4Q,SAAS,EAAG,qBAAoB4E,QAAQ,CAAC/O,IAAI,CAAChC,KAAM,qBAAoB+Q,QAAQ,CAAC/O,IAAI,CAAC3B,IAAK,YAAY;4BACvGqD,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAakN,QAAQ,CAAC/O,IAAI,CAAC5B,WAAY;4BACrD,CAAE;4BAAAgM,QAAA,eAEF7Q,OAAA;8BACE4Q,SAAS,EAAG,GAAE4E,QAAQ,CAAC/O,IAAI,CAAC/B,OAAQ,uEAAuE;8BAAAmM,QAAA,gBAE3G7Q,OAAA;gCAAK4Q,SAAS,EAAC;8BAAgE;gCAAAW,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtF1R,OAAA;gCACE4Q,SAAS,EAAC,mGAAmG;gCAC7GzI,KAAK,EAAE;kCACLsK,UAAU,EAAExM,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChByP,MAAM,EAAE;gCACV,CAAE;gCAAArD,QAAA,GACH,GACE,EAACgF,UAAU;8BAAA;gCAAAtE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGN1R,OAAA;gCAAK4Q,SAAS,EAAG,yBACff,aAAa,GACT,wCAAwC,GACxC,EACL,EAAE;gCAAAgB,QAAA,gBACD7Q,OAAA;kCACE4Q,SAAS,EAAC,wEAAwE;kCAClFzI,KAAK,EAAE;oCACLsK,UAAU,EAAE,SAAS;oCACrBnK,SAAS,EAAE,4BAA4B;oCACvC8M,KAAK,EAAE,MAAM;oCACbF,MAAM,EAAE;kCACV,CAAE;kCAAArE,QAAA,EAED2E,QAAQ,CAACrT,cAAc,gBACtBnC,OAAA;oCACE4S,GAAG,EAAE4C,QAAQ,CAACrT,cAAe;oCAC7B0Q,GAAG,EAAE2C,QAAQ,CAAC7T,IAAK;oCACnBiP,SAAS,EAAC;kCAAyC;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpD,CAAC,gBAEF1R,OAAA;oCACE4Q,SAAS,EAAC,2EAA2E;oCACrFzI,KAAK,EAAE;sCACLsK,UAAU,EAAE,SAAS;sCACrBhO,KAAK,EAAE,SAAS;sCAChB8N,QAAQ,EAAE;oCACZ,CAAE;oCAAA1B,QAAA,EAED2E,QAAQ,CAAC7T,IAAI,CAAC0T,MAAM,CAAC,CAAC,CAAC,CAAChC,WAAW,CAAC;kCAAC;oCAAA9B,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACnC;gCACN;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,EACL7B,aAAa,iBACZ7P,OAAA;kCACE4Q,SAAS,EAAC,iGAAiG;kCAC3GzI,KAAK,EAAE;oCACLsK,UAAU,EAAE,0CAA0C;oCACtDnK,SAAS,EAAE;kCACb,CAAE;kCAAAuI,QAAA,eAEF7Q,OAAA,CAAChB,MAAM;oCAAC4R,SAAS,EAAC;kCAA2B;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN1R,OAAA;gCACE4Q,SAAS,EAAC,iCAAiC;gCAC3CzI,KAAK,EAAE;kCAAE1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC7B;gCAAU,CAAE;gCAAAiM,QAAA,GAEzC2E,QAAQ,CAAC7T,IAAI,EACbkO,aAAa,iBACZ7P,OAAA;kCAAM4Q,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAEL1R,OAAA;gCAAK4Q,SAAS,EAAC,yBAAyB;gCAACzI,KAAK,EAAE;kCAAE1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC9B;gCAAU,CAAE;gCAAAkM,QAAA,GAChF2E,QAAQ,CAAC3T,OAAO,CAACoR,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAA1B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAEN1R,OAAA;gCAAK4Q,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChD7Q,OAAA;kCAAMmI,KAAK,EAAE;oCAAE1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC9B;kCAAU,CAAE;kCAAAkM,QAAA,GAAC,eAC5C,EAAC2E,QAAQ,CAACtT,iBAAiB;gCAAA;kCAAAqP,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACP1R,OAAA;kCAAMmI,KAAK,EAAE;oCAAE1D,KAAK,EAAE+Q,QAAQ,CAAC/O,IAAI,CAAC9B;kCAAU,CAAE;kCAAAkM,QAAA,GAAC,eAC5C,EAAC2E,QAAQ,CAACzT,aAAa;gCAAA;kCAAAwP,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GA3GD8D,QAAQ,CAAC9T,GAAG;0BAAA6P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA4GP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGL1C,UAAU,CAACzO,KAAK,CAAC+G,MAAM,GAAG,CAAC,iBAC1BtH,OAAA;sBAAK4Q,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/B7Q,OAAA;wBAAG4Q,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC7B,UAAU,CAACzO,KAAK,CAAC+G,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAAiK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GAhLIhL,SAAS;oBAAA6K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiLJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMAlP,WAAW,CAAC8E,MAAM,GAAG,CAAC,iBACrBtH,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJ7Q,OAAA;gBAAK4Q,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7Q,OAAA;kBAAI4Q,SAAS,EAAC,wBAAwB;kBAACzI,KAAK,EAAE;oBAC5C1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC1R,OAAA;kBAAK4Q,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5D7Q,OAAA;oBAAK4Q,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C7Q,OAAA;sBAAK4Q,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CrO,WAAW,CAACyE,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,SAAS,CAAC,CAAC/C;oBAAM;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN1R,OAAA;sBAAK4Q,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN1R,OAAA;oBAAK4Q,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5C7Q,OAAA;sBAAK4Q,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CrO,WAAW,CAACyE,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,eAAe,CAAC,CAAC/C;oBAAM;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN1R,OAAA;sBAAK4Q,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN1R,OAAA;oBAAK4Q,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9C7Q,OAAA;sBAAK4Q,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CrO,WAAW,CAACyE,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK,WAAW,CAAC,CAAC/C;oBAAM;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN1R,OAAA;sBAAK4Q,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1R,OAAA;kBAAG4Q,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGA9O,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrC5C,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAI,CAAE;cACpCnB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cAClC/J,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJ7Q,OAAA;gBAAK4Q,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7Q,OAAA;kBAAI4Q,SAAS,EAAC,yBAAyB;kBAACzI,KAAK,EAAE;oBAC7C1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B1R,OAAA;kBAAK4Q,SAAS,EAAC,0BAA0B;kBAACzI,KAAK,EAAE;oBAC/C1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,GAAC,GAAC,EAACjO,eAAe;gBAAA;kBAAA2O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3B1R,OAAA;kBAAG4Q,SAAS,EAAC,SAAS;kBAACzI,KAAK,EAAE;oBAC5B1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGD1R,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAG,CAAE;cAC/BZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEZ,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7B7Q,OAAA;gBAAK4Q,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3I7Q,OAAA,CAACvB,MAAM,CAACqS,GAAG;kBACTG,OAAO,EAAE;oBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE8I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9C7Q,OAAA,CAACV,QAAQ;oBAACsR,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACb1R,OAAA;kBAAI4Q,SAAS,EAAC,yBAAyB;kBAACzI,KAAK,EAAE;oBAC7C1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B1R,OAAA;kBAAG4Q,SAAS,EAAC,gCAAgC;kBAACzI,KAAK,EAAE;oBACnD1D,KAAK,EAAE,SAAS;oBAChBqP,UAAU,EAAE,6BAA6B;oBACzCW,UAAU,EAAE;kBACd,CAAE;kBAAA5D,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ1R,OAAA,CAACvB,MAAM,CAACyT,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BxB,SAAS,EAAC,sJAAsJ;kBAChK0B,OAAO,EAAEA,CAAA,KAAM1D,MAAM,CAACkH,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAlF,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZlP,WAAW,CAAC8E,MAAM,KAAK,CAAC,IAAI,CAAC5E,OAAO,iBACnC1C,OAAA,CAACvB,MAAM,CAACqS,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAI,CAAE;cACpCnB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cAClCxB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7B7Q,OAAA,CAAClB,QAAQ;gBAAC8R,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7D1R,OAAA;gBAAI4Q,SAAS,EAAC,yBAAyB;gBAACzI,KAAK,EAAE;kBAC7C1D,KAAK,EAAE,SAAS;kBAChBqP,UAAU,EAAE,6BAA6B;kBACzCW,UAAU,EAAE;gBACd,CAAE;gBAAA5D,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB1R,OAAA;gBAAG4Q,SAAS,EAAC,SAAS;gBAACzI,KAAK,EAAE;kBAC5B1D,KAAK,EAAE,SAAS;kBAChBqP,UAAU,EAAE,6BAA6B;kBACzCW,UAAU,EAAE;gBACd,CAAE;gBAAA5D,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACtR,EAAA,CA7vFID,kBAAkB;EAAA,QACJxB,WAAW,EAqDZC,WAAW;AAAA;AAAAoX,EAAA,GAtDxB7V,kBAAkB;AA+vFxB,eAAeA,kBAAkB;AAAC,IAAA6V,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}