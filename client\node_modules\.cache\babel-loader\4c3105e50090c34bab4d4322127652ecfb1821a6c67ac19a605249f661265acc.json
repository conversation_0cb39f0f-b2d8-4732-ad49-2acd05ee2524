{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Users\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport \"./index.css\";\nimport { getAllUsers, blockUserById, deleteUserById } from \"../../../apicalls/users\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\nimport { TbU<PERSON><PERSON>, Tb<PERSON>earch, Tb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>ol, TbMail, <PERSON>b<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TbX } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Users() {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [filteredUsers, setFilteredUsers] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [filterSubscription, setFilterSubscription] = useState(\"all\");\n  const [loading, setLoading] = useState(false);\n  const dispatch = useDispatch();\n\n  // Function to determine subscription status for filtering based on paymentRequired and activity\n  const getSubscriptionFilterStatus = user => {\n    var _user$activityTrackin;\n    const now = new Date();\n    const paymentRequired = user.paymentRequired;\n    const subscriptionEndDate = user.subscriptionEndDate;\n    const subscriptionStartDate = user.subscriptionStartDate;\n\n    // Get the most recent activity date from multiple sources\n    const lastLoginDate = user.lastLoginDate || ((_user$activityTrackin = user.activityTracking) === null || _user$activityTrackin === void 0 ? void 0 : _user$activityTrackin.lastLoginDate);\n    const lastActivity = user.lastActivity || lastLoginDate || user.updatedAt || user.createdAt;\n    const totalQuizzesTaken = user.totalQuizzesTaken || 0;\n\n    // Calculate if user is recently active (activity in last 30 days)\n    const isRecentlyActive = lastActivity ? now - new Date(lastActivity) <= 30 * 24 * 60 * 60 * 1000 : false;\n\n    // Calculate if user has taken quizzes recently (more specific activity)\n    const hasRecentQuizActivity = totalQuizzesTaken > 0 && isRecentlyActive;\n    console.log(`User ${user.name}:`, {\n      paymentRequired,\n      subscriptionEndDate,\n      subscriptionStartDate,\n      lastActivity,\n      totalQuizzesTaken,\n      isRecentlyActive,\n      hasRecentQuizActivity,\n      daysSinceActivity: lastActivity ? Math.floor((now - new Date(lastActivity)) / (24 * 60 * 60 * 1000)) : 'unknown'\n    });\n\n    // NO-PLAN: Users who never required payment or never had subscription\n    if (!paymentRequired) {\n      return 'no-plan';\n    }\n\n    // ON-PLAN: Users with paymentRequired = true\n    if (paymentRequired) {\n      // Check if subscription has expired by date\n      if (subscriptionEndDate) {\n        const endDate = new Date(subscriptionEndDate);\n        if (endDate < now) {\n          // Subscription date has passed - EXPIRED\n          return 'expired-plan';\n        } else {\n          // Subscription is still valid by date\n          // Additional check: Are they actively taking quizzes?\n          if (hasRecentQuizActivity) {\n            return 'on-plan';\n          } else if (isRecentlyActive) {\n            // Active but not taking quizzes - still on plan but maybe not engaged\n            return 'on-plan';\n          } else {\n            // Valid subscription but completely inactive - expired\n            return 'expired-plan';\n          }\n        }\n      } else {\n        // No end date specified, check activity to determine status\n        if (hasRecentQuizActivity) {\n          return 'on-plan';\n        } else if (isRecentlyActive) {\n          // Active but no quiz activity - might be new subscriber\n          return 'on-plan';\n        } else {\n          // Has payment required but no recent activity - likely expired\n          return 'expired-plan';\n        }\n      }\n    }\n\n    // Default fallback\n    return 'no-plan';\n  };\n  const getUsersData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllUsers();\n      dispatch(HideLoading());\n      if (response.success) {\n        setUsers(response.users);\n        console.log(\"users loaded:\", response.users.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const blockUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await blockUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(response.message);\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const deleteUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(\"User deleted successfully\");\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Filter users based on search, status, and subscription\n  useEffect(() => {\n    console.log('Filtering users:', {\n      totalUsers: users.length,\n      searchQuery,\n      filterStatus,\n      filterSubscription\n    });\n    let filtered = users;\n\n    // Filter by search query\n    if (searchQuery) {\n      filtered = filtered.filter(user => {\n        var _user$name, _user$email, _user$school, _user$class;\n        return ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchQuery.toLowerCase())) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchQuery.toLowerCase())) || ((_user$school = user.school) === null || _user$school === void 0 ? void 0 : _user$school.toLowerCase().includes(searchQuery.toLowerCase())) || ((_user$class = user.class) === null || _user$class === void 0 ? void 0 : _user$class.toLowerCase().includes(searchQuery.toLowerCase()));\n      });\n      console.log('After search filter:', filtered.length);\n    }\n\n    // Filter by status\n    if (filterStatus !== \"all\") {\n      filtered = filtered.filter(user => {\n        if (filterStatus === \"blocked\") return user.isBlocked;\n        if (filterStatus === \"active\") return !user.isBlocked;\n        return true;\n      });\n      console.log('After status filter:', filtered.length);\n    }\n\n    // Filter by subscription plan\n    if (filterSubscription !== \"all\") {\n      console.log('Applying subscription filter:', filterSubscription);\n      const beforeCount = filtered.length;\n      filtered = filtered.filter(user => {\n        const subscriptionStatus = getSubscriptionFilterStatus(user);\n        const matches = subscriptionStatus === filterSubscription;\n        console.log(`User ${user.name}: ${subscriptionStatus} === ${filterSubscription} = ${matches}`);\n        return matches;\n      });\n      console.log(`After subscription filter: ${filtered.length} (was ${beforeCount})`);\n    }\n    console.log('Final filtered users:', filtered.length);\n    setFilteredUsers(filtered);\n  }, [users, searchQuery, filterStatus, filterSubscription]);\n  useEffect(() => {\n    getUsersData();\n  }, []);\n  const UserCard = ({\n    user\n  }) => {\n    const subscriptionStatus = getSubscriptionFilterStatus(user);\n    const getSubscriptionBadge = () => {\n      switch (subscriptionStatus) {\n        case 'on-plan':\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbCrown, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"On Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this);\n        case 'expired-plan':\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this);\n        case 'no-plan':\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-3 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"No Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this);\n        default:\n          return null;\n      }\n    };\n    const formatDate = dateString => {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString();\n    };\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      whileHover: {\n        y: -2\n      },\n      transition: {\n        duration: 0.2\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"p-6 hover:shadow-large\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 rounded-full flex items-center justify-center ${user.isBlocked ? 'bg-error-100' : 'bg-primary-100'}`,\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: `w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `badge-modern ${user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'}`,\n                  children: user.isBlocked ? 'Blocked' : 'Active'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), getSubscriptionBadge()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-1 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbMail, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: user.school || 'No school specified'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class: \", user.class || 'Not assigned']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), user.subscriptionPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbCrown, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Plan: \", user.subscriptionPlan]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this), user.subscriptionEndDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Expires: \", formatDate(user.subscriptionEndDate)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), user.totalQuizzesTaken > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbUser, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Quizzes: \", user.totalQuizzesTaken]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), user.lastActivity && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Last Active: \", formatDate(user.lastActivity)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: user.isBlocked ? \"success\" : \"warning\",\n              size: \"sm\",\n              onClick: () => blockUser(user.studentId),\n              icon: user.isBlocked ? /*#__PURE__*/_jsxDEV(TbUserCheck, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 40\n              }, this) : /*#__PURE__*/_jsxDEV(TbUserX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 58\n              }, this),\n              children: user.isBlocked ? \"Unblock\" : \"Block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"error\",\n              size: \"sm\",\n              onClick: () => {\n                if (window.confirm(\"Are you sure you want to delete this user?\")) {\n                  deleteUser(user.studentId);\n                }\n              },\n              icon: /*#__PURE__*/_jsxDEV(TbTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 23\n              }, this),\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"heading-2 text-gradient flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-8 h-8 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), \"User Management\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-2\",\n              children: \"Manage student accounts, permissions, and access controls\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-4 text-center min-w-[120px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-primary-600\",\n                children: users.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-4 text-center min-w-[120px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-success-600\",\n                children: users.filter(u => !u.isBlocked).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-4 text-center min-w-[120px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: users.filter(u => getSubscriptionFilterStatus(u) === 'on-plan').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"On Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-4 text-center min-w-[120px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-orange-600\",\n                children: users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Expired\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-4 text-center min-w-[120px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-600\",\n                children: users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row gap-4 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Search Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"Search by name, email, school, or class...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                icon: /*#__PURE__*/_jsxDEV(TbSearch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-48\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Filter by Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterStatus,\n                onChange: e => setFilterStatus(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"blocked\",\n                  children: \"Blocked Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-48\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Filter by Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterSubscription,\n                onChange: e => setFilterSubscription(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Plans\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"on-plan\",\n                  children: \"On Plan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"expired-plan\",\n                  children: \"Expired Plan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"no-plan\",\n                  children: \"No Plan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              icon: /*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 23\n              }, this),\n              onClick: () => {\n                setSearchQuery(\"\");\n                setFilterStatus(\"all\");\n                setFilterSubscription(\"all\");\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), (searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 pt-4 border-t border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Showing \", filteredUsers.length, \" of \", users.length, \" users\", filterSubscription !== \"all\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\",\n                children: [filterSubscription === 'on-plan' && 'On Plan', filterSubscription === 'expired-plan' && 'Expired Plan', filterSubscription === 'no-plan' && 'No Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center py-12\",\n          children: /*#__PURE__*/_jsxDEV(Loading, {\n            text: \"Loading users...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this) : filteredUsers.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: filteredUsers.map((user, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(UserCard, {\n              user: user\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 19\n            }, this)\n          }, user.studentId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-12 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n            className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"No Users Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\" ? \"Try adjusting your search or filter criteria\" : \"No users have been registered yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n}\n_s(Users, \"d7WOEx4TVt71LTPBPUZ4mHBKjew=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useDispatch", "useNavigate", "motion", "getAllUsers", "blockUserById", "deleteUserById", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Input", "Loading", "TbUsers", "TbSearch", "Tb<PERSON><PERSON>er", "TbUserCheck", "TbUserX", "TbTrash", "TbEye", "TbSchool", "TbMail", "TbUser", "TbCrown", "TbClock", "TbX", "jsxDEV", "_jsxDEV", "Users", "_s", "navigate", "users", "setUsers", "filteredUsers", "setFilteredUsers", "searchQuery", "setSearch<PERSON>uery", "filterStatus", "setFilterStatus", "filterSubscription", "setFilterSubscription", "loading", "setLoading", "dispatch", "getSubscriptionFilterStatus", "user", "_user$activityTrackin", "now", "Date", "paymentRequired", "subscriptionEndDate", "subscriptionStartDate", "lastLoginDate", "activityTracking", "lastActivity", "updatedAt", "createdAt", "totalQuizzesTaken", "isRecentlyActive", "hasRecentQuizActivity", "console", "log", "name", "daysSinceActivity", "Math", "floor", "endDate", "getUsersData", "response", "success", "length", "error", "blockUser", "studentId", "deleteUser", "totalUsers", "filtered", "filter", "_user$name", "_user$email", "_user$school", "_user$class", "toLowerCase", "includes", "email", "school", "class", "isBlocked", "beforeCount", "subscriptionStatus", "matches", "UserCard", "getSubscriptionBadge", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "toLocaleDateString", "div", "initial", "opacity", "y", "animate", "whileHover", "transition", "duration", "subscriptionPlan", "variant", "size", "onClick", "icon", "window", "confirm", "u", "delay", "placeholder", "value", "onChange", "e", "target", "text", "map", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Users/<USER>"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport \"./index.css\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  TbTrash,\r\n  TbEye,\r\n  TbSchool,\r\n  TbMail,\r\n  TbUser,\r\n  TbCrown,\r\n  TbClock,\r\n  TbX\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [filterSubscription, setFilterSubscription] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  // Function to determine subscription status for filtering based on paymentRequired and activity\r\n  const getSubscriptionFilterStatus = (user) => {\r\n    const now = new Date();\r\n    const paymentRequired = user.paymentRequired;\r\n    const subscriptionEndDate = user.subscriptionEndDate;\r\n    const subscriptionStartDate = user.subscriptionStartDate;\r\n\r\n    // Get the most recent activity date from multiple sources\r\n    const lastLoginDate = user.lastLoginDate || user.activityTracking?.lastLoginDate;\r\n    const lastActivity = user.lastActivity || lastLoginDate || user.updatedAt || user.createdAt;\r\n    const totalQuizzesTaken = user.totalQuizzesTaken || 0;\r\n\r\n    // Calculate if user is recently active (activity in last 30 days)\r\n    const isRecentlyActive = lastActivity ?\r\n      (now - new Date(lastActivity)) <= (30 * 24 * 60 * 60 * 1000) : false;\r\n\r\n    // Calculate if user has taken quizzes recently (more specific activity)\r\n    const hasRecentQuizActivity = totalQuizzesTaken > 0 && isRecentlyActive;\r\n\r\n    console.log(`User ${user.name}:`, {\r\n      paymentRequired,\r\n      subscriptionEndDate,\r\n      subscriptionStartDate,\r\n      lastActivity,\r\n      totalQuizzesTaken,\r\n      isRecentlyActive,\r\n      hasRecentQuizActivity,\r\n      daysSinceActivity: lastActivity ? Math.floor((now - new Date(lastActivity)) / (24 * 60 * 60 * 1000)) : 'unknown'\r\n    });\r\n\r\n    // NO-PLAN: Users who never required payment or never had subscription\r\n    if (!paymentRequired) {\r\n      return 'no-plan';\r\n    }\r\n\r\n    // ON-PLAN: Users with paymentRequired = true\r\n    if (paymentRequired) {\r\n      // Check if subscription has expired by date\r\n      if (subscriptionEndDate) {\r\n        const endDate = new Date(subscriptionEndDate);\r\n\r\n        if (endDate < now) {\r\n          // Subscription date has passed - EXPIRED\r\n          return 'expired-plan';\r\n        } else {\r\n          // Subscription is still valid by date\r\n          // Additional check: Are they actively taking quizzes?\r\n          if (hasRecentQuizActivity) {\r\n            return 'on-plan';\r\n          } else if (isRecentlyActive) {\r\n            // Active but not taking quizzes - still on plan but maybe not engaged\r\n            return 'on-plan';\r\n          } else {\r\n            // Valid subscription but completely inactive - expired\r\n            return 'expired-plan';\r\n          }\r\n        }\r\n      } else {\r\n        // No end date specified, check activity to determine status\r\n        if (hasRecentQuizActivity) {\r\n          return 'on-plan';\r\n        } else if (isRecentlyActive) {\r\n          // Active but no quiz activity - might be new subscriber\r\n          return 'on-plan';\r\n        } else {\r\n          // Has payment required but no recent activity - likely expired\r\n          return 'expired-plan';\r\n        }\r\n      }\r\n    }\r\n\r\n    // Default fallback\r\n    return 'no-plan';\r\n  };\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users loaded:\", response.users.length);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // Filter users based on search, status, and subscription\r\n  useEffect(() => {\r\n    console.log('Filtering users:', {\r\n      totalUsers: users.length,\r\n      searchQuery,\r\n      filterStatus,\r\n      filterSubscription\r\n    });\r\n\r\n    let filtered = users;\r\n\r\n    // Filter by search query\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(user =>\r\n        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.class?.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n      console.log('After search filter:', filtered.length);\r\n    }\r\n\r\n    // Filter by status\r\n    if (filterStatus !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        if (filterStatus === \"blocked\") return user.isBlocked;\r\n        if (filterStatus === \"active\") return !user.isBlocked;\r\n        return true;\r\n      });\r\n      console.log('After status filter:', filtered.length);\r\n    }\r\n\r\n    // Filter by subscription plan\r\n    if (filterSubscription !== \"all\") {\r\n      console.log('Applying subscription filter:', filterSubscription);\r\n      const beforeCount = filtered.length;\r\n      filtered = filtered.filter(user => {\r\n        const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n        const matches = subscriptionStatus === filterSubscription;\r\n        console.log(`User ${user.name}: ${subscriptionStatus} === ${filterSubscription} = ${matches}`);\r\n        return matches;\r\n      });\r\n      console.log(`After subscription filter: ${filtered.length} (was ${beforeCount})`);\r\n    }\r\n\r\n    console.log('Final filtered users:', filtered.length);\r\n    setFilteredUsers(filtered);\r\n  }, [users, searchQuery, filterStatus, filterSubscription]);\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const UserCard = ({ user }) => {\r\n    const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n\r\n    const getSubscriptionBadge = () => {\r\n      switch (subscriptionStatus) {\r\n        case 'on-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\">\r\n              <TbCrown className=\"w-3 h-3\" />\r\n              <span>On Plan</span>\r\n            </span>\r\n          );\r\n        case 'expired-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\">\r\n              <TbClock className=\"w-3 h-3\" />\r\n              <span>Expired</span>\r\n            </span>\r\n          );\r\n        case 'no-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\">\r\n              <TbX className=\"w-3 h-3\" />\r\n              <span>No Plan</span>\r\n            </span>\r\n          );\r\n        default:\r\n          return null;\r\n      }\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return 'N/A';\r\n      return new Date(dateString).toLocaleDateString();\r\n    };\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -2 }}\r\n        transition={{ duration: 0.2 }}\r\n      >\r\n        <Card className=\"p-6 hover:shadow-large\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex items-start space-x-4\">\r\n              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\r\n                user.isBlocked ? 'bg-error-100' : 'bg-primary-100'\r\n              }`}>\r\n                <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex items-center space-x-2 mb-2\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{user.name}</h3>\r\n                  <span className={`badge-modern ${\r\n                    user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'\r\n                  }`}>\r\n                    {user.isBlocked ? 'Blocked' : 'Active'}\r\n                  </span>\r\n                  {getSubscriptionBadge()}\r\n                </div>\r\n\r\n                <div className=\"space-y-1 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbMail className=\"w-4 h-4\" />\r\n                    <span>{user.email}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbSchool className=\"w-4 h-4\" />\r\n                    <span>{user.school || 'No school specified'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbUsers className=\"w-4 h-4\" />\r\n                    <span>Class: {user.class || 'Not assigned'}</span>\r\n                  </div>\r\n\r\n                  {/* Subscription Details */}\r\n                  {user.subscriptionPlan && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span>Plan: {user.subscriptionPlan}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.subscriptionEndDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Expires: {formatDate(user.subscriptionEndDate)}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Activity Information */}\r\n                  {user.totalQuizzesTaken > 0 && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbUser className=\"w-4 h-4\" />\r\n                      <span>Quizzes: {user.totalQuizzesTaken}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.lastActivity && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Last Active: {formatDate(user.lastActivity)}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button\r\n                variant={user.isBlocked ? \"success\" : \"warning\"}\r\n                size=\"sm\"\r\n                onClick={() => blockUser(user.studentId)}\r\n                icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}\r\n              >\r\n                {user.isBlocked ? \"Unblock\" : \"Block\"}\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"error\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  if (window.confirm(\"Are you sure you want to delete this user?\")) {\r\n                    deleteUser(user.studentId);\r\n                  }\r\n                }}\r\n                icon={<TbTrash />}\r\n              >\r\n                Delete\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\">\r\n      <div className=\"container-modern\">\r\n        {/* Modern Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h1 className=\"heading-2 text-gradient flex items-center\">\r\n                <TbUsers className=\"w-8 h-8 mr-3\" />\r\n                User Management\r\n              </h1>\r\n              <p className=\"text-gray-600 mt-2\">\r\n                Manage student accounts, permissions, and access controls\r\n              </p>\r\n            </div>\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"hidden lg:flex space-x-4\">\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-primary-600\">{users.length}</div>\r\n                <div className=\"text-sm text-gray-500\">Total Users</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-success-600\">\r\n                  {users.filter(u => !u.isBlocked).length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Active</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-green-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'on-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">On Plan</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-orange-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Expired</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-gray-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">No Plan</div>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Modern Filters */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <Card className=\"p-6\">\r\n            <div className=\"flex flex-col lg:flex-row gap-4 items-end\">\r\n              <div className=\"flex-1\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Search Users\r\n                </label>\r\n                <Input\r\n                  placeholder=\"Search by name, email, school, or class...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  icon={<TbSearch />}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"w-full lg:w-48\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Filter by Status\r\n                </label>\r\n                <select\r\n                  value={filterStatus}\r\n                  onChange={(e) => setFilterStatus(e.target.value)}\r\n                  className=\"input-modern\"\r\n                >\r\n                  <option value=\"all\">All Users</option>\r\n                  <option value=\"active\">Active Only</option>\r\n                  <option value=\"blocked\">Blocked Only</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"w-full lg:w-48\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Filter by Plan\r\n                </label>\r\n                <select\r\n                  value={filterSubscription}\r\n                  onChange={(e) => setFilterSubscription(e.target.value)}\r\n                  className=\"input-modern\"\r\n                >\r\n                  <option value=\"all\">All Plans</option>\r\n                  <option value=\"on-plan\">On Plan</option>\r\n                  <option value=\"expired-plan\">Expired Plan</option>\r\n                  <option value=\"no-plan\">No Plan</option>\r\n                </select>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"secondary\"\r\n                icon={<TbFilter />}\r\n                onClick={() => {\r\n                  setSearchQuery(\"\");\r\n                  setFilterStatus(\"all\");\r\n                  setFilterSubscription(\"all\");\r\n                }}\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n            </div>\r\n\r\n            {(searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\") && (\r\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">\r\n                  Showing {filteredUsers.length} of {users.length} users\r\n                  {filterSubscription !== \"all\" && (\r\n                    <span className=\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\">\r\n                      {filterSubscription === 'on-plan' && 'On Plan'}\r\n                      {filterSubscription === 'expired-plan' && 'Expired Plan'}\r\n                      {filterSubscription === 'no-plan' && 'No Plan'}\r\n                    </span>\r\n                  )}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Card>\r\n        </motion.div>\r\n\r\n        {/* Users Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n        >\r\n          {loading ? (\r\n            <div className=\"flex justify-center py-12\">\r\n              <Loading text=\"Loading users...\" />\r\n            </div>\r\n          ) : filteredUsers.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {filteredUsers.map((user, index) => (\r\n                <motion.div\r\n                  key={user.studentId}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                >\r\n                  <UserCard user={user} />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <Card className=\"p-12 text-center\">\r\n              <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Users Found</h3>\r\n              <p className=\"text-gray-600\">\r\n                {searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\"\r\n                  ? \"Try adjusting your search or filter criteria\"\r\n                  : \"No users have been registered yet\"}\r\n              </p>\r\n            </Card>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,aAAa;AACpB,SACEC,WAAW,EACXC,aAAa,EACbC,cAAc,QACT,yBAAyB;AAChC,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AACzE,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,GAAG,QACE,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4C,2BAA2B,GAAIC,IAAI,IAAK;IAAA,IAAAC,qBAAA;IAC5C,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,eAAe,GAAGJ,IAAI,CAACI,eAAe;IAC5C,MAAMC,mBAAmB,GAAGL,IAAI,CAACK,mBAAmB;IACpD,MAAMC,qBAAqB,GAAGN,IAAI,CAACM,qBAAqB;;IAExD;IACA,MAAMC,aAAa,GAAGP,IAAI,CAACO,aAAa,MAAAN,qBAAA,GAAID,IAAI,CAACQ,gBAAgB,cAAAP,qBAAA,uBAArBA,qBAAA,CAAuBM,aAAa;IAChF,MAAME,YAAY,GAAGT,IAAI,CAACS,YAAY,IAAIF,aAAa,IAAIP,IAAI,CAACU,SAAS,IAAIV,IAAI,CAACW,SAAS;IAC3F,MAAMC,iBAAiB,GAAGZ,IAAI,CAACY,iBAAiB,IAAI,CAAC;;IAErD;IACA,MAAMC,gBAAgB,GAAGJ,YAAY,GAClCP,GAAG,GAAG,IAAIC,IAAI,CAACM,YAAY,CAAC,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAK,GAAG,KAAK;;IAEtE;IACA,MAAMK,qBAAqB,GAAGF,iBAAiB,GAAG,CAAC,IAAIC,gBAAgB;IAEvEE,OAAO,CAACC,GAAG,CAAE,QAAOhB,IAAI,CAACiB,IAAK,GAAE,EAAE;MAChCb,eAAe;MACfC,mBAAmB;MACnBC,qBAAqB;MACrBG,YAAY;MACZG,iBAAiB;MACjBC,gBAAgB;MAChBC,qBAAqB;MACrBI,iBAAiB,EAAET,YAAY,GAAGU,IAAI,CAACC,KAAK,CAAC,CAAClB,GAAG,GAAG,IAAIC,IAAI,CAACM,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;IACzG,CAAC,CAAC;;IAEF;IACA,IAAI,CAACL,eAAe,EAAE;MACpB,OAAO,SAAS;IAClB;;IAEA;IACA,IAAIA,eAAe,EAAE;MACnB;MACA,IAAIC,mBAAmB,EAAE;QACvB,MAAMgB,OAAO,GAAG,IAAIlB,IAAI,CAACE,mBAAmB,CAAC;QAE7C,IAAIgB,OAAO,GAAGnB,GAAG,EAAE;UACjB;UACA,OAAO,cAAc;QACvB,CAAC,MAAM;UACL;UACA;UACA,IAAIY,qBAAqB,EAAE;YACzB,OAAO,SAAS;UAClB,CAAC,MAAM,IAAID,gBAAgB,EAAE;YAC3B;YACA,OAAO,SAAS;UAClB,CAAC,MAAM;YACL;YACA,OAAO,cAAc;UACvB;QACF;MACF,CAAC,MAAM;QACL;QACA,IAAIC,qBAAqB,EAAE;UACzB,OAAO,SAAS;QAClB,CAAC,MAAM,IAAID,gBAAgB,EAAE;UAC3B;UACA,OAAO,SAAS;QAClB,CAAC,MAAM;UACL;UACA,OAAO,cAAc;QACvB;MACF;IACF;;IAEA;IACA,OAAO,SAAS;EAClB,CAAC;EAED,MAAMS,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFxB,QAAQ,CAACnC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM4D,QAAQ,GAAG,MAAMjE,WAAW,CAAC,CAAC;MACpCwC,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvB,IAAI6D,QAAQ,CAACC,OAAO,EAAE;QACpBrC,QAAQ,CAACoC,QAAQ,CAACrC,KAAK,CAAC;QACxB6B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAACrC,KAAK,CAACuC,MAAM,CAAC;MACrD,CAAC,MAAM;QACL1E,OAAO,CAAC2E,KAAK,CAACH,QAAQ,CAACxE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACd5B,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC2E,KAAK,CAACA,KAAK,CAAC3E,OAAO,CAAC;IAC9B;EACF,CAAC;EACD,MAAM4E,SAAS,GAAG,MAAOC,SAAS,IAAK;IACrC,IAAI;MACF9B,QAAQ,CAACnC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM4D,QAAQ,GAAG,MAAMhE,aAAa,CAAC;QACnCqE;MACF,CAAC,CAAC;MACF9B,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvB,IAAI6D,QAAQ,CAACC,OAAO,EAAE;QACpBzE,OAAO,CAACyE,OAAO,CAACD,QAAQ,CAACxE,OAAO,CAAC;QACjCuE,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLvE,OAAO,CAAC2E,KAAK,CAACH,QAAQ,CAACxE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACd5B,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC2E,KAAK,CAACA,KAAK,CAAC3E,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM8E,UAAU,GAAG,MAAOD,SAAS,IAAK;IACtC,IAAI;MACF9B,QAAQ,CAACnC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM4D,QAAQ,GAAG,MAAM/D,cAAc,CAAC;QAAEoE;MAAU,CAAC,CAAC;MACpD9B,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvB,IAAI6D,QAAQ,CAACC,OAAO,EAAE;QACpBzE,OAAO,CAACyE,OAAO,CAAC,2BAA2B,CAAC;QAC5CF,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLvE,OAAO,CAAC2E,KAAK,CAACH,QAAQ,CAACxE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACd5B,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC2E,KAAK,CAACA,KAAK,CAAC3E,OAAO,CAAC;IAC9B;EACF,CAAC;;EAGD;EACAE,SAAS,CAAC,MAAM;IACd8D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9Bc,UAAU,EAAE5C,KAAK,CAACuC,MAAM;MACxBnC,WAAW;MACXE,YAAY;MACZE;IACF,CAAC,CAAC;IAEF,IAAIqC,QAAQ,GAAG7C,KAAK;;IAEpB;IACA,IAAII,WAAW,EAAE;MACfyC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAChC,IAAI;QAAA,IAAAiC,UAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,WAAA;QAAA,OAC7B,EAAAH,UAAA,GAAAjC,IAAI,CAACiB,IAAI,cAAAgB,UAAA,uBAATA,UAAA,CAAWI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC,OAAAH,WAAA,GAC5DlC,IAAI,CAACuC,KAAK,cAAAL,WAAA,uBAAVA,WAAA,CAAYG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC,OAAAF,YAAA,GAC7DnC,IAAI,CAACwC,MAAM,cAAAL,YAAA,uBAAXA,YAAA,CAAaE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC,OAAAD,WAAA,GAC9DpC,IAAI,CAACyC,KAAK,cAAAL,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC;MAAA,CAC/D,CAAC;MACDtB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,QAAQ,CAACN,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIjC,YAAY,KAAK,KAAK,EAAE;MAC1BuC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAChC,IAAI,IAAI;QACjC,IAAIR,YAAY,KAAK,SAAS,EAAE,OAAOQ,IAAI,CAAC0C,SAAS;QACrD,IAAIlD,YAAY,KAAK,QAAQ,EAAE,OAAO,CAACQ,IAAI,CAAC0C,SAAS;QACrD,OAAO,IAAI;MACb,CAAC,CAAC;MACF3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,QAAQ,CAACN,MAAM,CAAC;IACtD;;IAEA;IACA,IAAI/B,kBAAkB,KAAK,KAAK,EAAE;MAChCqB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEtB,kBAAkB,CAAC;MAChE,MAAMiD,WAAW,GAAGZ,QAAQ,CAACN,MAAM;MACnCM,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAChC,IAAI,IAAI;QACjC,MAAM4C,kBAAkB,GAAG7C,2BAA2B,CAACC,IAAI,CAAC;QAC5D,MAAM6C,OAAO,GAAGD,kBAAkB,KAAKlD,kBAAkB;QACzDqB,OAAO,CAACC,GAAG,CAAE,QAAOhB,IAAI,CAACiB,IAAK,KAAI2B,kBAAmB,QAAOlD,kBAAmB,MAAKmD,OAAQ,EAAC,CAAC;QAC9F,OAAOA,OAAO;MAChB,CAAC,CAAC;MACF9B,OAAO,CAACC,GAAG,CAAE,8BAA6Be,QAAQ,CAACN,MAAO,SAAQkB,WAAY,GAAE,CAAC;IACnF;IAEA5B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEe,QAAQ,CAACN,MAAM,CAAC;IACrDpC,gBAAgB,CAAC0C,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAAC7C,KAAK,EAAEI,WAAW,EAAEE,YAAY,EAAEE,kBAAkB,CAAC,CAAC;EAE1DzC,SAAS,CAAC,MAAM;IACdqE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwB,QAAQ,GAAGA,CAAC;IAAE9C;EAAK,CAAC,KAAK;IAC7B,MAAM4C,kBAAkB,GAAG7C,2BAA2B,CAACC,IAAI,CAAC;IAE5D,MAAM+C,oBAAoB,GAAGA,CAAA,KAAM;MACjC,QAAQH,kBAAkB;QACxB,KAAK,SAAS;UACZ,oBACE9D,OAAA;YAAMkE,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACpFnE,OAAA,CAACJ,OAAO;cAACsE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BvE,OAAA;cAAAmE,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAEX,KAAK,cAAc;UACjB,oBACEvE,OAAA;YAAMkE,SAAS,EAAC,wEAAwE;YAAAC,QAAA,gBACtFnE,OAAA,CAACH,OAAO;cAACqE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BvE,OAAA;cAAAmE,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAEX,KAAK,SAAS;UACZ,oBACEvE,OAAA;YAAMkE,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBAClFnE,OAAA,CAACF,GAAG;cAACoE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3BvE,OAAA;cAAAmE,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAEX;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAIpD,IAAI,CAACoD,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;IAClD,CAAC;IAED,oBACE1E,OAAA,CAACzB,MAAM,CAACoG,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEF,CAAC,EAAE,CAAC;MAAE,CAAE;MACtBG,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAf,QAAA,eAE9BnE,OAAA,CAAClB,IAAI;QAACoF,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACtCnE,OAAA;UAAKkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CnE,OAAA;YAAKkE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCnE,OAAA;cAAKkE,SAAS,EAAG,2DACfhD,IAAI,CAAC0C,SAAS,GAAG,cAAc,GAAG,gBACnC,EAAE;cAAAO,QAAA,eACDnE,OAAA,CAACL,MAAM;gBAACuE,SAAS,EAAG,WAAUhD,IAAI,CAAC0C,SAAS,GAAG,gBAAgB,GAAG,kBAAmB;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACNvE,OAAA;cAAKkE,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBnE,OAAA;gBAAKkE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CnE,OAAA;kBAAIkE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEjD,IAAI,CAACiB;gBAAI;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpEvE,OAAA;kBAAMkE,SAAS,EAAG,gBAChBhD,IAAI,CAAC0C,SAAS,GAAG,6BAA6B,GAAG,iCAClD,EAAE;kBAAAO,QAAA,EACAjD,IAAI,CAAC0C,SAAS,GAAG,SAAS,GAAG;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACNN,oBAAoB,CAAC,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAENvE,OAAA;gBAAKkE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CnE,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACN,MAAM;oBAACwE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BvE,OAAA;oBAAAmE,QAAA,EAAOjD,IAAI,CAACuC;kBAAK;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNvE,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACP,QAAQ;oBAACyE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCvE,OAAA;oBAAAmE,QAAA,EAAOjD,IAAI,CAACwC,MAAM,IAAI;kBAAqB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNvE,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACd,OAAO;oBAACgF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvE,OAAA;oBAAAmE,QAAA,GAAM,SAAO,EAACjD,IAAI,CAACyC,KAAK,IAAI,cAAc;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,EAGLrD,IAAI,CAACiE,gBAAgB,iBACpBnF,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACJ,OAAO;oBAACsE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvE,OAAA;oBAAAmE,QAAA,GAAM,QAAM,EAACjD,IAAI,CAACiE,gBAAgB;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACN,EACArD,IAAI,CAACK,mBAAmB,iBACvBvB,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACH,OAAO;oBAACqE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvE,OAAA;oBAAAmE,QAAA,GAAM,WAAS,EAACK,UAAU,CAACtD,IAAI,CAACK,mBAAmB,CAAC;kBAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,EAGArD,IAAI,CAACY,iBAAiB,GAAG,CAAC,iBACzB9B,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACL,MAAM;oBAACuE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BvE,OAAA;oBAAAmE,QAAA,GAAM,WAAS,EAACjD,IAAI,CAACY,iBAAiB;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CACN,EACArD,IAAI,CAACS,YAAY,iBAChB3B,OAAA;kBAAKkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnE,OAAA,CAACH,OAAO;oBAACqE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/BvE,OAAA;oBAAAmE,QAAA,GAAM,eAAa,EAACK,UAAU,CAACtD,IAAI,CAACS,YAAY,CAAC;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CnE,OAAA,CAACjB,MAAM;cACLqG,OAAO,EAAElE,IAAI,CAAC0C,SAAS,GAAG,SAAS,GAAG,SAAU;cAChDyB,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMzC,SAAS,CAAC3B,IAAI,CAAC4B,SAAS,CAAE;cACzCyC,IAAI,EAAErE,IAAI,CAAC0C,SAAS,gBAAG5D,OAAA,CAACX,WAAW;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGvE,OAAA,CAACV,OAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAEpDjD,IAAI,CAAC0C,SAAS,GAAG,SAAS,GAAG;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eAETvE,OAAA,CAACjB,MAAM;cACLqG,OAAO,EAAC,OAAO;cACfC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIE,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;kBAChE1C,UAAU,CAAC7B,IAAI,CAAC4B,SAAS,CAAC;gBAC5B;cACF,CAAE;cACFyC,IAAI,eAAEvF,OAAA,CAACT,OAAO;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEjB,CAAC;EAED,oBACEvE,OAAA;IAAKkE,SAAS,EAAC,4DAA4D;IAAAC,QAAA,eACzEnE,OAAA;MAAKkE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BnE,OAAA,CAACzB,MAAM,CAACoG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BZ,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBnE,OAAA;UAAKkE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACvDnE,OAAA,CAACd,OAAO;gBAACgF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvE,OAAA;cAAGkE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCnE,OAAA,CAAClB,IAAI;cAACoF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC7CnE,OAAA;gBAAKkE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAE/D,KAAK,CAACuC;cAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACPvE,OAAA,CAAClB,IAAI;cAACoF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC7CnE,OAAA;gBAAKkE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EACjD/D,KAAK,CAAC8C,MAAM,CAACwC,CAAC,IAAI,CAACA,CAAC,CAAC9B,SAAS,CAAC,CAACjB;cAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACPvE,OAAA,CAAClB,IAAI;cAACoF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC7CnE,OAAA;gBAAKkE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/C/D,KAAK,CAAC8C,MAAM,CAACwC,CAAC,IAAIzE,2BAA2B,CAACyE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC/C;cAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACPvE,OAAA,CAAClB,IAAI;cAACoF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC7CnE,OAAA;gBAAKkE,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChD/D,KAAK,CAAC8C,MAAM,CAACwC,CAAC,IAAIzE,2BAA2B,CAACyE,CAAC,CAAC,KAAK,cAAc,CAAC,CAAC/C;cAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACPvE,OAAA,CAAClB,IAAI;cAACoF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC7CnE,OAAA;gBAAKkE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9C/D,KAAK,CAAC8C,MAAM,CAACwC,CAAC,IAAIzE,2BAA2B,CAACyE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC/C;cAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNvE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbvE,OAAA,CAACzB,MAAM,CAACoG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEU,KAAK,EAAE;QAAI,CAAE;QAC3BzB,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBnE,OAAA,CAAClB,IAAI;UAACoF,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACnBnE,OAAA;YAAKkE,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDnE,OAAA;cAAKkE,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA,CAAChB,KAAK;gBACJ4G,WAAW,EAAC,4CAA4C;gBACxDC,KAAK,EAAErF,WAAY;gBACnBsF,QAAQ,EAAGC,CAAC,IAAKtF,cAAc,CAACsF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDN,IAAI,eAAEvF,OAAA,CAACb,QAAQ;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA;cAAKkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE6F,KAAK,EAAEnF,YAAa;gBACpBoF,QAAQ,EAAGC,CAAC,IAAKpF,eAAe,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACjD3B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAExBnE,OAAA;kBAAQ6F,KAAK,EAAC,KAAK;kBAAA1B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCvE,OAAA;kBAAQ6F,KAAK,EAAC,QAAQ;kBAAA1B,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CvE,OAAA;kBAAQ6F,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvE,OAAA;cAAKkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE6F,KAAK,EAAEjF,kBAAmB;gBAC1BkF,QAAQ,EAAGC,CAAC,IAAKlF,qBAAqB,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACvD3B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAExBnE,OAAA;kBAAQ6F,KAAK,EAAC,KAAK;kBAAA1B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCvE,OAAA;kBAAQ6F,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCvE,OAAA;kBAAQ6F,KAAK,EAAC,cAAc;kBAAA1B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDvE,OAAA;kBAAQ6F,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvE,OAAA,CAACjB,MAAM;cACLqG,OAAO,EAAC,WAAW;cACnBG,IAAI,eAAEvF,OAAA,CAACZ,QAAQ;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBe,OAAO,EAAEA,CAAA,KAAM;gBACb7E,cAAc,CAAC,EAAE,CAAC;gBAClBE,eAAe,CAAC,KAAK,CAAC;gBACtBE,qBAAqB,CAAC,KAAK,CAAC;cAC9B,CAAE;cAAAsD,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL,CAAC/D,WAAW,IAAIE,YAAY,KAAK,KAAK,IAAIE,kBAAkB,KAAK,KAAK,kBACrEZ,OAAA;YAAKkE,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDnE,OAAA;cAAMkE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UAC9B,EAAC7D,aAAa,CAACqC,MAAM,EAAC,MAAI,EAACvC,KAAK,CAACuC,MAAM,EAAC,QAChD,EAAC/B,kBAAkB,KAAK,KAAK,iBAC3BZ,OAAA;gBAAMkE,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,GAC5EvD,kBAAkB,KAAK,SAAS,IAAI,SAAS,EAC7CA,kBAAkB,KAAK,cAAc,IAAI,cAAc,EACvDA,kBAAkB,KAAK,SAAS,IAAI,SAAS;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbvE,OAAA,CAACzB,MAAM,CAACoG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEU,KAAK,EAAE;QAAI,CAAE;QAAAxB,QAAA,EAE1BrD,OAAO,gBACNd,OAAA;UAAKkE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCnE,OAAA,CAACf,OAAO;YAACgH,IAAI,EAAC;UAAkB;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,GACJjE,aAAa,CAACqC,MAAM,GAAG,CAAC,gBAC1B3C,OAAA;UAAKkE,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7D,aAAa,CAAC4F,GAAG,CAAC,CAAChF,IAAI,EAAEiF,KAAK,kBAC7BnG,OAAA,CAACzB,MAAM,CAACoG,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEU,KAAK,EAAEQ,KAAK,GAAG;YAAI,CAAE;YAAAhC,QAAA,eAEnCnE,OAAA,CAACgE,QAAQ;cAAC9C,IAAI,EAAEA;YAAK;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GALnBrD,IAAI,CAAC4B,SAAS;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvE,OAAA,CAAClB,IAAI;UAACoF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAChCnE,OAAA,CAACd,OAAO;YAACgF,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DvE,OAAA;YAAIkE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EvE,OAAA;YAAGkE,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzB3D,WAAW,IAAIE,YAAY,KAAK,KAAK,IAAIE,kBAAkB,KAAK,KAAK,GAClE,8CAA8C,GAC9C;UAAmC;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrE,EAAA,CArfQD,KAAK;EAAA,QACK3B,WAAW,EAOXD,WAAW;AAAA;AAAA+H,EAAA,GARrBnG,KAAK;AAufd,eAAeA,KAAK;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}