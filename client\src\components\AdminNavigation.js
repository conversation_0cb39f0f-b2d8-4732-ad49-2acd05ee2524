import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import {
  TbUsers,
  TbBook,
  TbFileText,
  TbChartBar,
  TbRobot,
  TbBell,
  TbMenu2,
  TbX,
  TbHome,
  TbLogout,
  TbUser
} from 'react-icons/tb';

const AdminNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state) => state.user);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const adminMenuItems = [
    {
      title: 'Dashboard',
      icon: TbHome,
      path: '/admin/dashboard',
      description: 'Overview and statistics'
    },
    {
      title: 'Users',
      icon: TbUsers,
      path: '/admin/users',
      description: 'Manage student accounts'
    },
    {
      title: 'Exams',
      icon: TbFileText,
      path: '/admin/exams',
      description: 'Create and manage exams'
    },
    {
      title: 'Study Materials',
      icon: TbB<PERSON>,
      path: '/admin/study-materials',
      description: 'Manage learning resources'
    },
    {
      title: 'AI Questions',
      icon: TbRobot,
      path: '/admin/ai-questions',
      description: 'Generate AI questions'
    },
    {
      title: 'Reports',
      icon: TbChartBar,
      path: '/admin/reports',
      description: 'View analytics and reports'
    },
    {
      title: 'Announcements',
      icon: TbBell,
      path: '/admin/announcements',
      description: 'Manage announcements'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  const isActivePath = (path) => {
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 bg-white rounded-lg shadow-lg border border-gray-200"
        >
          {isMobileMenuOpen ? <TbX className="w-6 h-6" /> : <TbMenu2 className="w-6 h-6" />}
        </button>
      </div>

      {/* Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: isMobileMenuOpen || window.innerWidth >= 1024 ? 0 : -300 }}
        transition={{ duration: 0.3 }}
        className={`fixed left-0 top-0 h-full w-72 bg-gradient-to-b from-blue-900 to-blue-800 text-white z-40 shadow-2xl ${
          isMobileMenuOpen ? 'block' : 'hidden lg:block'
        }`}
      >
        <div className="p-6">
          {/* Admin Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                <span className="text-blue-900 font-bold text-lg">🧠</span>
              </div>
              <div>
                <h1 className="text-xl font-bold">BrainWave Admin</h1>
                <p className="text-blue-200 text-sm">Administrator Panel</p>
              </div>
            </div>
            
            {/* Admin Profile */}
            <div className="bg-blue-800/50 rounded-lg p-3 mt-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <TbUser className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-medium text-sm">{user?.name}</p>
                  <p className="text-blue-200 text-xs">Administrator</p>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Menu */}
          <nav className="space-y-2">
            {adminMenuItems.map((item, index) => {
              const IconComponent = item.icon;
              const isActive = isActivePath(item.path);
              
              return (
                <motion.button
                  key={item.path}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-white text-blue-900 shadow-lg'
                      : 'hover:bg-blue-700/50 text-white'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <div className="text-left">
                    <p className="font-medium text-sm">{item.title}</p>
                    <p className={`text-xs ${isActive ? 'text-blue-600' : 'text-blue-200'}`}>
                      {item.description}
                    </p>
                  </div>
                </motion.button>
              );
            })}
          </nav>

          {/* Bottom Actions */}
          <div className="absolute bottom-6 left-6 right-6 space-y-2">
            <button
              onClick={() => handleNavigation('/profile')}
              className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700/50 transition-all duration-200"
            >
              <TbUser className="w-5 h-5" />
              <span className="text-sm">Profile Settings</span>
            </button>
            
            <button
              onClick={() => handleNavigation('/')}
              className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700/50 transition-all duration-200"
            >
              <TbHome className="w-5 h-5" />
              <span className="text-sm">View Site</span>
            </button>
            
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-red-600/50 transition-all duration-200 text-red-200"
            >
              <TbLogout className="w-5 h-5" />
              <span className="text-sm">Logout</span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
};

export default AdminNavigation;
