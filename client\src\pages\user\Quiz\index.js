import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import {
  Tb<PERSON><PERSON>ch,
  Tb<PERSON>ilter,
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain,
  TbTarget,
  TbCheck,
  TbX,
  TbStar,
  TbHome,
  TbBolt
} from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReportsByUser } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getUserResults = async () => {
    try {
      if (!user?._id) return;

      const response = await getAllReportsByUser({ userId: user._id });
      if (response.success) {
        const resultsMap = {};
        response.data.forEach(report => {
          const examId = report.exam?._id;
          if (!examId) return;
          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {
            resultsMap[examId] = {
              verdict: report.verdict,
              percentage: report.percentage,
              correctAnswers: report.correctAnswers,
              wrongAnswers: report.wrongAnswers,
              totalQuestions: report.totalQuestions,
              obtainedMarks: report.obtainedMarks,
              totalMarks: report.totalMarks,
              timeTaken: report.timeTaken,
              completedAt: report.createdAt,
            };
          }
        });
        setUserResults(resultsMap);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    }
  };

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          // Filter exams by user's level
          const userLevelExams = response.data.filter(exam => {
            if (!exam.level || !user.level) return false;
            return exam.level.toLowerCase() === user.level.toLowerCase();
          });

          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          setExams(sortedExams);

          // Set default class filter to user's class
          if (user?.class) {
            setSelectedClass(String(user.class));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      } finally {
        setLoading(false);
      }
    };

    getExams();
    getUserResults();
  }, [dispatch, user?.level, user?.class, user?._id]);

  // Real-time updates for quiz completion
  useEffect(() => {
    // Listen for real-time updates from quiz completion
    const handleRankingUpdate = () => {
      console.log('🔄 Quiz listing - refreshing data after quiz completion...');
      getUserResults(); // Refresh user results to show updated XP
    };

    // Listen for window focus to refresh data when returning from quiz
    const handleWindowFocus = () => {
      console.log('🎯 Quiz listing - window focused, refreshing data...');
      getUserResults();
    };

    window.addEventListener('rankingUpdate', handleRankingUpdate);
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('rankingUpdate', handleRankingUpdate);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, []);

  useEffect(() => {
    let filtered = exams;
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedClass) {
      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));
    }
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    navigate(`/quiz/${quiz._id}/play`);
  };

  // Custom Quiz Card Component
  const QuizCard = ({ quiz, userResult, onStart }) => {
    const getStatusInfo = () => {
      if (!userResult) {
        return {
          status: 'not-attempted',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          cardBorder: 'border-blue-200',
          cardShadow: 'shadow-blue-100',
          icon: TbTarget,
          text: 'Not Attempted',
          xpEarned: 0
        };
      }

      if (userResult.verdict === 'Pass') {
        return {
          status: 'passed',
          color: 'bg-green-100 text-green-800 border-green-200',
          cardBorder: 'border-green-200',
          cardShadow: 'shadow-green-100',
          icon: TbCheck,
          text: `Passed (${userResult.percentage}%)`,
          xpEarned: userResult.xpEarned || quiz.xpPoints || 100
        };
      } else {
        return {
          status: 'failed',
          color: 'bg-red-100 text-red-800 border-red-200',
          cardBorder: 'border-red-200',
          cardShadow: 'shadow-red-100',
          icon: TbX,
          text: `Failed (${userResult.percentage}%)`,
          xpEarned: userResult.xpEarned || 0
        };
      }
    };

    const statusInfo = getStatusInfo();
    const StatusIcon = statusInfo.icon;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className={`bg-white rounded-2xl shadow-lg hover:shadow-2xl border-2 overflow-hidden group transition-all duration-300 ${statusInfo.cardBorder} ${statusInfo.cardShadow}`}
      >
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-2">
                <TbBrain className="w-6 h-6" />
              </div>
              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`}>
                <div className="flex items-center gap-1">
                  <StatusIcon className="w-3 h-3" />
                  {statusInfo.text}
                </div>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 line-clamp-2">{quiz.name}</h3>
            <div className="flex flex-wrap items-center gap-2 text-sm opacity-90">
              {quiz.subject && (
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-xs">📚 {quiz.subject}</span>
              )}
              {quiz.topic && (
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-xs">🎯 {quiz.topic}</span>
              )}
              <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-xs">Class {quiz.class}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Quiz Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6">
            <div className="text-center">
              <div className="bg-blue-50 rounded-lg p-2 mb-1">
                <TbQuestionMark className="w-4 h-4 text-blue-600 mx-auto" />
              </div>
              <div className="text-sm font-bold text-gray-900">{quiz.questions?.length || 0}</div>
              <div className="text-xs text-gray-500">Questions</div>
            </div>
            <div className="text-center">
              <div className="bg-green-50 rounded-lg p-2 mb-1">
                <TbClock className="w-4 h-4 text-green-600 mx-auto" />
              </div>
              <div className="text-sm font-bold text-gray-900">{quiz.duration || 30}</div>
              <div className="text-xs text-gray-500">Minutes</div>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-2 mb-1 border border-yellow-200">
                <TbBolt className="w-4 h-4 text-yellow-600 mx-auto" />
              </div>
              <div className="text-sm font-bold text-yellow-700">{quiz.xpPoints || 100}</div>
              <div className="text-xs text-yellow-600 font-medium">XP Points</div>
            </div>
            <div className="text-center">
              <div className="bg-purple-50 rounded-lg p-2 mb-1">
                <TbTarget className="w-4 h-4 text-purple-600 mx-auto" />
              </div>
              <div className="text-sm font-bold text-gray-900">{quiz.passingMarks || 70}%</div>
              <div className="text-xs text-gray-500">Pass Mark</div>
            </div>
          </div>

          {/* Progress Bar (if attempted) */}
          {userResult && (
            <div className="mb-6">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-600">Your Result</span>
                <span className="font-semibold">{userResult.percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${
                    userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${userResult.percentage}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>{userResult.correctAnswers || 0} correct • {userResult.wrongAnswers || 0} wrong</span>
                <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-200">
                  <TbBolt className="w-3 h-3 text-yellow-600" />
                  <span className="font-bold text-yellow-700">+{statusInfo.xpEarned} XP</span>
                </div>
              </div>
            </div>
          )}

          {/* Action Button */}
          <button
            onClick={() => onStart(quiz)}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group"
          >
            <TbPlayerPlay className="w-5 h-5 group-hover:scale-110 transition-transform" />
            {userResult ? 'Retake Quiz' : 'Start Quiz'}
          </button>
        </div>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading quizzes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive">

      <div className="container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8 sm:mb-12"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg">
            <TbBrain className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Challenge Your Brain, Beat the Rest
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4">
            Test your knowledge with our comprehensive quizzes designed for Class {user?.class || 'All Classes'}
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{filteredExams.length} Available Quizzes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Level: {user?.level || 'All Levels'}</span>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-4xl mx-auto mb-8 sm:mb-12"
        >
          <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                  <TbSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                />
              </div>
              <div className="sm:w-48 md:w-64">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                    <TbFilter className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>Class {className}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="quiz-listing-content"
        >
          {filteredExams.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto">
                <TbTarget className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {searchTerm || selectedClass
                    ? "Try adjusting your search or filter criteria."
                    : "No quizzes are available for your level at the moment."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="quiz-grid-container grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
              {filteredExams.map((quiz, index) => (
                <motion.div
                  key={quiz._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: Math.min(index * 0.1, 0.8) }}
                  className="h-full"
                >
                  <QuizCard
                    quiz={quiz}
                    userResult={userResults[quiz._id]}
                    onStart={handleQuizStart}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Quiz;
