[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "97"}, {"size": 395, "mtime": 1696247250000, "results": "98", "hashOfConfig": "99"}, {"size": 9590, "mtime": 1751422565403, "results": "100", "hashOfConfig": "99"}, {"size": 362, "mtime": 1696247250000, "results": "101", "hashOfConfig": "99"}, {"size": 430, "mtime": 1736735017645, "results": "102", "hashOfConfig": "99"}, {"size": 180, "mtime": 1696247250000, "results": "103", "hashOfConfig": "99"}, {"size": 18417, "mtime": 1751422776240, "results": "104", "hashOfConfig": "99"}, {"size": 334, "mtime": 1696247250000, "results": "105", "hashOfConfig": "99"}, {"size": 416, "mtime": 1696247250000, "results": "106", "hashOfConfig": "99"}, {"size": 404, "mtime": 1736731932223, "results": "107", "hashOfConfig": "99"}, {"size": 449, "mtime": 1736732007232, "results": "108", "hashOfConfig": "99"}, {"size": 2226, "mtime": 1749936905425, "results": "109", "hashOfConfig": "99"}, {"size": 4816, "mtime": 1748816644854, "results": "110", "hashOfConfig": "99"}, {"size": 13176, "mtime": 1751228551397, "results": "111", "hashOfConfig": "99"}, {"size": 2884, "mtime": 1748816644857, "results": "112", "hashOfConfig": "99"}, {"size": 7653, "mtime": 1750329453221, "results": "113", "hashOfConfig": "99"}, {"size": 47884, "mtime": 1751250270759, "results": "114", "hashOfConfig": "99"}, {"size": 44652, "mtime": 1751260519262, "results": "115", "hashOfConfig": "99"}, {"size": 2243, "mtime": 1735567977494, "results": "116", "hashOfConfig": "99"}, {"size": 18068, "mtime": 1751420480736, "results": "117", "hashOfConfig": "99"}, {"size": 134899, "mtime": 1751413149035, "results": "118", "hashOfConfig": "99"}, {"size": 1327, "mtime": 1709427669270, "results": "119", "hashOfConfig": "99"}, {"size": 8089, "mtime": 1740446459586, "results": "120", "hashOfConfig": "99"}, {"size": 4251, "mtime": 1751165083094, "results": "121", "hashOfConfig": "99"}, {"size": 19148, "mtime": 1751409742999, "results": "122", "hashOfConfig": "99"}, {"size": 7528, "mtime": 1751289392869, "results": "123", "hashOfConfig": "99"}, {"size": 3037, "mtime": 1751407422967, "results": "124", "hashOfConfig": "99"}, {"size": 22502, "mtime": 1751422739903, "results": "125", "hashOfConfig": "99"}, {"size": 6821, "mtime": 1751421854816, "results": "126", "hashOfConfig": "99"}, {"size": 16989, "mtime": 1751415543478, "results": "127", "hashOfConfig": "99"}, {"size": 4579, "mtime": 1749938582942, "results": "128", "hashOfConfig": "99"}, {"size": 522, "mtime": 1736735708590, "results": "129", "hashOfConfig": "99"}, {"size": 2578, "mtime": 1740446459580, "results": "130", "hashOfConfig": "99"}, {"size": 2204, "mtime": 1696247250000, "results": "131", "hashOfConfig": "99"}, {"size": 388, "mtime": 1703845955779, "results": "132", "hashOfConfig": "99"}, {"size": 1095, "mtime": 1748816644845, "results": "133", "hashOfConfig": "99"}, {"size": 279, "mtime": 1736719733927, "results": "134", "hashOfConfig": "99"}, {"size": 1104, "mtime": 1749936905424, "results": "135", "hashOfConfig": "99"}, {"size": 3391, "mtime": 1751304153158, "results": "136", "hashOfConfig": "99"}, {"size": 5595, "mtime": 1751164672302, "results": "137", "hashOfConfig": "99"}, {"size": 944, "mtime": 1750970590507, "results": "138", "hashOfConfig": "99"}, {"size": 6669, "mtime": 1750999504134, "results": "139", "hashOfConfig": "99"}, {"size": 12864, "mtime": 1751134045332, "results": "140", "hashOfConfig": "99"}, {"size": 3826, "mtime": 1751199818433, "results": "141", "hashOfConfig": "99"}, {"size": 8101, "mtime": 1750963515173, "results": "142", "hashOfConfig": "99"}, {"size": 578, "mtime": 1705434185826, "results": "143", "hashOfConfig": "99"}, {"size": 1787, "mtime": 1734985908268, "results": "144", "hashOfConfig": "99"}, {"size": 2748, "mtime": 1736737718411, "results": "145", "hashOfConfig": "99"}, {"size": 2421, "mtime": 1737107445778, "results": "146", "hashOfConfig": "99"}, {"size": 3692, "mtime": 1751088963669, "results": "147", "hashOfConfig": "99"}, {"size": 8145, "mtime": 1751000372079, "results": "148", "hashOfConfig": "99"}, {"size": 29072, "mtime": 1750992761364, "results": "149", "hashOfConfig": "99"}, {"size": 9494, "mtime": 1750995979612, "results": "150", "hashOfConfig": "99"}, {"size": 1524, "mtime": 1750994293078, "results": "151", "hashOfConfig": "99"}, {"size": 17375, "mtime": 1751000106093, "results": "152", "hashOfConfig": "99"}, {"size": 11161, "mtime": 1750999560542, "results": "153", "hashOfConfig": "99"}, {"size": 8252, "mtime": 1751004143541, "results": "154", "hashOfConfig": "99"}, {"size": 3047, "mtime": 1751086581664, "results": "155", "hashOfConfig": "99"}, {"size": 25462, "mtime": 1751089065189, "results": "156", "hashOfConfig": "99"}, {"size": 10774, "mtime": 1751085763434, "results": "157", "hashOfConfig": "99"}, {"size": 11689, "mtime": 1751100954560, "results": "158", "hashOfConfig": "99"}, {"size": 5089, "mtime": 1751261831682, "results": "159", "hashOfConfig": "99"}, {"size": 5991, "mtime": 1751088070022, "results": "160", "hashOfConfig": "99"}, {"size": 5741, "mtime": 1751088101803, "results": "161", "hashOfConfig": "99"}, {"size": 3690, "mtime": 1751088038266, "results": "162", "hashOfConfig": "99"}, {"size": 36524, "mtime": 1751372441466, "results": "163", "hashOfConfig": "99"}, {"size": 9710, "mtime": 1751290952231, "results": "164", "hashOfConfig": "99"}, {"size": 8920, "mtime": 1751372481600, "results": "165", "hashOfConfig": "99"}, {"size": 12494, "mtime": 1751290777539, "results": "166", "hashOfConfig": "99"}, {"size": 15402, "mtime": 1751422122218, "results": "167", "hashOfConfig": "99"}, {"size": 1410, "mtime": 1751140352157, "results": "168", "hashOfConfig": "99"}, {"size": 1150, "mtime": 1751188610035, "results": "169", "hashOfConfig": "99"}, {"size": 2324, "mtime": 1751140401815, "results": "170", "hashOfConfig": "99"}, {"size": 2913, "mtime": 1751140370241, "results": "171", "hashOfConfig": "99"}, {"size": 1857, "mtime": 1751140385464, "results": "172", "hashOfConfig": "99"}, {"size": 3119, "mtime": 1751164996340, "results": "173", "hashOfConfig": "99"}, {"size": 25094, "mtime": 1751311415213, "results": "174", "hashOfConfig": "99"}, {"size": 13299, "mtime": 1751249005755, "results": "175", "hashOfConfig": "99"}, {"size": 7171, "mtime": 1751229742199, "results": "176", "hashOfConfig": "99"}, {"size": 2576, "mtime": 1751143230244, "results": "177", "hashOfConfig": "99"}, {"size": 3904, "mtime": 1751143777976, "results": "178", "hashOfConfig": "99"}, {"size": 5088, "mtime": 1751143254906, "results": "179", "hashOfConfig": "99"}, {"size": 4989, "mtime": 1751143312418, "results": "180", "hashOfConfig": "99"}, {"size": 6304, "mtime": 1751188593099, "results": "181", "hashOfConfig": "99"}, {"size": 3061, "mtime": 1751208387152, "results": "182", "hashOfConfig": "99"}, {"size": 12711, "mtime": 1751260271529, "results": "183", "hashOfConfig": "99"}, {"size": 19540, "mtime": 1751260093830, "results": "184", "hashOfConfig": "99"}, {"size": 29607, "mtime": 1751260312633, "results": "185", "hashOfConfig": "99"}, {"size": 11901, "mtime": 1751236424130, "results": "186", "hashOfConfig": "99"}, {"size": 8429, "mtime": 1751244672688, "results": "187", "hashOfConfig": "99"}, {"size": 7685, "mtime": 1751244700154, "results": "188", "hashOfConfig": "99"}, {"size": 10081, "mtime": 1751244608756, "results": "189", "hashOfConfig": "99"}, {"size": 9250, "mtime": 1751318441502, "results": "190", "hashOfConfig": "99"}, {"size": 3109, "mtime": 1751260973778, "results": "191", "hashOfConfig": "99"}, {"size": 6595, "mtime": 1751407436134, "results": "192", "hashOfConfig": "99"}, {"size": 9778, "mtime": 1751407462268, "results": "193", "hashOfConfig": "99"}, {"size": 846, "mtime": 1751407484742, "results": "194", "hashOfConfig": "99"}, {"size": 7685, "mtime": 1751422625963, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, "1ymk59w", {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "233", "usedDeprecatedRules": "199"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "237", "usedDeprecatedRules": "199"}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "241", "usedDeprecatedRules": "199"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "245", "usedDeprecatedRules": "199"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "255", "usedDeprecatedRules": "199"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "265", "usedDeprecatedRules": "199"}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "269", "usedDeprecatedRules": "199"}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "273", "usedDeprecatedRules": "199"}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "295", "usedDeprecatedRules": "199"}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "323", "usedDeprecatedRules": "199"}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "348", "usedDeprecatedRules": "199"}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "373", "usedDeprecatedRules": "199"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "377", "usedDeprecatedRules": "199"}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "384", "usedDeprecatedRules": "199"}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "397", "usedDeprecatedRules": "199"}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "401", "usedDeprecatedRules": "199"}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "447", "usedDeprecatedRules": "199"}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "454", "usedDeprecatedRules": "199"}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "461", "usedDeprecatedRules": "199"}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "465", "usedDeprecatedRules": "199"}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "199"}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "481", "usedDeprecatedRules": "199"}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["509", "510", "511", "512", "513", "514", "515"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx", ["516"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { message, Modal, Input } from \"antd\";\r\nimport {\r\n  PlusOutlined,\r\n  EditOutlined,\r\n  DeleteOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  addAnnouncement,\r\n  deleteAnnouncement,\r\n  getAnnouncements,\r\n  updateAnnouncement,\r\n} from \"../../../apicalls/announcements\";\r\nimport \"./announcement.css\"; // your custom styles\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\n\r\nexport default function Announcement() {\r\n  const [list, setList] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalOpen, setModalOpen] = useState(false);\r\n  const [form, setForm] = useState({ heading: \"\", description: \"\" });\r\n  const [editingId, setEditingId] = useState(null);\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchAll = async () => {\r\n    dispatch(ShowLoading());\r\n    const res = await getAnnouncements();\r\n    dispatch(HideLoading());\r\n    if (res.success !== false) setList(res);\r\n    else message.error(res.error || \"Failed to load announcements\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAll();\r\n  }, []);\r\n\r\n  const openAddModal = () => {\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const openEditModal = (announcement) => {\r\n    setForm({\r\n      heading: announcement.heading,\r\n      description: announcement.description,\r\n    });\r\n    setEditingId(announcement._id);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const handleModalSubmit = async () => {\r\n    if (!form.heading || !form.description) {\r\n      return message.warning(\"Heading and Description are required\");\r\n    }\r\n    dispatch(ShowLoading());\r\n\r\n    setLoading(true);\r\n    const res = editingId\r\n      ? await updateAnnouncement(editingId, form)\r\n      : await addAnnouncement(form);\r\n    setLoading(false);\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Operation failed\");\r\n\r\n    message.success(editingId ? \"Announcement updated\" : \"Announcement added\");\r\n    setModalOpen(false);\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    fetchAll();\r\n    dispatch(HideLoading());\r\n\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (!window.confirm(\"Delete this announcement?\")) return;\r\n    dispatch(ShowLoading());\r\n    const res = await deleteAnnouncement(id);\r\n    dispatch(HideLoading());\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Delete failed\");\r\n    message.success(\"Announcement deleted\");\r\n    fetchAll();\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcement-admin\">\r\n      <div className=\"admin-header\">\r\n        <h2>Manage Announcements</h2>\r\n        <button onClick={openAddModal} className=\"add-btn\">\r\n          <PlusOutlined />\r\n          <span>Add Announcement</span>\r\n        </button>\r\n      </div>\r\n\r\n      {list.length === 0 ? (\r\n        <p className=\"no-announcements\">No announcements yet.</p>\r\n      ) : (\r\n        <div className=\"announcement-list\">\r\n          {list.map((item) => (\r\n            <div className=\"announcement-card\" key={item._id}>\r\n              <div className=\"announcement-content\">\r\n                <h3>{item.heading}</h3>\r\n                <p>{item.description}</p>\r\n              </div>\r\n              <div className=\"card-actions\">\r\n                <button\r\n                  onClick={() => openEditModal(item)}\r\n                  className=\"edit-btn\"\r\n                  title=\"Edit\"\r\n                >\r\n                  <EditOutlined />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDelete(item._id)}\r\n                  className=\"delete-btn\"\r\n                  title=\"Delete\"\r\n                >\r\n                  <DeleteOutlined />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Modal for Add/Edit */}\r\n      <Modal\r\n        title={editingId ? \"Edit Announcement\" : \"Add Announcement\"}\r\n        open={modalOpen}\r\n        onCancel={() => setModalOpen(false)}\r\n        onOk={handleModalSubmit}\r\n        okText={editingId ? \"Update\" : \"Add\"}\r\n        confirmLoading={loading}\r\n      >\r\n        <div className=\"modal-form\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Heading\"\r\n            style={{ borderRadius: \"5px\" }}\r\n            value={form.heading}\r\n            onChange={(e) => setForm({ ...form, heading: e.target.value })}\r\n          />\r\n          <Input.TextArea\r\n            placeholder=\"Description\"\r\n            rows={4}\r\n            value={form.description}\r\n            onChange={(e) =>\r\n              setForm({ ...form, description: e.target.value })\r\n            }\r\n          />\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["517", "518"], [], "import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n  console.log(examData?.questions, \"examData?.questions\")\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/admin/exams\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n      });\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctOption\",\r\n      render: (text, record) => {\r\n        if (record.answerType === \"Free Text\") {\r\n          return <div>{record.correctOption}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {record.correctOption}: {record.options && record.options[record.correctOption] ? record.options[record.correctOption] : record.correctOption}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Topic\" name=\"topic\">\r\n                    <input type=\"text\" placeholder=\"Enter quiz topic (e.g., Algebra, Cell Biology)\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-end\">\r\n                  <button\r\n                    className=\"primary-outlined-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["519"], [], "import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Exams\" />\r\n\r\n        <button\r\n          className=\"primary-outlined-btn flex items-center\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <i className=\"ri-add-line\"></i>\r\n          Add Exam\r\n        </button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx", ["520"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch()\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addPayment({ plan });\r\n            localStorage.setItem(\"order_id\", response.order_id);\r\n            setWaitingModalOpen(true);\r\n            setPaymentInProgress(true);\r\n            dispatch(setPaymentVerificationNeeded(true));\r\n        } catch (error) {\r\n            console.error(\"Error processing payment:\", error);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans.map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${plan.title === \"Standard Membership\" ? \"basic\" : \"\"}`}\r\n                                >\r\n                                    {plan.title === \"Standard Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n\r\n                                    <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                    <p className=\"plan-actual-price\">\r\n                                        {plan.actualPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <p className=\"plan-discounted-price\">\r\n                                        {plan.discountedPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <span className=\"plan-discount-tag\">\r\n                                        {plan.discountPercentage}% OFF\r\n                                    </span>\r\n                                    <p className=\"plan-renewal-info\">\r\n                                        For {plan?.features[0]}\r\n                                    </p>\r\n                                    <button className=\"plan-button\"\r\n                                        // onClick={() => setConfirmModalOpen(true)}\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >Choose Plan</button>\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["521"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["539", "540"], [], "import React from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Modal, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam?.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam?.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam?.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"reports-container\">\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <Table \r\n      columns={columns} \r\n      dataSource={reportsData} \r\n      rowKey={(record) => record._id} \r\n      scroll={{ x: true }} \r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["541", "542", "543", "544", "545"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["562", "563"], [], "import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["564"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Rate } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addReview, getAllReviews } from \"../../../apicalls/reviews\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst AboutUs = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [userRating, setUserRating] = useState('');\r\n    const [userText, setUserText] = useState('');\r\n    const [reviews, setReviews] = useState('');\r\n    const [userOldReview, setUserOldReview] = useState(null);\r\n    const dispatch = useDispatch();\r\n\r\n    const getReviews = async () => {\r\n        try {\r\n            const response = await getAllReviews();\r\n            if (response.success) {\r\n                setReviews(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await getReviews();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const handleRatingChange = (value) => {\r\n        setUserRating(value);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (userRating === '' || userRating === 0 || userText === '') {\r\n            return;\r\n        }\r\n        try {\r\n            const data = {\r\n                rating: userRating,\r\n                text: userText\r\n            }\r\n            const response = await addReview(data);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                getReviews();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n            dispatch(HideLoading());\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (reviews) {\r\n            const userInReview = reviews.find(review => review.user._id === userData._id);\r\n            setUserOldReview(userInReview);\r\n        }\r\n    }, [reviews, userData]);\r\n\r\n    return (\r\n        <div className=\"AboutUs\">\r\n            {!isAdmin &&\r\n                <>\r\n                    <PageTitle title=\"About Us\" />\r\n                    <div className=\"divider\"></div>\r\n                    <p className=\"info-para\">\r\n                        Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit.\r\n                        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,\r\n                        quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                    </p>\r\n                    {!userOldReview ?\r\n                        <>\r\n                            <h1>Feedback</h1>\r\n                            <p>\r\n                                We strive to provide an exceptional user experience and value your feedback.<br />\r\n                                Please take a moment to rate our web app:\r\n                            </p>\r\n                            <div><b>Rate Your Experience:</b></div>\r\n                            <div className=\"rating\">\r\n                                <div>\r\n                                    <Rate defaultValue={0} onChange={handleRatingChange} />\r\n                                    <br />\r\n                                    <textarea\r\n                                        className=\"rating-text\"\r\n                                        placeholder=\"Share your thoughts...\"\r\n                                        rows={4}\r\n                                        value={userText}\r\n                                        onChange={(e) => setUserText(e.target.value)}\r\n                                    />\r\n                                </div>\r\n                                <button onClick={handleSubmit}>Submit</button>\r\n                            </div>\r\n                        </>\r\n                        :\r\n                        <>\r\n                            <h2>Your Feedback</h2>\r\n                            <div className=\"p-rating-div\">\r\n                                <div className=\"profile-row\">\r\n                                    <img className=\"profile\" src={userOldReview.user.profileImage ? userOldReview.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                    <p>{userOldReview.user.name}</p>\r\n                                </div>\r\n                                <Rate defaultValue={userOldReview.rating} className=\"rate\" disabled={true} />\r\n                                <br />\r\n                                <div className=\"text\">{userOldReview.text}</div>\r\n                            </div>\r\n                        </>\r\n                    }\r\n                    <h2>Previous Reviews</h2>\r\n                    {reviews ?\r\n                        <div className=\"p-ratings\">\r\n                            {reviews.map((review, index) => (\r\n                                <div key={index}>\r\n                                    {userOldReview?.user._id !== review.user?._id && review.user?._id &&\r\n                                        <div className=\"p-rating-div\">\r\n                                            <div className=\"profile-row\">\r\n                                                <img className=\"profile\" src={review.user.profileImage ? review.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                                <p>{review.user.name}</p>\r\n                                            </div>\r\n                                            <Rate defaultValue={review.rating} className=\"rate\" disabled={true} />\r\n                                            <br />\r\n                                            <div className=\"text\">{review.text}</div>\r\n                                        </div>\r\n                                    }\r\n                                </div>\r\n                            ))\r\n                            }\r\n                        </div>\r\n                        :\r\n                        <div>\r\n                            No reviews yet.    \r\n                        </div>\r\n                    }\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AboutUs;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["565", "566"], [], "import React from \"react\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction AdminReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const [pagination, setPagination] = React.useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0, // total number of records\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = React.useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n  });\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam.name}</>,\r\n    },\r\n    {\r\n      title: \"User Name\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => <>{record.user.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers?.length || 0}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict || \"N/A\"}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <div className=\"flex gap-2\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Exam\"\r\n          value={filters.examName}\r\n          onChange={(e) => setFilters({ ...filters, examName: e.target.value })}\r\n        />\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"User\"\r\n          value={filters.userName}\r\n          onChange={(e) => setFilters({ ...filters, userName: e.target.value })}\r\n        />\r\n        <button\r\n          className=\"primary-outlined-btn\"\r\n          onClick={() => {\r\n            setFilters({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n            getData({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n          }}\r\n        >\r\n          Clear\r\n        </button>\r\n        <button\r\n          className=\"primary-contained-btn\"\r\n          onClick={() => getData(filters, 1, pagination.pageSize)}\r\n        >\r\n          Search\r\n        </button>\r\n      </div>\r\n      <Table\r\n  columns={columns}\r\n  dataSource={reportsData}\r\n  className=\"mt-2\"\r\n  pagination={{\r\n    current: pagination.current,\r\n    total: pagination.total,\r\n    showSizeChanger: false, // Disables size changer as per your request\r\n    onChange: (page) => {\r\n      setPagination({\r\n        ...pagination,\r\n        current: page,\r\n      });\r\n      getData(filters, page); // Pass the page, no need to pass pageSize\r\n    },\r\n  }}\r\n/>\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["567", "568", "569", "570", "571"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["572", "573", "574", "575", "576", "577"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", ["595", "596", "597", "598", "599"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx", ["600", "601"], [], "import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport \"./index.css\"; // Import the custom CSS\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["602", "603", "604"], [], "import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["605"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx", ["606", "607"], [], "import React, { useEffect, useState } from \"react\";\r\nimport Mo<PERSON> from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <h2>Please confirm the payment</h2>\r\n            </div>\r\n            <div className=\"waiting-modal-timer\">\r\n                <svg\r\n                    fill=\"#253864\"\r\n                    version=\"1.1\"\r\n                    id=\"Layer_1\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    width=\"64px\"\r\n                    height=\"64px\"\r\n                    stroke=\"#253864\"\r\n                >\r\n                    <g>\r\n                        <path d=\"M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z\"></path>\r\n                        <path d=\"M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z\"></path>\r\n                        <path d=\"M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z\"></path>\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n\r\n            <p className=\"waiting-modal-footer\">\r\n                Ensure that your payment is confirmed before the timer runs out.\r\n            </p>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["608", "609", "610", "611"], [], "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js", ["612", "613", "614"], [], "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Button, Row, Col, Statistic, Table, Tag, Space, message } from \"antd\";\nimport { \n  FaRobot, \n  FaQuestionCircle, \n  FaHistory, \n  FaCog, \n  FaPlus,\n  FaEye,\n  FaCheck,\n  FaTimes\n} from \"react-icons/fa\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getGenerationHistory } from \"../../../apicalls/aiQuestions\";\nimport QuestionGenerationForm from \"./QuestionGenerationForm\";\nimport QuestionPreview from \"./QuestionPreview\";\nimport \"./AIQuestionGeneration.css\";\n\nfunction AIQuestionGeneration() {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [activeView, setActiveView] = useState(\"dashboard\");\n  const [generationHistory, setGenerationHistory] = useState([]);\n  const [selectedGeneration, setSelectedGeneration] = useState(null);\n  const [stats, setStats] = useState({\n    totalGenerations: 0,\n    totalQuestions: 0,\n    approvedQuestions: 0,\n    pendingReview: 0,\n  });\n\n  useEffect(() => {\n    fetchGenerationHistory();\n  }, []);\n\n  const fetchGenerationHistory = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getGenerationHistory({ limit: 20 });\n      if (response.success) {\n        setGenerationHistory(response.data.generations);\n        calculateStats(response.data.generations);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to fetch generation history\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const calculateStats = (generations) => {\n    const stats = generations.reduce((acc, gen) => {\n      acc.totalGenerations += 1;\n      acc.totalQuestions += gen.generatedQuestions.length;\n      acc.approvedQuestions += gen.generatedQuestions.filter(q => q.approved).length;\n      acc.pendingReview += gen.generationStatus === \"completed\" ? 1 : 0;\n      return acc;\n    }, {\n      totalGenerations: 0,\n      totalQuestions: 0,\n      approvedQuestions: 0,\n      pendingReview: 0,\n    });\n    setStats(stats);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      pending: \"orange\",\n      in_progress: \"blue\",\n      completed: \"green\",\n      failed: \"red\",\n      cancelled: \"gray\",\n    };\n    return colors[status] || \"default\";\n  };\n\n  const historyColumns = [\n    {\n      title: \"Generation ID\",\n      dataIndex: \"_id\",\n      key: \"_id\",\n      render: (id) => id.slice(-8),\n    },\n    {\n      title: \"Exam\",\n      dataIndex: [\"examId\", \"name\"],\n      key: \"examName\",\n    },\n    {\n      title: \"Questions\",\n      dataIndex: \"generatedQuestions\",\n      key: \"questionCount\",\n      render: (questions) => questions.length,\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"generationStatus\",\n      key: \"status\",\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Created\",\n      dataIndex: \"createdAt\",\n      key: \"createdAt\",\n      render: (date) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<FaEye />}\n            onClick={() => {\n              setSelectedGeneration(record);\n              setActiveView(\"preview\");\n            }}\n          >\n            Preview\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderDashboard = () => (\n    <div className=\"ai-question-dashboard\">\n      <Row gutter={[16, 16]} className=\"mb-4\">\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Total Generations\"\n              value={stats.totalGenerations}\n              prefix={<FaRobot />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Questions Generated\"\n              value={stats.totalQuestions}\n              prefix={<FaQuestionCircle />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Approved Questions\"\n              value={stats.approvedQuestions}\n              prefix={<FaCheck />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Pending Review\"\n              value={stats.pendingReview}\n              prefix={<FaTimes />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generate New Questions\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                type=\"primary\"\n                icon={<FaPlus />}\n                onClick={() => setActiveView(\"generate\")}\n              >\n                Start Generation\n              </Button>\n            ]}\n          >\n            <p>Create AI-generated questions for your exams using advanced language models.</p>\n            <ul>\n              <li>Multiple choice questions</li>\n              <li>Fill in the blank questions</li>\n              <li>Picture-based questions</li>\n              <li>Tanzania syllabus compliant</li>\n            </ul>\n          </Card>\n        </Col>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generation History\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                icon={<FaHistory />}\n                onClick={() => setActiveView(\"history\")}\n              >\n                View History\n              </Button>\n            ]}\n          >\n            <p>Review and manage your previous question generations.</p>\n            <ul>\n              <li>Track generation status</li>\n              <li>Preview generated questions</li>\n              <li>Approve or reject questions</li>\n              <li>Add approved questions to exams</li>\n            </ul>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card title=\"Recent Generations\" className=\"mt-4\">\n        <Table\n          dataSource={generationHistory.slice(0, 5)}\n          columns={historyColumns}\n          pagination={false}\n          rowKey=\"_id\"\n        />\n        {generationHistory.length > 5 && (\n          <div className=\"text-center mt-3\">\n            <Button onClick={() => setActiveView(\"history\")}>\n              View All Generations\n            </Button>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n\n  const renderHistory = () => (\n    <Card title=\"Generation History\">\n      <Table\n        dataSource={generationHistory}\n        columns={historyColumns}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n        }}\n        rowKey=\"_id\"\n      />\n    </Card>\n  );\n\n  const renderContent = () => {\n    switch (activeView) {\n      case \"generate\":\n        return (\n          <QuestionGenerationForm\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"preview\":\n        return (\n          <QuestionPreview\n            generation={selectedGeneration}\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"history\":\n        return renderHistory();\n      default:\n        return renderDashboard();\n    }\n  };\n\n  return (\n    <div className=\"ai-question-generation\">\n      <PageTitle title=\"AI Question Generation\" />\n      \n      {activeView === \"dashboard\" && (\n        <div className=\"page-header\">\n          <h2>AI Question Generation Dashboard</h2>\n          <p>Generate high-quality questions using artificial intelligence</p>\n        </div>\n      )}\n\n      {renderContent()}\n    </div>\n  );\n}\n\nexport default AIQuestionGeneration;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js", ["615"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js", ["616", "617"], [], "import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Button, Checkbox, Alert, Typography, Space, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, RobotOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { quickLogin, autoRefreshToken } from '../apicalls/auth';\nimport { getTokenExpiryInfo } from '../utils/authUtils';\n\nconst { Title, Text } = Typography;\n\nconst AILoginModal = ({ \n  visible, \n  onCancel, \n  onSuccess, \n  title = \"Login Required for AI Features\",\n  description = \"Please login to access AI question generation features.\"\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      // Check current token status when modal opens\n      const info = getTokenExpiryInfo();\n      setTokenInfo(info);\n      \n      // Try auto-refresh if token is expiring soon\n      if (info.needsRefresh && !info.expired) {\n        handleAutoRefresh();\n      }\n    }\n  }, [visible]);\n\n  const handleAutoRefresh = async () => {\n    try {\n      setAutoRefreshing(true);\n      const success = await autoRefreshToken();\n      if (success) {\n        const newInfo = getTokenExpiryInfo();\n        setTokenInfo(newInfo);\n        \n        if (!newInfo.expired) {\n          onSuccess?.();\n          return;\n        }\n      }\n    } catch (error) {\n      console.error('Auto-refresh failed:', error);\n    } finally {\n      setAutoRefreshing(false);\n    }\n  };\n\n  const handleLogin = async (values) => {\n    try {\n      setLoading(true);\n      \n      const response = await quickLogin({\n        email: values.email,\n        password: values.password,\n        rememberMe: values.rememberMe || false\n      });\n\n      if (response.success) {\n        // Check AI access\n        const { aiAccess } = response.data;\n        \n        if (!aiAccess.enabled) {\n          Modal.warning({\n            title: 'AI Features Not Available',\n            content: aiAccess.requiresUpgrade \n              ? 'AI question generation requires a premium subscription. Please upgrade your account.'\n              : 'AI features are not available for your account. Please contact support.',\n          });\n          return;\n        }\n\n        form.resetFields();\n        onSuccess?.(response.data);\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderTokenStatus = () => {\n    if (!tokenInfo) return null;\n\n    if (tokenInfo.expired) {\n      return (\n        <Alert\n          type=\"warning\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expired\"\n          description=\"Your session has expired. Please login again to continue using AI features.\"\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    if (tokenInfo.needsRefresh) {\n      return (\n        <Alert\n          type=\"info\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expiring Soon\"\n          description={`Your session will expire in ${tokenInfo.formattedTimeLeft}. Login to extend your session.`}\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <RobotOutlined style={{ color: '#1890ff' }} />\n          <span>{title}</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={450}\n      destroyOnClose\n      maskClosable={false}\n    >\n      <div style={{ padding: '20px 0' }}>\n        <Text type=\"secondary\" style={{ display: 'block', marginBottom: 24, textAlign: 'center' }}>\n          {description}\n        </Text>\n\n        {renderTokenStatus()}\n\n        {autoRefreshing && (\n          <Alert\n            type=\"info\"\n            message=\"Refreshing Session...\"\n            description=\"Attempting to refresh your authentication automatically.\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please enter your email' },\n              { type: 'email', message: 'Please enter a valid email' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: 'Please enter your password' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"rememberMe\" valuePropName=\"checked\">\n            <Checkbox>\n              Keep me logged in for 30 days\n            </Checkbox>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading || autoRefreshing}\n              block\n              size=\"large\"\n              icon={<RobotOutlined />}\n            >\n              {loading ? 'Logging in...' : autoRefreshing ? 'Refreshing...' : 'Login for AI Features'}\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            Secure authentication for AI-powered question generation\n          </Text>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default AILoginModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js", ["618"], [], "import { useState, useEffect, useCallback } from 'react';\nimport { message } from 'antd';\nimport { validateSession, autoRefreshToken, checkAIAccess } from '../apicalls/auth';\nimport { getTokenExpiryInfo, isSessionValid } from '../utils/authUtils';\n\n/**\n * Enhanced authentication hook specifically for AI features\n * Provides automatic token refresh, session validation, and AI access checking\n */\nexport const useAIAuth = () => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [hasAIAccess, setHasAIAccess] = useState(false);\n  const [user, setUser] = useState(null);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [requiresUpgrade, setRequiresUpgrade] = useState(false);\n\n  // Check authentication status\n  const checkAuth = useCallback(async () => {\n    try {\n      setLoading(true);\n      \n      // Quick check if session is valid\n      if (!isSessionValid()) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        setTokenInfo(null);\n        return false;\n      }\n\n      // Get token expiry info\n      const expiry = getTokenExpiryInfo();\n      setTokenInfo(expiry);\n\n      // If token is expired, clear everything\n      if (expiry.expired) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        return false;\n      }\n\n      // Try to auto-refresh if needed\n      if (expiry.needsRefresh) {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n        } catch (error) {\n          console.warn('Auto-refresh failed:', error);\n        }\n      }\n\n      // Validate session and check AI access\n      const accessCheck = await checkAIAccess();\n      \n      if (accessCheck.hasAccess) {\n        setIsAuthenticated(true);\n        setHasAIAccess(true);\n        setUser(accessCheck.user);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return true;\n      } else {\n        setIsAuthenticated(!!accessCheck.user);\n        setHasAIAccess(false);\n        setUser(accessCheck.user || null);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return false;\n      }\n\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setHasAIAccess(false);\n      setUser(null);\n      setTokenInfo(null);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Refresh authentication\n  const refreshAuth = useCallback(async () => {\n    return await checkAuth();\n  }, [checkAuth]);\n\n  // Logout\n  const logout = useCallback(() => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setHasAIAccess(false);\n    setUser(null);\n    setTokenInfo(null);\n    message.info('Logged out successfully');\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((userData) => {\n    setIsAuthenticated(true);\n    setUser(userData.user);\n    \n    // Check AI access from login response\n    const aiEnabled = userData.aiAccess?.enabled !== false;\n    setHasAIAccess(aiEnabled);\n    setRequiresUpgrade(userData.aiAccess?.requiresUpgrade || false);\n    \n    // Update token info\n    const expiry = getTokenExpiryInfo();\n    setTokenInfo(expiry);\n    \n    message.success('Successfully logged in for AI features!');\n  }, []);\n\n  // Require authentication for AI operations\n  const requireAIAuth = useCallback(async () => {\n    if (loading) {\n      return { success: false, reason: 'loading' };\n    }\n\n    if (!isAuthenticated) {\n      return { success: false, reason: 'not_authenticated' };\n    }\n\n    if (!hasAIAccess) {\n      if (requiresUpgrade) {\n        return { success: false, reason: 'requires_upgrade' };\n      }\n      return { success: false, reason: 'no_ai_access' };\n    }\n\n    // Check if token is about to expire\n    if (tokenInfo?.needsRefresh) {\n      try {\n        await autoRefreshToken();\n        const newExpiry = getTokenExpiryInfo();\n        setTokenInfo(newExpiry);\n      } catch (error) {\n        return { success: false, reason: 'refresh_failed' };\n      }\n    }\n\n    return { success: true };\n  }, [isAuthenticated, hasAIAccess, requiresUpgrade, tokenInfo, loading]);\n\n  // Auto-refresh timer\n  useEffect(() => {\n    let refreshTimer;\n\n    if (isAuthenticated && tokenInfo && !tokenInfo.expired) {\n      // Set timer to refresh token 5 minutes before expiry\n      const refreshTime = Math.max(0, (tokenInfo.timeLeft - 300) * 1000);\n      \n      refreshTimer = setTimeout(async () => {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n          console.log('🔄 Token auto-refreshed');\n        } catch (error) {\n          console.warn('Auto-refresh timer failed:', error);\n        }\n      }, refreshTime);\n    }\n\n    return () => {\n      if (refreshTimer) {\n        clearTimeout(refreshTimer);\n      }\n    };\n  }, [isAuthenticated, tokenInfo]);\n\n  // Initial auth check\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  return {\n    // State\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    tokenInfo,\n    loading,\n    requiresUpgrade,\n    \n    // Actions\n    checkAuth,\n    refreshAuth,\n    logout,\n    handleLoginSuccess,\n    requireAIAuth,\n    \n    // Computed values\n    needsLogin: !isAuthenticated,\n    needsUpgrade: isAuthenticated && !hasAIAccess && requiresUpgrade,\n    sessionExpiringSoon: tokenInfo?.needsRefresh || false,\n    timeUntilExpiry: tokenInfo?.formattedTimeLeft || 'Unknown'\n  };\n};\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["619"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["620", "621", "622"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["623", "624", "625", "626"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["627"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["628"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\n\nconst QuizTimer = ({\n  duration, // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300, // 5 minutes\n  className = '',\n}) => {\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const interval = setInterval(() => {\n      setTimeRemaining((prev) => {\n        if (prev <= 1) {\n          onTimeUp?.();\n          return 0;\n        }\n        \n        const newTime = prev - 1;\n        \n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        \n        return newTime;\n      });\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getProgressPercentage = () => {\n    return ((duration - timeRemaining) / duration) * 100;\n  };\n\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-white'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-white'; // Warning\n    return 'text-white'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n\n  return (\n    <div className={`${className}`}>\n      {/* Compact Timer Display */}\n      <motion.div\n        animate={isWarning ? { scale: [1, 1.05, 1] } : {}}\n        transition={{ duration: 1, repeat: isWarning ? Infinity : 0 }}\n        className={`inline-flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg border-2 ${\n          timeRemaining <= 60\n            ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50'\n            : timeRemaining <= warningThreshold\n            ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50'\n            : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'\n        }`}\n        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n      >\n        {timeRemaining <= warningThreshold && (\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity }}\n          >\n            <TbAlertTriangle className={`w-5 h-5 drop-shadow-md ${\n              timeRemaining <= 60 ? 'text-red-100' : 'text-yellow-100'\n            }`} />\n          </motion.div>\n        )}\n\n        <TbClock className={`w-5 h-5 drop-shadow-md ${\n          timeRemaining <= 60\n            ? 'text-red-100'\n            : timeRemaining <= warningThreshold\n            ? 'text-yellow-100'\n            : 'text-blue-100'\n        }`} />\n\n        <div className=\"text-center\">\n          <div className={`text-xs font-semibold opacity-90 mb-1 ${\n            timeRemaining <= 60\n              ? 'text-red-100'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-100'\n              : 'text-blue-100'\n          }`}>TIME</div>\n          <span className={`font-mono font-black text-lg ${\n            timeRemaining <= 60\n              ? 'text-red-50'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-50'\n              : 'text-blue-50'\n          }`} style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n            {formatTime(timeRemaining)}\n          </span>\n        </div>\n      </motion.div>\n\n      {/* Progress Bar */}\n      <div className=\"mt-3 w-full bg-gray-300 rounded-full h-2 overflow-hidden shadow-inner\">\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${getProgressPercentage()}%` }}\n          transition={{ duration: 0.5 }}\n          className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full shadow-sm`}\n        />\n      </div>\n\n      {/* Warning Message */}\n      {isWarning && timeRemaining > 60 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-semibold bg-yellow-100 text-yellow-800 px-3 py-2 rounded-lg border border-yellow-300\"\n        >\n          ⚠️ {Math.floor(timeRemaining / 60)} minutes remaining\n        </motion.div>\n      )}\n\n      {/* Critical Warning */}\n      {timeRemaining <= 60 && timeRemaining > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-bold bg-red-100 text-red-800 px-3 py-2 rounded-lg border border-red-300\"\n        >\n          🚨 Less than 1 minute left!\n        </motion.div>\n      )}\n\n      {/* Time's Up */}\n      {timeRemaining === 0 && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"mt-3 text-sm font-black bg-red-200 text-red-900 px-3 py-2 rounded-lg border border-red-400\"\n        >\n          ⏰ Time's up!\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\n// Full-screen timer overlay for critical moments\nexport const QuizTimerOverlay = ({ timeRemaining, onClose }) => {\n  if (timeRemaining > 10) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\n    >\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className=\"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\"\n      >\n        <motion.div\n          animate={{ scale: [1, 1.2, 1] }}\n          transition={{ duration: 1, repeat: Infinity }}\n          className=\"text-6xl mb-4\"\n        >\n          ⏰\n        </motion.div>\n        \n        <h3 className=\"text-2xl font-bold text-red-600 mb-2\">\n          Time Almost Up!\n        </h3>\n        \n        <motion.div\n          animate={{ scale: [1, 1.1, 1] }}\n          transition={{ duration: 0.5, repeat: Infinity }}\n          className=\"text-4xl font-mono font-bold text-red-600 mb-4\"\n        >\n          {timeRemaining}\n        </motion.div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          Submit your answers now!\n        </p>\n        \n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n        >\n          Continue Quiz\n        </button>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default QuizTimer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["629"], [], "import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', size = 'md' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12',\n  };\n\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={`\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <AnimatePresence mode=\"wait\" initial={false}>\n          {isDarkMode ? (\n            <motion.div\n              key=\"sun\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbSun className={`${iconSizes[size]} text-yellow-500`} />\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"moon\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbMoon className={`${iconSizes[size]} text-blue-600`} />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.button>\n  );\n};\n\n// Advanced Theme Toggle with Switch Design\nexport const ThemeSwitch = ({ className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={toggleTheme}\n      className={`\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <motion.span\n        layout\n        className={`\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: isDarkMode ? 0 : 180 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDarkMode ? (\n            <TbMoon className=\"w-2.5 h-2.5 text-primary-600\" />\n          ) : (\n            <TbSun className=\"w-2.5 h-2.5 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.span>\n    </motion.button>\n  );\n};\n\n// Theme Toggle with Label\nexport const ThemeToggleWithLabel = ({ className = '', showLabel = true }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {showLabel && (\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {isDarkMode ? 'Dark Mode' : 'Light Mode'}\n        </span>\n      )}\n      <ThemeSwitch />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["630"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst ResponsiveContainer = ({ \n  children, \n  className = '', \n  maxWidth = '7xl',\n  padding = 'responsive',\n  ...props \n}) => {\n  const maxWidths = {\n    'sm': 'max-w-sm',\n    'md': 'max-w-md',\n    'lg': 'max-w-lg',\n    'xl': 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '3xl': 'max-w-3xl',\n    '4xl': 'max-w-4xl',\n    '5xl': 'max-w-5xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    'full': 'max-w-full',\n  };\n\n  const paddings = {\n    'none': '',\n    'sm': 'px-4',\n    'md': 'px-6',\n    'lg': 'px-8',\n    'responsive': 'px-4 sm:px-6 lg:px-8',\n  };\n\n  return (\n    <div \n      className={`${maxWidths[maxWidth]} mx-auto ${paddings[padding]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Grid Component\nexport const ResponsiveGrid = ({ \n  children, \n  cols = { xs: 1, sm: 2, md: 3, lg: 4 },\n  gap = 6,\n  className = '',\n  ...props \n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-2',\n    3: 'grid-cols-3',\n    4: 'grid-cols-4',\n    5: 'grid-cols-5',\n    6: 'grid-cols-6',\n  };\n\n  const gaps = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const responsiveClasses = [\n    cols.xs && gridCols[cols.xs],\n    cols.sm && `sm:${gridCols[cols.sm]}`,\n    cols.md && `md:${gridCols[cols.md]}`,\n    cols.lg && `lg:${gridCols[cols.lg]}`,\n    cols.xl && `xl:${gridCols[cols.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`grid ${responsiveClasses} ${gaps[gap]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Text Component\nexport const ResponsiveText = ({ \n  children, \n  size = { xs: 'sm', sm: 'base', md: 'lg' },\n  weight = 'normal',\n  className = '',\n  ...props \n}) => {\n  const textSizes = {\n    'xs': 'text-xs',\n    'sm': 'text-sm',\n    'base': 'text-base',\n    'lg': 'text-lg',\n    'xl': 'text-xl',\n    '2xl': 'text-2xl',\n    '3xl': 'text-3xl',\n    '4xl': 'text-4xl',\n  };\n\n  const fontWeights = {\n    'light': 'font-light',\n    'normal': 'font-normal',\n    'medium': 'font-medium',\n    'semibold': 'font-semibold',\n    'bold': 'font-bold',\n  };\n\n  const responsiveClasses = [\n    size.xs && textSizes[size.xs],\n    size.sm && `sm:${textSizes[size.sm]}`,\n    size.md && `md:${textSizes[size.md]}`,\n    size.lg && `lg:${textSizes[size.lg]}`,\n    size.xl && `xl:${textSizes[size.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <span \n      className={`${responsiveClasses} ${fontWeights[weight]} ${className}`}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Mobile-First Responsive Component\nexport const MobileFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`block lg:hidden ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Desktop-First Responsive Component\nexport const DesktopFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`hidden lg:block ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Responsive Stack Component\nexport const ResponsiveStack = ({ \n  children, \n  direction = { xs: 'col', md: 'row' },\n  spacing = 4,\n  align = 'start',\n  justify = 'start',\n  className = '',\n  ...props \n}) => {\n  const directions = {\n    'row': 'flex-row',\n    'col': 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const spacings = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const alignments = {\n    'start': 'items-start',\n    'center': 'items-center',\n    'end': 'items-end',\n    'stretch': 'items-stretch',\n  };\n\n  const justifications = {\n    'start': 'justify-start',\n    'center': 'justify-center',\n    'end': 'justify-end',\n    'between': 'justify-between',\n    'around': 'justify-around',\n    'evenly': 'justify-evenly',\n  };\n\n  const responsiveClasses = [\n    direction.xs && directions[direction.xs],\n    direction.sm && `sm:${directions[direction.sm]}`,\n    direction.md && `md:${directions[direction.md]}`,\n    direction.lg && `lg:${directions[direction.lg]}`,\n    direction.xl && `xl:${directions[direction.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`flex ${responsiveClasses} ${spacings[spacing]} ${alignments[align]} ${justifications[justify]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Show/Hide Component\nexport const ResponsiveShow = ({ \n  children, \n  breakpoint = 'md',\n  direction = 'up',\n  className = '' \n}) => {\n  const breakpoints = {\n    'sm': direction === 'up' ? 'sm:block' : 'sm:hidden',\n    'md': direction === 'up' ? 'md:block' : 'md:hidden',\n    'lg': direction === 'up' ? 'lg:block' : 'lg:hidden',\n    'xl': direction === 'up' ? 'xl:block' : 'xl:hidden',\n  };\n\n  const baseClass = direction === 'up' ? 'hidden' : 'block';\n\n  return (\n    <div className={`${baseClass} ${breakpoints[breakpoint]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default ResponsiveContainer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["631", "632"], [], "import React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0,\n  });\n\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n    \n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage,\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    \n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      \n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n        setMetrics(prev => ({ ...prev, fps }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      \n      requestAnimationFrame(measureFPS);\n    };\n    \n    requestAnimationFrame(measureFPS);\n  }, []);\n\n  return metrics;\n};\n\n// Performance indicator component\nconst PerformanceIndicator = ({ show = false }) => {\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n\n  // Completely disable the performance indicator\n  return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          className=\"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\"\n        >\n          <div className=\"space-y-1\">\n            <div className=\"font-bold text-green-400 mb-2\">Performance Metrics</div>\n            <div>Load Time: {metrics.loadTime}ms</div>\n            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>\n            <div>FPS: {metrics.fps}</div>\n            <div className=\"text-gray-400 mt-2 text-xs\">\n              Press Ctrl+Shift+P to toggle\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\n// Lazy loading wrapper with intersection observer\nexport const LazyWrapper = ({ children, threshold = 0.1, rootMargin = '50px' }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.disconnect();\n        }\n      },\n      { threshold, rootMargin }\n    );\n\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n\n  return (\n    <div ref={setRef}>\n      {isVisible ? children : <div className=\"h-32 bg-gray-100 animate-pulse rounded\" />}\n    </div>\n  );\n};\n\n// Optimized image component with WebP support\nexport const OptimizedImage = ({ \n  src, \n  webpSrc, \n  alt, \n  className = '',\n  loading = 'lazy',\n  ...props \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const handleError = () => {\n    setImageError(true);\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n      \n      {!imageError ? (\n        <picture>\n          {webpSrc && <source srcSet={webpSrc} type=\"image/webp\" />}\n          <motion.img\n            src={src}\n            alt={alt}\n            loading={loading}\n            onError={handleError}\n            onLoad={handleLoad}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            {...props}\n          />\n        </picture>\n      ) : (\n        <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n          <span className=\"text-gray-400 text-sm\">Image not available</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Debounced search hook\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\nexport const VirtualList = ({ \n  items, \n  itemHeight = 60, \n  containerHeight = 400,\n  renderItem,\n  className = '' \n}) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(\n    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n\n  const handleScroll = (e) => {\n    setScrollTop(e.target.scrollTop);\n  };\n\n  return (\n    <div\n      ref={setContainerRef}\n      className={`overflow-auto ${className}`}\n      style={{ height: containerHeight }}\n      onScroll={handleScroll}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ transform: `translateY(${offsetY}px)` }}>\n          {visibleItems.map((item, index) => (\n            <div key={visibleStart + index} style={{ height: itemHeight }}>\n              {renderItem(item, visibleStart + index)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceIndicator;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["633", "634", "635"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  TbTrophy, \n  TbMedal, \n  TbCrown, \n  TbStar, \n  TbFlame, \n  TbTarget, \n  TbTrendingUp, \n  TbBolt,\n  TbAward,\n  TbDiamond\n} from 'react-icons/tb';\n\nconst AchievementBadge = ({ \n  achievement, \n  size = 'medium', \n  showDetails = true, \n  className = '',\n  onClick = null \n}) => {\n  // Achievement type configurations\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      icon: 'w-6 h-6',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      icon: 'w-8 h-8',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      icon: 'w-10 h-10',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const sizes = sizeConfig[size];\n  const IconComponent = config.icon;\n\n  const badgeVariants = {\n    hidden: { opacity: 0, scale: 0.8, rotate: -10 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 20\n      }\n    },\n    hover: { \n      scale: 1.1, \n      rotate: 5,\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      }\n    }\n  };\n\n  const glowVariants = {\n    hidden: { opacity: 0 },\n    visible: { \n      opacity: [0.5, 1, 0.5],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={badgeVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"hover\"\n      onClick={onClick}\n      className={`\n        relative cursor-pointer group\n        ${className}\n      `}\n    >\n      {/* Glow effect */}\n      <motion.div\n        variants={glowVariants}\n        className={`\n          absolute inset-0 rounded-full blur-md opacity-75\n          bg-gradient-to-r ${config.color}\n          ${sizes.container}\n        `}\n      />\n      \n      {/* Main badge */}\n      <div className={`\n        relative flex items-center justify-center rounded-full\n        bg-gradient-to-r ${config.color}\n        ${sizes.container} ${sizes.padding}\n        shadow-lg border-2 border-white\n        group-hover:shadow-xl transition-shadow duration-200\n      `}>\n        <IconComponent className={`${sizes.icon} text-white drop-shadow-sm`} />\n      </div>\n\n      {/* Achievement details tooltip */}\n      {showDetails && (\n        <motion.div\n          initial={{ opacity: 0, y: 10, scale: 0.9 }}\n          whileHover={{ opacity: 1, y: 0, scale: 1 }}\n          className={`\n            absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2\n            ${config.bgColor} ${config.textColor}\n            px-3 py-2 rounded-lg shadow-lg border\n            whitespace-nowrap z-10\n            pointer-events-none\n            ${sizes.text}\n          `}\n        >\n          <div className=\"font-semibold\">{config.title}</div>\n          <div className=\"text-xs opacity-75\">{config.description}</div>\n          {achievement.subject && (\n            <div className=\"text-xs font-medium mt-1\">\n              Subject: {achievement.subject}\n            </div>\n          )}\n          {achievement.earnedAt && (\n            <div className=\"text-xs opacity-60 mt-1\">\n              {new Date(achievement.earnedAt).toLocaleDateString()}\n            </div>\n          )}\n          \n          {/* Tooltip arrow */}\n          <div className={`\n            absolute top-full left-1/2 transform -translate-x-1/2\n            w-0 h-0 border-l-4 border-r-4 border-t-4\n            border-l-transparent border-r-transparent\n            ${config.bgColor.replace('bg-', 'border-t-')}\n          `} />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement list component\nexport const AchievementList = ({\n  achievements = [],\n  maxDisplay = 5,\n  size = 'medium',\n  layout = 'horizontal', // 'horizontal' or 'grid'\n  className = ''\n}) => {\n  const displayAchievements = achievements.slice(0, maxDisplay);\n  const remainingCount = Math.max(0, achievements.length - maxDisplay);\n\n  // Size configurations for the list\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const layoutClasses = layout === 'grid'\n    ? 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4'\n    : 'flex flex-wrap gap-2';\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={`${layoutClasses} ${className}`}\n    >\n      {displayAchievements.map((achievement, index) => (\n        <AchievementBadge\n          key={`${achievement.type}-${index}`}\n          achievement={achievement}\n          size={size}\n          showDetails={true}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <motion.div\n          variants={{\n            hidden: { opacity: 0, scale: 0.8 },\n            visible: { opacity: 1, scale: 1 }\n          }}\n          className={`\n            flex items-center justify-center rounded-full\n            bg-gray-100 border-2 border-gray-200\n            ${sizeConfig[size]?.container} ${sizeConfig[size]?.padding}\n            text-gray-600 font-semibold\n            ${sizeConfig[size]?.text}\n          `}\n        >\n          +{remainingCount}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement notification component\nexport const AchievementNotification = ({\n  achievement,\n  onClose,\n  autoClose = true,\n  duration = 4000\n}) => {\n  // Achievement type configurations for notifications\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  React.useEffect(() => {\n    if (autoClose && onClose) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, duration, onClose]);\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const IconComponent = config.icon;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -50, scale: 0.9 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -50, scale: 0.9 }}\n      className={`\n        fixed top-4 right-4 z-50\n        ${config.bgColor} border border-gray-200\n        rounded-lg shadow-lg p-4 max-w-sm\n      `}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <div className={`\n          flex items-center justify-center w-12 h-12 rounded-full\n          bg-gradient-to-r ${config.color}\n        `}>\n          <IconComponent className=\"w-6 h-6 text-white\" />\n        </div>\n        \n        <div className=\"flex-1\">\n          <div className={`font-semibold ${config.textColor}`}>\n            Achievement Unlocked!\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            {config.title}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {config.description}\n          </div>\n        </div>\n        \n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            ×\n          </button>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default AchievementBadge;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["636"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["637"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["638"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", ["639"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", [], [], {"ruleId": "640", "severity": 1, "message": "641", "line": 8, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 8, "endColumn": 21}, {"ruleId": "640", "severity": 1, "message": "644", "line": 8, "column": 23, "nodeType": "642", "messageId": "643", "endLine": 8, "endColumn": 34}, {"ruleId": "640", "severity": 1, "message": "645", "line": 15, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 15, "endColumn": 16}, {"ruleId": "646", "severity": 1, "message": "647", "line": 55, "column": 6, "nodeType": "648", "endLine": 55, "endColumn": 8, "suggestions": "649"}, {"ruleId": "646", "severity": 1, "message": "650", "line": 102, "column": 6, "nodeType": "648", "endLine": 102, "endColumn": 33, "suggestions": "651"}, {"ruleId": "646", "severity": 1, "message": "652", "line": 109, "column": 6, "nodeType": "648", "endLine": 109, "endColumn": 25, "suggestions": "653"}, {"ruleId": "640", "severity": 1, "message": "654", "line": 112, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 112, "endColumn": 23}, {"ruleId": "646", "severity": 1, "message": "655", "line": 37, "column": 6, "nodeType": "648", "endLine": 37, "endColumn": 8, "suggestions": "656"}, {"ruleId": "640", "severity": 1, "message": "657", "line": 1, "column": 35, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 41}, {"ruleId": "646", "severity": 1, "message": "658", "line": 81, "column": 6, "nodeType": "648", "endLine": 81, "endColumn": 8, "suggestions": "659"}, {"ruleId": "646", "severity": 1, "message": "660", "line": 93, "column": 6, "nodeType": "648", "endLine": 93, "endColumn": 8, "suggestions": "661"}, {"ruleId": "646", "severity": 1, "message": "662", "line": 65, "column": 8, "nodeType": "648", "endLine": 65, "endColumn": 32, "suggestions": "663"}, {"ruleId": "646", "severity": 1, "message": "664", "line": 776, "column": 6, "nodeType": "648", "endLine": 776, "endColumn": 81, "suggestions": "665"}, {"ruleId": "640", "severity": 1, "message": "666", "line": 19, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 19, "endColumn": 16}, {"ruleId": "640", "severity": 1, "message": "667", "line": 20, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 20, "endColumn": 11}, {"ruleId": "640", "severity": 1, "message": "668", "line": 29, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 29, "endColumn": 11}, {"ruleId": "640", "severity": 1, "message": "669", "line": 30, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 30, "endColumn": 11}, {"ruleId": "640", "severity": 1, "message": "670", "line": 31, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 31, "endColumn": 18}, {"ruleId": "640", "severity": 1, "message": "671", "line": 32, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 32, "endColumn": 9}, {"ruleId": "640", "severity": 1, "message": "672", "line": 33, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 33, "endColumn": 13}, {"ruleId": "640", "severity": 1, "message": "673", "line": 34, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 34, "endColumn": 8}, {"ruleId": "640", "severity": 1, "message": "674", "line": 35, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 35, "endColumn": 13}, {"ruleId": "640", "severity": 1, "message": "675", "line": 36, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 36, "endColumn": 9}, {"ruleId": "640", "severity": 1, "message": "676", "line": 38, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 38, "endColumn": 14}, {"ruleId": "640", "severity": 1, "message": "677", "line": 39, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 39, "endColumn": 6}, {"ruleId": "640", "severity": 1, "message": "678", "line": 40, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 40, "endColumn": 18}, {"ruleId": "640", "severity": 1, "message": "679", "line": 41, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 41, "endColumn": 15}, {"ruleId": "640", "severity": 1, "message": "680", "line": 42, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 42, "endColumn": 10}, {"ruleId": "640", "severity": 1, "message": "681", "line": 43, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 43, "endColumn": 14}, {"ruleId": "646", "severity": 1, "message": "682", "line": 71, "column": 9, "nodeType": "683", "endLine": 75, "endColumn": 29}, {"ruleId": "640", "severity": 1, "message": "684", "line": 4, "column": 19, "nodeType": "642", "messageId": "643", "endLine": 4, "endColumn": 24}, {"ruleId": "646", "severity": 1, "message": "685", "line": 67, "column": 6, "nodeType": "648", "endLine": 67, "endColumn": 8, "suggestions": "686"}, {"ruleId": "640", "severity": 1, "message": "687", "line": 11, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 11, "endColumn": 11}, {"ruleId": "640", "severity": 1, "message": "688", "line": 17, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 17, "endColumn": 9}, {"ruleId": "640", "severity": 1, "message": "645", "line": 18, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 18, "endColumn": 9}, {"ruleId": "646", "severity": 1, "message": "689", "line": 103, "column": 6, "nodeType": "648", "endLine": 103, "endColumn": 53, "suggestions": "690"}, {"ruleId": "646", "severity": 1, "message": "689", "line": 126, "column": 6, "nodeType": "648", "endLine": 126, "endColumn": 8, "suggestions": "691"}, {"ruleId": "640", "severity": 1, "message": "692", "line": 2, "column": 18, "nodeType": "642", "messageId": "643", "endLine": 2, "endColumn": 33}, {"ruleId": "640", "severity": 1, "message": "693", "line": 21, "column": 53, "nodeType": "642", "messageId": "643", "endLine": 21, "endColumn": 67}, {"ruleId": "640", "severity": 1, "message": "694", "line": 73, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 73, "endColumn": 18}, {"ruleId": "640", "severity": 1, "message": "695", "line": 73, "column": 20, "nodeType": "642", "messageId": "643", "endLine": 73, "endColumn": 31}, {"ruleId": "640", "severity": 1, "message": "696", "line": 74, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 74, "endColumn": 19}, {"ruleId": "640", "severity": 1, "message": "697", "line": 74, "column": 21, "nodeType": "642", "messageId": "643", "endLine": 74, "endColumn": 33}, {"ruleId": "640", "severity": 1, "message": "698", "line": 75, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 75, "endColumn": 24}, {"ruleId": "640", "severity": 1, "message": "699", "line": 78, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 78, "endColumn": 27}, {"ruleId": "640", "severity": 1, "message": "700", "line": 80, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 80, "endColumn": 24}, {"ruleId": "640", "severity": 1, "message": "701", "line": 86, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 86, "endColumn": 18}, {"ruleId": "640", "severity": 1, "message": "702", "line": 87, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 87, "endColumn": 23}, {"ruleId": "646", "severity": 1, "message": "703", "line": 783, "column": 6, "nodeType": "648", "endLine": 783, "endColumn": 8, "suggestions": "704"}, {"ruleId": "640", "severity": 1, "message": "705", "line": 804, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 804, "endColumn": 24}, {"ruleId": "640", "severity": 1, "message": "706", "line": 892, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 892, "endColumn": 29}, {"ruleId": "640", "severity": 1, "message": "707", "line": 1322, "column": 39, "nodeType": "642", "messageId": "643", "endLine": 1322, "endColumn": 48}, {"ruleId": "708", "severity": 1, "message": "709", "line": 1927, "column": 27, "nodeType": "710", "messageId": "711", "endLine": 1927, "endColumn": 28}, {"ruleId": "640", "severity": 1, "message": "712", "line": 1, "column": 38, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 46}, {"ruleId": "640", "severity": 1, "message": "713", "line": 8, "column": 12, "nodeType": "642", "messageId": "643", "endLine": 8, "endColumn": 19}, {"ruleId": "646", "severity": 1, "message": "714", "line": 58, "column": 8, "nodeType": "648", "endLine": 58, "endColumn": 10, "suggestions": "715"}, {"ruleId": "646", "severity": 1, "message": "716", "line": 90, "column": 6, "nodeType": "648", "endLine": 90, "endColumn": 35, "suggestions": "717"}, {"ruleId": "640", "severity": 1, "message": "718", "line": 92, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 92, "endColumn": 26}, {"ruleId": "640", "severity": 1, "message": "719", "line": 12, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 12, "endColumn": 17}, {"ruleId": "640", "severity": 1, "message": "673", "line": 22, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 22, "endColumn": 8}, {"ruleId": "640", "severity": 1, "message": "720", "line": 32, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 32, "endColumn": 17}, {"ruleId": "640", "severity": 1, "message": "721", "line": 38, "column": 19, "nodeType": "642", "messageId": "643", "endLine": 38, "endColumn": 29}, {"ruleId": "646", "severity": 1, "message": "722", "line": 177, "column": 6, "nodeType": "648", "endLine": 177, "endColumn": 8, "suggestions": "723"}, {"ruleId": "640", "severity": 1, "message": "724", "line": 1, "column": 38, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 44}, {"ruleId": "640", "severity": 1, "message": "719", "line": 5, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 5, "endColumn": 17}, {"ruleId": "640", "severity": 1, "message": "725", "line": 35, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 35, "endColumn": 20}, {"ruleId": "646", "severity": 1, "message": "726", "line": 61, "column": 6, "nodeType": "648", "endLine": 61, "endColumn": 26, "suggestions": "727"}, {"ruleId": "646", "severity": 1, "message": "728", "line": 93, "column": 6, "nodeType": "648", "endLine": 93, "endColumn": 8, "suggestions": "729"}, {"ruleId": "646", "severity": 1, "message": "730", "line": 213, "column": 6, "nodeType": "648", "endLine": 213, "endColumn": 20, "suggestions": "731"}, {"ruleId": "640", "severity": 1, "message": "719", "line": 3, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 3, "endColumn": 17}, {"ruleId": "640", "severity": 1, "message": "732", "line": 10, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 10, "endColumn": 14}, {"ruleId": "640", "severity": 1, "message": "684", "line": 10, "column": 25, "nodeType": "642", "messageId": "643", "endLine": 10, "endColumn": 30}, {"ruleId": "640", "severity": 1, "message": "733", "line": 10, "column": 32, "nodeType": "642", "messageId": "643", "endLine": 10, "endColumn": 37}, {"ruleId": "640", "severity": 1, "message": "734", "line": 10, "column": 39, "nodeType": "642", "messageId": "643", "endLine": 10, "endColumn": 45}, {"ruleId": "640", "severity": 1, "message": "735", "line": 18, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 18, "endColumn": 21}, {"ruleId": "640", "severity": 1, "message": "736", "line": 19, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 19, "endColumn": 14}, {"ruleId": "640", "severity": 1, "message": "737", "line": 20, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 20, "endColumn": 22}, {"ruleId": "640", "severity": 1, "message": "738", "line": 31, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 31, "endColumn": 30}, {"ruleId": "646", "severity": 1, "message": "739", "line": 64, "column": 6, "nodeType": "648", "endLine": 64, "endColumn": 32, "suggestions": "740"}, {"ruleId": "646", "severity": 1, "message": "728", "line": 98, "column": 6, "nodeType": "648", "endLine": 98, "endColumn": 8, "suggestions": "741"}, {"ruleId": "640", "severity": 1, "message": "742", "line": 100, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 100, "endColumn": 21}, {"ruleId": "640", "severity": 1, "message": "743", "line": 182, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 182, "endColumn": 33}, {"ruleId": "640", "severity": 1, "message": "744", "line": 192, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 192, "endColumn": 32}, {"ruleId": "640", "severity": 1, "message": "745", "line": 197, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 197, "endColumn": 26}, {"ruleId": "640", "severity": 1, "message": "746", "line": 207, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 207, "endColumn": 26}, {"ruleId": "640", "severity": 1, "message": "747", "line": 226, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 226, "endColumn": 19}, {"ruleId": "640", "severity": 1, "message": "748", "line": 18, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 18, "endColumn": 14}, {"ruleId": "640", "severity": 1, "message": "749", "line": 23, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 23, "endColumn": 14}, {"ruleId": "640", "severity": 1, "message": "734", "line": 26, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 26, "endColumn": 16}, {"ruleId": "640", "severity": 1, "message": "750", "line": 32, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 32, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "751", "line": 40, "column": 38, "nodeType": "648", "endLine": 40, "endColumn": 40, "suggestions": "752"}, {"ruleId": "640", "severity": 1, "message": "753", "line": 2, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 2, "endColumn": 13}, {"ruleId": "640", "severity": 1, "message": "754", "line": 74, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 74, "endColumn": 23}, {"ruleId": "640", "severity": 1, "message": "755", "line": 19, "column": 11, "nodeType": "642", "messageId": "643", "endLine": 19, "endColumn": 24}, {"ruleId": "756", "severity": 1, "message": "757", "line": 73, "column": 111, "nodeType": "758", "messageId": "759", "endLine": 73, "endColumn": 112, "suggestions": "760"}, {"ruleId": "756", "severity": 1, "message": "757", "line": 95, "column": 89, "nodeType": "758", "messageId": "759", "endLine": 95, "endColumn": 90, "suggestions": "761"}, {"ruleId": "646", "severity": 1, "message": "762", "line": 126, "column": 6, "nodeType": "648", "endLine": 126, "endColumn": 32, "suggestions": "763", "suppressions": "764"}, {"ruleId": "640", "severity": 1, "message": "765", "line": 1, "column": 17, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 26}, {"ruleId": "640", "severity": 1, "message": "766", "line": 1, "column": 28, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 36}, {"ruleId": "640", "severity": 1, "message": "767", "line": 20, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 20, "endColumn": 8}, {"ruleId": "640", "severity": 1, "message": "768", "line": 21, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 21, "endColumn": 11}, {"ruleId": "640", "severity": 1, "message": "667", "line": 22, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 22, "endColumn": 11}, {"ruleId": "646", "severity": 1, "message": "769", "line": 95, "column": 6, "nodeType": "648", "endLine": 95, "endColumn": 15, "suggestions": "770"}, {"ruleId": "640", "severity": 1, "message": "771", "line": 9, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 9, "endColumn": 8}, {"ruleId": "640", "severity": 1, "message": "720", "line": 23, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 23, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "772", "line": 37, "column": 6, "nodeType": "648", "endLine": 37, "endColumn": 8, "suggestions": "773"}, {"ruleId": "640", "severity": 1, "message": "774", "line": 42, "column": 5, "nodeType": "642", "messageId": "643", "endLine": 42, "endColumn": 15}, {"ruleId": "640", "severity": 1, "message": "775", "line": 7, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 7, "endColumn": 14}, {"ruleId": "646", "severity": 1, "message": "776", "line": 32, "column": 6, "nodeType": "648", "endLine": 32, "endColumn": 15, "suggestions": "777"}, {"ruleId": "640", "severity": 1, "message": "778", "line": 3, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 3, "endColumn": 25}, {"ruleId": "640", "severity": 1, "message": "779", "line": 24, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 24, "endColumn": 18}, {"ruleId": "646", "severity": 1, "message": "664", "line": 180, "column": 6, "nodeType": "648", "endLine": 180, "endColumn": 74, "suggestions": "780"}, {"ruleId": "646", "severity": 1, "message": "781", "line": 212, "column": 6, "nodeType": "648", "endLine": 212, "endColumn": 8, "suggestions": "782"}, {"ruleId": "646", "severity": 1, "message": "783", "line": 225, "column": 6, "nodeType": "648", "endLine": 225, "endColumn": 27, "suggestions": "784"}, {"ruleId": "640", "severity": 1, "message": "785", "line": 8, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 8, "endColumn": 9}, {"ruleId": "640", "severity": 1, "message": "786", "line": 21, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 21, "endColumn": 15}, {"ruleId": "640", "severity": 1, "message": "787", "line": 30, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 30, "endColumn": 21}, {"ruleId": "646", "severity": 1, "message": "788", "line": 56, "column": 6, "nodeType": "648", "endLine": 56, "endColumn": 8, "suggestions": "789"}, {"ruleId": "640", "severity": 1, "message": "790", "line": 1, "column": 38, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 46}, {"ruleId": "640", "severity": 1, "message": "791", "line": 59, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 59, "endColumn": 22}, {"ruleId": "640", "severity": 1, "message": "792", "line": 112, "column": 23, "nodeType": "642", "messageId": "643", "endLine": 112, "endColumn": 34}, {"ruleId": "640", "severity": 1, "message": "793", "line": 2, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 2, "endColumn": 16}, {"ruleId": "794", "severity": 1, "message": "795", "line": 69, "column": 3, "nodeType": "796", "messageId": "797", "endLine": 90, "endColumn": 5}, {"ruleId": "640", "severity": 1, "message": "798", "line": 198, "column": 10, "nodeType": "642", "messageId": "643", "endLine": 198, "endColumn": 22}, {"ruleId": "640", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 5, "endColumn": 10}, {"ruleId": "640", "severity": 1, "message": "800", "line": 12, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 12, "endColumn": 10}, {"ruleId": "640", "severity": 1, "message": "801", "line": 13, "column": 3, "nodeType": "642", "messageId": "643", "endLine": 13, "endColumn": 12}, {"ruleId": "640", "severity": 1, "message": "802", "line": 62, "column": 9, "nodeType": "642", "messageId": "643", "endLine": 62, "endColumn": 32}, {"ruleId": "640", "severity": 1, "message": "803", "line": 50, "column": 5, "nodeType": "642", "messageId": "643", "endLine": 50, "endColumn": 14}, {"ruleId": "646", "severity": 1, "message": "804", "line": 34, "column": 6, "nodeType": "648", "endLine": 34, "endColumn": 8, "suggestions": "805"}, {"ruleId": "640", "severity": 1, "message": "806", "line": 1, "column": 8, "nodeType": "642", "messageId": "643", "endLine": 1, "endColumn": 13}, "no-unused-vars", "'HideLoading' is defined but never used.", "Identifier", "unusedVar", "'ShowLoading' is defined but never used.", "'TbHome' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'getUserData' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["807"], "React Hook useEffect has missing dependencies: 'dispatch', 'user?.isAdmin', 'user?.paymentRequired', and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["808"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["809"], "'getButtonClass' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAll'. Either include it or remove the dependency array.", ["810"], "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["811"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["812"], "React Hook useEffect has a missing dependency: 'paymentInProgress'. Either include it or remove the dependency array.", ["813"], "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["814"], "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbPlay' is defined but never used.", "'TbDownload' is defined but never used.", "'TbEye' is defined but never used.", "'TbCalendar' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbX' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "'TbSubtitles' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 127) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "'Modal' is defined but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["815"], "'TbTrophy' is defined but never used.", "'TbStar' is defined but never used.", "React Hook useEffect has a missing dependency: 'getUserResults'. Either include it or remove the dependency array.", ["816"], ["817"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["818"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["819"], "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["820"], "'handleTableChange' is assigned a value but never used.", "'PageTitle' is defined but never used.", "'navigate' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["821"], "'useRef' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["822"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["823"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["824"], "'Form' is defined but never used.", "'Input' is defined but never used.", "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'edit' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "'showLevelChangeModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["825"], ["826"], "'handleChange' is assigned a value but never used.", "'handleLevelChangeConfirm' is assigned a value but never used.", "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageChange' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", "'verifyUser' is assigned a value but never used.", "'Rate' is defined but never used.", "'Image2' is defined but never used.", "'reviews' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getReviews'. Either include it or remove the dependency array.", ["827"], "'axios' is defined but never used.", "'handleKeyPress' is assigned a value but never used.", "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["828", "829"], ["830", "831"], "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["832"], ["833"], "'useEffect' is defined but never used.", "'useState' is defined but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["834"], "'FaCog' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGenerationHistory'. Either include it or remove the dependency array.", ["835"], "'needsLogin' is assigned a value but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoRefresh'. Either include it or remove the dependency array.", ["836"], "'validateSession' is defined but never used.", "'examData' is assigned a value but never used.", ["837"], "React Hook useEffect has missing dependencies: 'getExamData' and 'id'. Either include them or remove the dependency array.", ["838"], "React Hook useEffect has a missing dependency: 'startTimer'. Either include it or remove the dependency array.", ["839"], "'FaHome' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'inspiringQuotes.length'. Either include it or remove the dependency array.", ["840"], "'Fragment' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'toggleTheme' is assigned a value but never used.", "'motion' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbAward' is defined but never used.", "'TbDiamond' is defined but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'xpAwarded' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["841"], "'React' is defined but never used.", {"desc": "842", "fix": "843"}, {"desc": "844", "fix": "845"}, {"desc": "846", "fix": "847"}, {"desc": "848", "fix": "849"}, {"desc": "850", "fix": "851"}, {"desc": "852", "fix": "853"}, {"desc": "854", "fix": "855"}, {"desc": "856", "fix": "857"}, {"desc": "858", "fix": "859"}, {"desc": "860", "fix": "861"}, {"desc": "862", "fix": "863"}, {"desc": "864", "fix": "865"}, {"desc": "866", "fix": "867"}, {"desc": "868", "fix": "869"}, {"desc": "870", "fix": "871"}, {"desc": "872", "fix": "873"}, {"desc": "874", "fix": "875"}, {"desc": "876", "fix": "877"}, {"desc": "878", "fix": "879"}, {"desc": "874", "fix": "880"}, {"desc": "881", "fix": "882"}, {"messageId": "883", "fix": "884", "desc": "885"}, {"messageId": "886", "fix": "887", "desc": "888"}, {"messageId": "883", "fix": "889", "desc": "885"}, {"messageId": "886", "fix": "890", "desc": "888"}, {"desc": "891", "fix": "892"}, {"kind": "893", "justification": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, "Update the dependencies array to be: [getUserData, navigate]", {"range": "911", "text": "912"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", {"range": "913", "text": "914"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "915", "text": "916"}, "Update the dependencies array to be: [fetchAll]", {"range": "917", "text": "918"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "919", "text": "920"}, "Update the dependencies array to be: [getExamsData]", {"range": "921", "text": "922"}, "Update the dependencies array to be: [user, subscriptionData, paymentInProgress]", {"range": "923", "text": "924"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "925", "text": "926"}, "Update the dependencies array to be: [getData]", {"range": "927", "text": "928"}, "Update the dependencies array to be: [dispatch, user.level, user.class, user._id, getUserResults]", {"range": "929", "text": "930"}, "Update the dependencies array to be: [getUserResults]", {"range": "931", "text": "932"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "933", "text": "934"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "935", "text": "936"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "937", "text": "938"}, "Update the dependencies array to be: [getUsersData]", {"range": "939", "text": "940"}, "Update the dependencies array to be: [currentPage, fetchQuestions, limit]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [getUserData]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "947", "text": "948"}, {"range": "949", "text": "944"}, "Update the dependencies array to be: [getReviews]", {"range": "950", "text": "951"}, "removeEscape", {"range": "952", "text": "894"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "953", "text": "954"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "955", "text": "894"}, {"range": "956", "text": "954"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "957", "text": "958"}, "directive", "", "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [fetchGenerationHistory]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [handleAutoRefresh, visible]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingPercentage, examData.passingMarks, id, navigate, selectedOptions]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [getExamData, id]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [examData, questions, startTimer]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [inspiringQuotes.length]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "973", "text": "974"}, [1841, 1843], "[get<PERSON><PERSON><PERSON><PERSON>, navigate]", [3208, 3235], "[dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", [3396, 3415], "[user, activeRoute, verifyPaymentStatus]", [1156, 1158], "[fetchAll]", [2375, 2377], "[getExamData, params.id]", [2372, 2374], "[getExamsData]", [2446, 2470], "[user, subscriptionData, paymentInProgress]", [28673, 28748], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [1901, 1903], "[getData]", [3306, 3353], "[dispatch, user.level, user.class, user._id, getUserResults]", [4203, 4205], "[getUserResults]", [29248, 29250], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [1990, 1992], "[dispatch, getUserData]", [2529, 2558], "[filters, getData, pagination]", [5429, 5431], "[getUsersData]", [2250, 2270], "[currentPage, fetchQuestions, limit]", [3018, 3020], "[getUserData]", [6129, 6143], "[editQuestion, form2]", [2000, 2026], "[getUserStats, rankingData, userDetails]", [2968, 2970], [1400, 1402], "[getReviews]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]", [2411, 2420], "[fetchMaterials, filters]", [1203, 1205], "[fetchGenerationHistory]", [1110, 1119], "[handleAutoRefresh, visible]", [6318, 6386], "[user, dispatch, questions, startTime, examData?.duration, examData.passingPercentage, examData.passingMarks, id, navigate, selectedOptions]", [7110, 7112], "[getExamData, id]", [7377, 7398], "[examData, questions, startTimer]", [1591, 1593], "[inspiringQuotes.length]", [920, 922], "[fetchDashboardData]"]