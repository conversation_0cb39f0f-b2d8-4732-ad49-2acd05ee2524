const authMiddleware = require("../middlewares/authMiddleware");
const Exam = require("../models/examModel");
const User = require("../models/userModel");
const Report = require("../models/reportModel");
const Subscription = require("../models/subscriptionModel");
const xpCalculationService = require("../services/xpCalculationService");
const enhancedXPService = require("../services/enhancedXPService");
const streakTrackingService = require("../services/streakTrackingService");
const router = require("express").Router();

// add report and update user statistics with XP system

router.post("/add-report", authMiddleware, async (req, res) => {
  try {
    console.log('📝 Quiz submission received:', {
      userId: req.body.user,
      examId: req.body.exam,
      score: req.body.result?.score,
      verdict: req.body.result?.verdict
    });

    const newReport = new Report(req.body);
    await newReport.save();
    console.log('✅ Report saved successfully');

    // Update user statistics and award XP
    const { user: userId, result, exam: examId } = req.body;
    let xpData = null;

    if (userId && result && examId) {
      console.log('💰 Starting XP calculation...');
      xpData = await updateUserStatisticsWithXP(userId, result, examId, newReport);
      console.log('🎯 XP calculation result:', xpData);
    } else {
      console.log('⚠️ Missing required data for XP calculation:', { userId, result: !!result, examId });
    }

    res.send({
      message: "Attempt added successfully",
      success: true,
      xpData: xpData // Include XP information in response
    });
  } catch (error) {
    console.error('❌ Error in add-report:', error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Enhanced helper function to update user statistics with XP system
async function updateUserStatisticsWithXP(userId, result, examId, report) {
  try {
    console.log('🔍 Starting updateUserStatisticsWithXP for user:', userId);

    // Validate inputs
    if (!userId || !examId || !result) {
      console.log('❌ Invalid input parameters:', { userId, examId, result: !!result });
      return null;
    }

    // Validate ObjectId format
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(examId)) {
      console.log('❌ Invalid ObjectId format:', { userId, examId });
      return null;
    }

    const user = await User.findById(userId);
    const exam = await Exam.findById(examId);

    if (!user || !exam) {
      console.log('❌ User or exam not found:', { user: !!user, exam: !!exam });
      return null;
    }

    console.log(`👤 User: ${user.name}, 📝 Exam: ${exam.name}`);

    // Get all reports for this user to calculate accurate stats
    const allReports = await Report.find({ user: userId }).populate('exam');

    // Calculate total points from all reports (legacy system)
    const totalPoints = allReports.reduce((sum, report) => {
      return sum + (report.result.points || 0);
    }, 0);

    // Calculate average score
    const scores = allReports.map(report => report.result.score || 0);
    const averageScore = scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;

    // Calculate streak
    let currentStreak = 0;
    let bestStreak = user.bestStreak || 0;

    // Check recent reports for current streak (sorted by creation date)
    const recentReports = allReports.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    for (const report of recentReports) {
      if (report.result.verdict === 'Pass') {
        currentStreak++;
      } else {
        break;
      }
    }

    // Update best streak if current is higher
    bestStreak = Math.max(bestStreak, currentStreak);

    // Update user document with legacy stats and activity tracking
    await User.findByIdAndUpdate(userId, {
      totalQuizzesTaken: allReports.length,
      totalPointsEarned: totalPoints,
      averageScore: Math.round(averageScore * 100) / 100,
      currentStreak: currentStreak,
      bestStreak: bestStreak,
      // Update activity tracking
      lastActivity: new Date(),
      'activityTracking.lastLoginDate': new Date()
    });

    // ENHANCED XP SYSTEM: Update streaks and calculate XP

    // 1. Update login streak (if user hasn't logged in today)
    await streakTrackingService.updateLoginStreak(userId);

    // 2. Update quiz streaks and performance tracking
    const streakResult = await streakTrackingService.updateQuizStreaks(userId, result, exam);

    // 3. Determine if this is first attempt for this exam
    const previousAttempts = allReports.filter(r =>
      r.exam && r.exam._id.toString() === examId.toString()
    );
    const isFirstAttempt = previousAttempts.length <= 1; // Current attempt is included

    // Get previous score for improvement calculation
    const previousScore = previousAttempts.length > 1 ?
      previousAttempts[previousAttempts.length - 2].result.score : null;

    // 4. Calculate XP using simplified system (fallback)
    console.log('💰 Calculating XP with data:', {
      score: result.score,
      verdict: result.verdict,
      correctAnswers: result.correctAnswers?.length,
      isFirstAttempt,
      previousScore
    });

    let xpResult;
    try {
      xpResult = await enhancedXPService.calculateQuizXP({
        userId: userId,
        examData: {
          _id: examId,
          subject: exam.subject || exam.category || 'General',
          difficulty: exam.difficulty || exam.difficultyLevel || 'medium',
          duration: exam.duration || 30,
          name: exam.name
        },
        result: result,
        timeSpent: result.timeSpent || 0,
        isFirstAttempt: isFirstAttempt,
        previousScore: previousScore
      });

      console.log('🎯 XP calculation result:', {
        xpAwarded: xpResult.xpAwarded,
        breakdown: xpResult.breakdown
      });
    } catch (xpError) {
      console.error('❌ Enhanced XP calculation failed, using simple calculation:', xpError);

      // Simple XP calculation fallback
      const correctCount = result.correctAnswers?.length || 0;
      const score = result.score || 0;
      const verdict = result.verdict || 'Fail';

      let xpAwarded = 10; // Base completion XP
      xpAwarded += correctCount * 5; // 5 XP per correct answer

      if (score === 100) xpAwarded += 25; // Perfect score bonus
      if (isFirstAttempt && verdict === 'Pass') xpAwarded += 15; // First attempt bonus

      xpResult = {
        xpAwarded: xpAwarded,
        breakdown: {
          baseCompletion: 10,
          correctAnswers: correctCount * 5,
          perfectScore: score === 100 ? 25 : 0,
          firstAttemptBonus: (isFirstAttempt && verdict === 'Pass') ? 15 : 0
        }
      };

      console.log('🎯 Simple XP calculation result:', {
        xpAwarded: xpResult.xpAwarded,
        breakdown: xpResult.breakdown
      });
    }

    // 5. Award XP to user
    let xpAwardResult;
    try {
      xpAwardResult = await enhancedXPService.awardXP({
        userId: userId,
        xpAmount: xpResult.xpAwarded,
        transactionType: 'quiz_completion',
        sourceId: examId,
        sourceModel: 'exams',
        breakdown: xpResult.breakdown,
        quizData: {
          examId: examId,
          subject: exam.subject || exam.category || 'General',
          difficulty: exam.difficulty || exam.difficultyLevel || 'medium',
          questionsTotal: (result.correctAnswers?.length || 0) + (result.wrongAnswers?.length || 0),
          questionsCorrect: result.correctAnswers?.length || 0,
          timeSpent: result.timeSpent || 0,
          score: result.score || 0,
          isFirstAttempt: isFirstAttempt,
        },
        metadata: {
          reportId: report._id,
          verdict: result.verdict,
          streakInfo: streakResult,
          ...xpResult.metadata
        }
      });
    } catch (awardError) {
      console.error('❌ Enhanced XP awarding failed, using simple update:', awardError);

      // Simple XP awarding fallback
      const currentUser = await User.findById(userId);
      const newTotalXP = (currentUser.totalXP || 0) + xpResult.xpAwarded;

      await User.findByIdAndUpdate(userId, {
        $inc: {
          totalXP: xpResult.xpAwarded,
          lifetimeXP: xpResult.xpAwarded,
          seasonXP: xpResult.xpAwarded
        },
        $set: {
          'xpStats.lastXPGain': new Date()
        }
      }, { runValidators: false });

      xpAwardResult = {
        success: true,
        xpAwarded: xpResult.xpAwarded,
        newTotalXP: newTotalXP,
        levelUp: false,
        newLevel: currentUser.currentLevel || 1
      };

      console.log('✅ Simple XP awarding completed:', xpAwardResult);
    }

    console.log(`XP awarded to user ${user.name}: ${xpResult.xpAwarded} XP`, {
      breakdown: xpResult.breakdown,
      levelUp: xpAwardResult.levelUp,
      newLevel: xpAwardResult.newLevel
    });

    // Return XP data for client
    return {
      xpAwarded: xpResult.xpAwarded,
      xpBreakdown: xpResult.breakdown,
      levelUp: xpAwardResult.levelUp,
      newLevel: xpAwardResult.newLevel,
      newTotalXP: xpAwardResult.newTotalXP,
      currentLevel: user.currentLevel,
      currentStreak: user.currentStreak,
      achievements: xpResult.achievements || []
    };

  } catch (error) {
    console.error('Error updating user statistics with XP:', error);
    return null;
  }
}

// get all reports

router.post("/get-all-reports", authMiddleware, async (req, res) => {
  try {
    const { examName, userName, page, limit } = req.body;

    // Fetch exams matching the name
    const exams = await Exam.find({
      name: {
        $regex: examName,
        $options: "i", // Case-insensitive matching
      },
    });

    const matchedExamIds = exams.map((exam) => exam._id);

    // Fetch users matching the name
    const users = await User.find({
      name: {
        $regex: userName,
        $options: "i",
      },
    });

    const matchedUserIds = users.map((user) => user._id);

    // Fetch reports with pagination
    const reports = await Report.find({
      exam: {
        $in: matchedExamIds,
      },
      user: {
        $in: matchedUserIds,
      },
    })
      .populate("exam")
      .populate("user")
      .sort({ createdAt: -1 }) // Sort by most recent
      .skip((page - 1) * limit) // Skip documents for previous pages
      .limit(parseInt(limit)); // Limit number of documents per page

    // Count total matching documents
    const totalReports = await Report.countDocuments({
      exam: {
        $in: matchedExamIds,
      },
      user: {
        $in: matchedUserIds,
      },
    });

    res.send({
      message: "Attempts fetched successfully",
      data: reports,
      success: true,
      pagination: {
        totalReports,
        currentPage: page,
        totalPages: Math.ceil(totalReports / limit),
      },
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});


// get all reports by user
router.post("/get-all-reports-by-user", authMiddleware, async (req, res) => {
  try {
    const reports = await Report.find({ user: req.body.userId })
      .populate("exam")
      .populate("user")
      .sort({ createdAt: -1 });
    res.send({
      message: "Attempts fetched successfully",
      data: reports,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// get all reports for ranking
router.get("/get-all-reports-for-ranking", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Fetch the requesting user's details
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Determine match conditions based on user's level
    let matchConditions;
    if (user.level === "secondary") {
      // If viewing user is secondary, show only users who are secondary AND took secondary exams
      matchConditions = {
        $and: [
          {
            "userDetails.level": "secondary",
          },
          {
            "examDetails.level": "secondary"
          }
        ]
      };
    } else if (user.level === "advance") {
      // If viewing user is advance, show only users who are advance AND took advance exams
      matchConditions = {
        $and: [
          {
            "userDetails.level": "advance",
          },
          {
            "examDetails.level": "advance"
          }
        ]
      };
    } else {
      // For primary level, exclude any reports where either user or exam is secondary/advance
      matchConditions = {
        $and: [
          {
            "userDetails.level": { $nin: ["secondary", "advance"] }
          },
          {
            "examDetails.level": { $nin: ["secondary", "advance"] }
          }
        ]
      };
    }

    const distinctPassReportsCountPerUser = await Report.aggregate([
      // Stage 1: Lookup exam details
      {
        $lookup: {
          from: "exams",
          localField: "exam",
          foreignField: "_id",
          as: "examDetails"
        }
      },
      // Stage 2: Unwind exam details
      {
        $unwind: "$examDetails"
      },
      // Stage 3: Lookup user details
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails"
        }
      },
      // Stage 4: Unwind user details
      {
        $unwind: "$userDetails"
      },
      // Stage 5: Match conditions and exclude admins
      {
        $match: {
          ...matchConditions,
          "userDetails.isAdmin": { $ne: true }, // Exclude admin users from rankings
          "userDetails.isBlocked": { $ne: true } // Also exclude blocked users
        }
      },

      // Stage 6: Group by user and exam for distinct score & attempts
      {
        $group: {
          _id: { user: "$user", exam: "$exam" },
          maxScore: {
            $max: {
              $cond: [{ $eq: ["$result.verdict", "Pass"] }, "$result.score", 0]
            }
          },
          maxPoints: {
            $max: {
              $cond: [{ $ne: ["$result.points", null] }, "$result.points", 0]
            }
          },
          totalMarks: { $first: "$examDetails.totalMarks" },
          attempts: { $sum: 1 },
          userDetails: { $first: "$userDetails" }
        }
      },
      // Stage 7: Group by user to compute aggregates
      {
        $group: {
          _id: "$_id.user",
          totalMaxScores: { $sum: "$maxScore" },
          totalPoints: { $sum: "$maxPoints" },
          totalPossibleScores: { $sum: "$totalMarks" },
          retryCount: { $sum: { $subtract: ["$attempts", 1] } },
          quizzesTaken: { $sum: 1 },
          userDetails: { $first: "$userDetails" },
          passedExamsCount: {
            $sum: {
              $cond: [{ $gt: ["$maxScore", 0] }, 1, 0]
            }
          }

        }
      },
      // Stage 8: Project final output with rankingScore
      {
        $project: {
          userId: "$_id",
          userPhoto: "$userDetails.profileImage",
          userName: "$userDetails.name",
          userSchool: "$userDetails.school",
          userClass: "$userDetails.class",
          userLevel: "$userDetails.level",
          subscriptionStatus: "$userDetails.subscriptionStatus",
          subscriptionEndDate: "$userDetails.subscriptionEndDate",
          subscriptionPlan: "$userDetails.subscriptionPlan",
          achievements: "$userDetails.achievements",
          totalPoints: 1,
          quizzesTaken: 1,
          passedExamsCount: 1,
          retryCount: 1,
          scoreRatio: {
            $cond: [
              { $gt: ["$totalPossibleScores", 0] },
              { $divide: ["$totalMaxScores", "$totalPossibleScores"] },
              0
            ]
          },
          rankingScore: {
            $add: [
              "$totalPoints",
              { $multiply: ["$passedExamsCount", 50] }, // Bonus for passing exams
              { $subtract: [0, { $multiply: ["$retryCount", 5] }] }, // Penalty for retries
              // Premium user bonus
              {
                $cond: [
                  { $in: ["$userDetails.subscriptionStatus", ["premium", "active"]] },
                  10, // 10 point bonus for premium users
                  0
                ]
              }
            ]
          },
          _id: 0
        }
      },
      // Stage 9: Sort by rankingScore then totalPoints then name
      {
        $sort: {
          rankingScore: -1,
          totalPoints: -1,
          userName: 1
        }
      }
    ]);

    const updatedResults = await Promise.all(
      distinctPassReportsCountPerUser.map(async (row) => {
        // Calculate actual subscription status based on user data and subscription end date
        let actualSubscriptionStatus = "free";

        // Check if user has premium/active subscription and if it's still valid
        if (row.subscriptionStatus === "premium" || row.subscriptionStatus === "active") {
          if (row.subscriptionEndDate) {
            const endDate = new Date(row.subscriptionEndDate);
            const currentDate = new Date();

            if (endDate > currentDate) {
              actualSubscriptionStatus = "premium";
            } else {
              actualSubscriptionStatus = "expired";
            }
          } else {
            // No end date but status is premium/active - consider it premium
            actualSubscriptionStatus = "premium";
          }
        } else if (row.subscriptionStatus === "expired") {
          actualSubscriptionStatus = "expired";
        } else {
          // Default to free for any other status
          actualSubscriptionStatus = "free";
        }

        return {
          ...row,
          subscriptionStatus: actualSubscriptionStatus
        };
      })
    );

    res.send({
      message: "Reports for all users fetched successfully",
      data: updatedResults,
      success: true
    });


  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false
    });
  }
});

module.exports = router;