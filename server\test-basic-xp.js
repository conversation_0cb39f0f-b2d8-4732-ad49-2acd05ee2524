const mongoose = require('mongoose');
const User = require('./models/userModel');
const XPTransaction = require('./models/xpTransactionModel');
require('dotenv').config();

async function testBasicXP() {
  try {
    console.log('🔍 Testing Basic XP Award (Direct Database Update)...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');

    // Find a test user (non-admin)
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}\n`);

    // Store original values
    const originalXP = testUser.totalXP || 0;
    const originalLevel = testUser.currentLevel || 1;

    // Calculate new XP (simple calculation)
    const xpToAward = 50;
    const newTotalXP = originalXP + xpToAward;

    console.log(`💰 Awarding ${xpToAward} XP...`);

    // Update user directly
    const updateResult = await User.findByIdAndUpdate(
      testUser._id,
      {
        $inc: {
          totalXP: xpToAward,
          lifetimeXP: xpToAward,
          seasonXP: xpToAward
        },
        $set: {
          'xpStats.lastXPGain': new Date()
        }
      },
      { new: true, runValidators: false }
    );

    console.log('✅ User updated successfully');
    console.log(`📊 New XP: ${updateResult.totalXP}`);
    console.log(`🎯 Level: ${updateResult.currentLevel}\n`);

    // Create XP transaction record
    const transaction = new XPTransaction({
      user: testUser._id,
      xpAmount: xpToAward,
      transactionType: 'quiz_completion',
      breakdown: {
        baseXP: 30,
        bonusXP: 20
      },
      quizData: {
        subject: 'Test',
        difficulty: 'medium',
        questionsTotal: 4,
        questionsCorrect: 3,
        timeSpent: 300,
        score: 75,
        isFirstAttempt: true,
      },
      userStateAtTransaction: {
        levelBefore: originalLevel,
        xpBefore: originalXP,
        levelAfter: updateResult.currentLevel,
        xpAfter: updateResult.totalXP,
      },
      season: updateResult.currentSeason || '2024-S1',
      metadata: {
        testRun: true
      }
    });

    await transaction.save();
    console.log('✅ XP Transaction created successfully\n');

    // Verify the changes
    const verifyUser = await User.findById(testUser._id);
    console.log('🔍 Verification:');
    console.log(`   Original XP: ${originalXP}`);
    console.log(`   XP Awarded: ${xpToAward}`);
    console.log(`   New XP: ${verifyUser.totalXP}`);
    console.log(`   Expected XP: ${originalXP + xpToAward}`);
    console.log(`   ✅ XP Update: ${verifyUser.totalXP === originalXP + xpToAward ? 'SUCCESS' : 'FAILED'}\n`);

    // Check if transaction was saved
    const savedTransaction = await XPTransaction.findOne({
      user: testUser._id,
      transactionType: 'quiz_completion'
    }).sort({ createdAt: -1 });

    if (savedTransaction) {
      console.log('✅ XP Transaction verified:');
      console.log(`   XP Amount: ${savedTransaction.xpAmount}`);
      console.log(`   Created: ${savedTransaction.createdAt}`);
      console.log(`   Transaction ID: ${savedTransaction._id}\n`);
    } else {
      console.log('❌ XP Transaction not found\n');
    }

    console.log('🎉 Basic XP test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   User: ${testUser.name}`);
    console.log(`   XP Before: ${originalXP}`);
    console.log(`   XP After: ${verifyUser.totalXP}`);
    console.log(`   XP Gained: ${verifyUser.totalXP - originalXP}`);
    console.log(`   Transaction Created: ${!!savedTransaction}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the test
testBasicXP();
