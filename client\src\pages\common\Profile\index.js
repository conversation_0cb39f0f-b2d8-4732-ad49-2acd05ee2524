import React, { useEffect, useState } from "react";
import "./index.css";
import PageTitle from "../../../components/PageTitle";
import {
  getUserInfo,
  updateUserInfo,
  updateUserPhoto,
  sendOTP,
} from "../../../apicalls/users";
import { Form, message, Modal } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReportsForRanking } from "../../../apicalls/reports";

const Profile = () => {
  const [userDetails, setUserDetails] = useState(null);
  const [rankingData, setRankingData] = useState(null);
  const [userRanking, setUserRanking] = useState(null);
  const [edit, setEdit] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    school: "",
    level: "", // New field for level
    class_: "",
    phoneNumber: "",
  });
  const [profileImage, setProfileImage] = useState(null);
  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);
  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);
  const [pendingLevelChange, setPendingLevelChange] = useState(null);
  const dispatch = useDispatch();

  const fetchReports = async () => {
    try {
      const response = await getAllReportsForRanking();
      if (response.success) {
        setRankingData(response.data);
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      message.error(error.message);
    }
  };

  const getUserStats = () => {
    const Ranking = rankingData
      .map((user, index) => ({
        user,
        ranking: index + 1,
      }))
      .filter((item) => item.user.userId.includes(userDetails._id));
    setUserRanking(Ranking);
  };

  useEffect(() => {
    if (rankingData) {
      getUserStats();
    }
  }, [rankingData]);

  const getUserData = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getUserInfo();
      if (response.success) {
        setUserDetails(response.data);
        setFormData({
          ...formData,
          name: response.data.name,
          email: response.data.email,
          school: response.data.school,
          class_: response.data.class,
          level: response.data.level,
          phoneNumber: response.data.phoneNumber,
        });
        fetchReports();
        if (response.data.profileImage) {
          setProfileImage(response.data.profileImage);
        }
        dispatch(HideLoading());
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  useEffect(() => {
    if (localStorage.getItem("token")) {
      getUserData();
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "phoneNumber" && value.length > 10) return; // Limit to 10 digits

    // Check if level is changing
    if (name === "level" && value !== userDetails.level && value !== "") {
      setPendingLevelChange(value);
      setShowLevelChangeModal(true);
      return; // Don't update formData yet
    }

    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: value,
      ...(name === "level" ? { class_: "" } : {}),
    }));
  };

  const discardChanges = () => {
    setFormData({
      ...formData,
      name: userDetails.name,
      email: userDetails.email,
      school: userDetails.school,
      class_: userDetails.class,
      level: userDetails.level,
    });
    setEdit(false);
  };

  const sendOTPRequest = async (email) => {
    dispatch(ShowLoading());
    try {
      const response = await sendOTP({ email });
      if (response.success) {
        message.success("Please verify new email!");
        setEdit(false);
        setServerGeneratedOTP(response.data);
      } else {
        message.error(response.message);
        discardChanges();
      }
    } catch (error) {
      message.error(error.message);
      discardChanges();
    }
    dispatch(HideLoading());
  };

  const handleUpdate = async ({ skipOTP }) => {
    if (
      formData.name === userDetails.name &&
      formData.email === userDetails.email &&
      formData.school === userDetails.school &&
      formData.class_ === userDetails.class &&
      formData.phoneNumber === userDetails.phoneNumber &&
      formData.level === userDetails.level
    ) {
      return;
    }

    if (!formData.name) {
      message.error("Please enter your name.");
      return;
    }

    if (!formData.class_) {
      message.error("Please select a class before updating your profile.");
      return;
    }

    // Check if any other fields have been updated
    if (
      formData.name === userDetails.name &&
      formData.email === userDetails.email &&
      formData.school === userDetails.school &&
      formData.class_ === userDetails.class &&
      formData.phoneNumber === userDetails.phoneNumber &&
      formData.level === userDetails.level
    ) {
      message.info("No changes detected to update.");
      return;
    }

    if (!skipOTP && formData.email !== userDetails.email) {
      sendOTPRequest(formData.email);
      return;
    }
    dispatch(ShowLoading());

    try {
      const response = await updateUserInfo({
        ...formData,
        userId: userDetails._id
      });
      if (response.success) {
        if (response.levelChanged) {
          message.success(response.message);
          // Refresh the page to ensure all components reflect the new level
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          message.success("Info updated successfully!");
        }
        setEdit(false);
        setServerGeneratedOTP(null);
        getUserData();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    dispatch(HideLoading());
  };

  const handleLevelChangeConfirm = () => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      level: pendingLevelChange,
      class_: "", // Reset class when level changes
    }));
    setShowLevelChangeModal(false);
    setPendingLevelChange(null);
  };

  const handleLevelChangeCancel = () => {
    setShowLevelChangeModal(false);
    setPendingLevelChange(null);
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setProfileImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageUpload = async () => {
    const formData = new FormData();
    formData.append("profileImage", profileImage);
    dispatch(ShowLoading());
    try {
      const response = await updateUserPhoto(formData);
      if (response.success) {
        message.success("Photo updated successfully!");
        getUserData();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
  };

  const verifyUser = async (values) => {
    if (values.otp === serverGeneratedOTP) {
      handleUpdate({ skipOTP: true });
    } else {
      message.error("Invalid OTP");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="Profile max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Modern Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg">
            <i className="ri-user-line text-2xl text-white"></i>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            My <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Profile</span>
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Manage your account settings, track your progress, and personalize your learning experience.
          </p>
        </div>

        {serverGeneratedOTP ? (
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 max-w-md mx-auto">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="ri-shield-check-line text-2xl text-white"></i>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Email Verification</h2>
              <p className="text-gray-600">Please enter the verification code sent to your email</p>
            </div>

            <Form layout="vertical" className="modern-form" onFinish={verifyUser}>
              <Form.Item name="otp" label="Verification Code" initialValue="">
                <Input
                  type="number"
                  placeholder="Enter 6-digit code"
                  className="h-12 text-center text-lg font-mono"
                  maxLength={6}
                />
              </Form.Item>
              <Form.Item className="mb-0">
                <Button
                  type="primary"
                  htmlType="submit"
                  className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base"
                >
                  Verify Account
                </Button>
              </Form.Item>
            </Form>
          </div>
        ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Picture Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
              <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">Profile Picture</h3>

              <div className="flex flex-col items-center">
                <div
                  className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-blue-100 shadow-lg cursor-pointer group hover:border-blue-300 transition-all duration-300"
                  onClick={() => document.getElementById("profileImageInput").click()}
                >
                  {imagePreview ? (
                    <img src={imagePreview} alt="Profile Preview" className="w-full h-full object-cover" />
                  ) : profileImage ? (
                    <img src={profileImage} alt="Profile" className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <i className="ri-camera-line text-3xl text-gray-400"></i>
                    </div>
                  )}

                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-white text-center">
                      <i className="ri-camera-line text-2xl mb-1 block"></i>
                      <span className="text-sm font-medium">Change Photo</span>
                    </div>
                  </div>
                </div>

                <input
                  id="profileImageInput"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />

                {profileImage instanceof File && (
                  <button
                    onClick={handleImageUpload}
                    className="mt-4 px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300"
                  >
                    Save Photo
                  </button>
                )}
              </div>
            </div>

            {/* User Ranking Card */}
            {userRanking && !userDetails.isAdmin && (
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 mt-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">Your Ranking</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200">
                    <div className="flex items-center">
                      <i className="ri-trophy-line text-2xl text-yellow-600 mr-3"></i>
                      <span className="font-semibold text-gray-700">Position</span>
                    </div>
                    <span className="text-xl font-bold text-yellow-700">
                      {userRanking[0]?.ranking ? `#${userRanking[0].ranking}` : "Not Ranked"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                    <div className="flex items-center">
                      <i className="ri-star-line text-2xl text-blue-600 mr-3"></i>
                      <span className="font-semibold text-gray-700">Score</span>
                    </div>
                      {userRanking[0]?.user.score ? userRanking[0].user.score : "0"}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Profile Form Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-gray-900">Personal Information</h3>
                <button
                  onClick={() => setEdit(!edit)}
                  className={`px-6 py-2 rounded-lg font-semibold transition-all duration-300 ${
                    edit
                      ? 'bg-red-100 text-red-700 hover:bg-red-200'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  <i className={`${edit ? 'ri-close-line' : 'ri-edit-line'} mr-2`}></i>
                  {edit ? 'Cancel' : 'Edit Profile'}
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* User Name */}
                <div className="form-group">
                  <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className={`w-full px-4 py-3 border rounded-lg transition-all duration-300 ${
                      edit
                        ? 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    }`}
                    value={formData.name}
                    onChange={handleChange}
                    disabled={!edit}
                    placeholder="Enter your full name"
                  />
                </div>

                {/* School */}
                <div className="form-group">
                  <label htmlFor="school" className="block text-sm font-semibold text-gray-700 mb-2">
                    School
                  </label>
                  <input
                    type="text"
                    id="school"
                    name="school"
                    className={`w-full px-4 py-3 border rounded-lg transition-all duration-300 ${
                      edit
                        ? 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    }`}
                    value={formData.school || ""}
                    onChange={handleChange}
                    disabled={!edit}
                    placeholder="Enter your school name"
                  />
                </div>

                {/* Level */}
                <div className="form-group">
                  <label htmlFor="level" className="block text-sm font-semibold text-gray-700 mb-2">
                    Education Level
                  </label>
                  <select
                    id="level"
                    name="level"
                    className={`w-full px-4 py-3 border rounded-lg transition-all duration-300 ${
                      edit
                        ? 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    }`}
                    value={formData.level}
                    onChange={handleChange}
                    disabled={!edit}
                  >
                    <option value="">Select Level</option>
                    <option value="Primary">Primary</option>
                    <option value="Secondary">Secondary</option>
                    <option value="Advance">Advance</option>
                  </select>
                </div>

                {/* Class */}
                <div className="form-group">
                  <label htmlFor="class" className="block text-sm font-semibold text-gray-700 mb-2">
                    Class
                  </label>
                  <select
                    id="class"
                    name="class_"
                    className={`w-full px-4 py-3 border rounded-lg transition-all duration-300 ${
                      edit && formData.level
                        ? 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    }`}
                    value={formData.class_}
                    onChange={handleChange}
                    disabled={!edit || !formData.level}
                  >
                    <option value="">Select Class</option>

                    {formData.level === "Primary" && (
                      <>
                        <option value="1">Class 1</option>
                        <option value="2">Class 2</option>
                        <option value="3">Class 3</option>
                        <option value="4">Class 4</option>
                        <option value="5">Class 5</option>
                        <option value="6">Class 6</option>
                        <option value="7">Class 7</option>
                      </>
                    )}

              {formData.level === "Secondary" && (
                <>
                  <option value="Form-1">Form-1</option>
                  <option value="Form-2">Form-2</option>
                  <option value="Form-3">Form-3</option>
                  <option value="Form-4">Form-4</option>
                </>
              )}

              {formData.level === "Advance" && (
                <>
                  <option value="Form-5">Form-5</option>
                  <option value="Form-6">Form-6</option>
                </>
              )}
            </select>
          </div>
          <div className="input-container">
            <label htmlFor="email" className="label">
              Email Address
            </label>
            <br />
            <input
              type="text"
              id="email"
              name="email"
              className="input"
              value={formData.email}
              onChange={handleChange}
              disabled={!edit}
            />
          </div>
          <div className="input-container">
            <label htmlFor="email" className="label">
              Phone Number
            </label>
            <br />
            <input
              type="number"
              id="phoneNumber"
              name="phoneNumber"
              className="input"
              value={formData.phoneNumber}
              onChange={handleChange}
              disabled={!edit}
            />
          </div>
          {!edit ? (
            <div className="edit-btn-div">
              <button className="btn" onClick={(e) => setEdit(true)}>
                Edit
              </button>
            </div>
          ) : (
            <div className="btns-container">
              <button className="btn" onClick={discardChanges}>
                Cancel
              </button>
              <button className="btn" onClick={handleUpdate}>
                Update
              </button>
            </div>
          )}
        </>
      )}

      {/* Level Change Confirmation Modal */}
      <Modal
        title="Confirm Level Change"
        open={showLevelChangeModal}
        onOk={handleLevelChangeConfirm}
        onCancel={handleLevelChangeCancel}
        okText="Yes, Change Level"
        cancelText="Cancel"
        okButtonProps={{ danger: true }}
      >
        <div>
          <p><strong>Warning:</strong> You are about to change your level from <strong>{userDetails?.level}</strong> to <strong>{pendingLevelChange}</strong>.</p>
          <p>This will:</p>
          <ul>
            <li>Change your access to content for the new level only</li>
            <li>Reset your class selection</li>
            <li>You will no longer see content from your previous level</li>
            <li>This change will take effect immediately</li>
          </ul>
          <p>Are you sure you want to continue?</p>
        </div>
      </Modal>
    </div>
  );
};

export default Profile;
