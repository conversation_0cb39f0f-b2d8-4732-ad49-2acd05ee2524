import React, { useEffect, useState } from "react";
import "./index.css";
import PageTitle from "../../../components/PageTitle";
import {
  getUserInfo,
  updateUserInfo,
  updateUserPhoto,
  sendOTP,
} from "../../../apicalls/users";
import { Form, message, Modal, Input, Button } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReportsForRanking } from "../../../apicalls/reports";

const Profile = () => {
  const [userDetails, setUserDetails] = useState(null);
  const [rankingData, setRankingData] = useState(null);
  const [userRanking, setUserRanking] = useState(null);
  const [edit, setEdit] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    school: "",
    level: "",
    class_: "",
    phoneNumber: "",
  });
  const [profileImage, setProfileImage] = useState(null);
  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);
  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);
  const [pendingLevelChange, setPendingLevelChange] = useState(null);
  const dispatch = useDispatch();

  const fetchReports = async () => {
    try {
      const response = await getAllReportsForRanking();
      if (response.success) {
        setRankingData(response.data);
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      message.error(error.message);
      dispatch(HideLoading());
    }
  };

  const getUserStats = () => {
    const Ranking = rankingData
      .map((user, index) => ({
        user,
        ranking: index + 1,
      }))
      .filter((item) => item.user.userId.includes(userDetails._id));
    setUserRanking(Ranking);
  };

  useEffect(() => {
    if (rankingData && userDetails) {
      getUserStats();
    }
  }, [rankingData, userDetails]);

  const getUserData = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getUserInfo();
      if (response.success) {
        setUserDetails(response.data);
        setFormData({
          name: response.data.name || "",
          email: response.data.email || "",
          school: response.data.school || "",
          class_: response.data.class || "",
          level: response.data.level || "",
          phoneNumber: response.data.phoneNumber || "",
        });
        if (response.data.profileImage) {
          setProfileImage(response.data.profileImage);
        }
        fetchReports();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      dispatch(HideLoading());
    }
  };

  useEffect(() => {
    if (localStorage.getItem("token")) {
      getUserData();
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "phoneNumber" && value.length > 10) return;
    if (name === "level" && value !== userDetails?.level && value !== "") {
      setPendingLevelChange(value);
      setShowLevelChangeModal(true);
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: value,
      ...(name === "level" ? { class_: "" } : {}),
    }));
  };

  const discardChanges = () => {
    setFormData({
      name: userDetails.name,
      email: userDetails.email,
      school: userDetails.school,
      class_: userDetails.class,
      level: userDetails.level,
      phoneNumber: userDetails.phoneNumber,
    });
    setEdit(false);
  };

  const sendOTPRequest = async (email) => {
    dispatch(ShowLoading());
    try {
      const response = await sendOTP({ email });
      if (response.success) {
        message.success("Please verify new email!");
        setEdit(false);
        setServerGeneratedOTP(response.data);
      } else {
        message.error(response.message);
        discardChanges();
      }
    } catch (error) {
      message.error(error.message);
      discardChanges();
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleUpdate = async ({ skipOTP } = {}) => {
    if (!formData.name) return message.error("Please enter your name.");
    if (!formData.class_) return message.error("Please select a class.");

    if (
      !skipOTP &&
      formData.email !== userDetails.email
    ) {
      return sendOTPRequest(formData.email);
    }

    dispatch(ShowLoading());
    try {
      const response = await updateUserInfo({
        ...formData,
        userId: userDetails._id,
      });
      if (response.success) {
        message.success(response.message);
        setEdit(false);
        setServerGeneratedOTP(null);
        getUserData();
        if (response.levelChanged) {
          setTimeout(() => window.location.reload(), 2000);
        }
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleLevelChangeConfirm = () => {
    setFormData((prev) => ({
      ...prev,
      level: pendingLevelChange,
      class_: "",
    }));
    setShowLevelChangeModal(false);
    setPendingLevelChange(null);
  };

  const handleLevelChangeCancel = () => {
    setShowLevelChangeModal(false);
    setPendingLevelChange(null);
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setProfileImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleImageUpload = async () => {
    const data = new FormData();
    data.append("profileImage", profileImage);
    dispatch(ShowLoading());
    try {
      const response = await updateUserPhoto(data);
      if (response.success) {
        message.success("Photo updated successfully!");
        getUserData();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      dispatch(HideLoading());
    }
  };

  const verifyUser = async (values) => {
    if (values.otp === serverGeneratedOTP) {
      handleUpdate({ skipOTP: true });
    } else {
      message.error("Invalid OTP");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Render UI Sections */}
      {/* You may include your JSX from previous UI rendering here */}
    </div>
  );
};

export default Profile;
