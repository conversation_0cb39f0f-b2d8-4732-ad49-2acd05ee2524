{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Find Me functionality - scroll to current user's position\n  const findCurrentUser = () => {\n    if (!user) {\n      message.warning('Please log in to find your position');\n      return;\n    }\n    console.log('🔍 Find Me Debug - User Info:', {\n      userId: user._id,\n      userIdType: typeof user._id,\n      userName: user.name,\n      selectedLeague: selectedLeague,\n      leagueUsersCount: leagueUsers.length,\n      rankingDataCount: rankingData.length\n    });\n\n    // Find user in current view (either selected league or all users)\n    let targetUsers = selectedLeague ? leagueUsers : rankingData;\n    let userElement = null;\n\n    // Debug: Check if user exists in target data\n    const userInData = targetUsers.find(u => String(u._id) === String(user._id));\n    console.log('🔍 User in target data:', userInData);\n\n    // Try to find user element by data attributes\n    const userCards = document.querySelectorAll('[data-user-id]');\n    console.log('🔍 Found user cards:', userCards.length);\n\n    // Debug: Log all card IDs\n    const cardIds = Array.from(userCards).map(card => ({\n      id: card.dataset.userId,\n      type: typeof card.dataset.userId,\n      element: card.tagName\n    }));\n    console.log('🔍 All card IDs:', cardIds);\n\n    // Try multiple matching strategies\n    for (const card of userCards) {\n      const cardUserId = card.dataset.userId;\n\n      // Strategy 1: Exact match\n      if (cardUserId === user._id) {\n        userElement = card;\n        console.log('✅ Found user with exact match');\n        break;\n      }\n\n      // Strategy 2: String comparison\n      if (String(cardUserId) === String(user._id)) {\n        userElement = card;\n        console.log('✅ Found user with string match');\n        break;\n      }\n\n      // Strategy 3: Loose comparison (remove ObjectId wrapper if present)\n      const cleanCardId = String(cardUserId).replace(/ObjectId\\(\"(.+)\"\\)/, '$1');\n      const cleanUserId = String(user._id).replace(/ObjectId\\(\"(.+)\"\\)/, '$1');\n      if (cleanCardId === cleanUserId) {\n        userElement = card;\n        console.log('✅ Found user with clean match');\n        break;\n      }\n    }\n    if (userElement) {\n      // Smooth scroll to user with offset for header\n      const headerOffset = 120;\n      const elementPosition = userElement.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n\n      // Add pulsing highlight effect\n      userElement.style.transition = 'all 0.6s ease-in-out';\n      userElement.style.transform = 'scale(1.08)';\n      userElement.style.boxShadow = '0 0 40px rgba(255, 215, 0, 1), 0 0 80px rgba(255, 215, 0, 0.5)';\n      userElement.style.border = '4px solid #FFD700';\n      userElement.style.borderRadius = '16px';\n      userElement.style.zIndex = '1000';\n      userElement.style.position = 'relative';\n\n      // Add pulsing animation\n      let pulseCount = 0;\n      const pulseInterval = setInterval(() => {\n        if (pulseCount < 6) {\n          userElement.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n          userElement.style.boxShadow = pulseCount % 2 === 0 ? '0 0 40px rgba(255, 215, 0, 1), 0 0 80px rgba(255, 215, 0, 0.5)' : '0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.3)';\n          pulseCount++;\n        } else {\n          clearInterval(pulseInterval);\n          // Gradually remove highlight\n          setTimeout(() => {\n            userElement.style.transition = 'all 1s ease-out';\n            userElement.style.transform = '';\n            userElement.style.boxShadow = '';\n            userElement.style.border = '';\n            userElement.style.borderRadius = '';\n            userElement.style.zIndex = '';\n            userElement.style.position = '';\n          }, 500);\n        }\n      }, 400);\n      const userRank = userElement.dataset.userRank;\n      message.success(`Found your position! You are ranked #${userRank}`, 3);\n    }\n\n    // If still not found, try finding by name as fallback\n    if (!userElement && user.name) {\n      console.log('🔍 Trying to find user by name:', user.name);\n      for (const card of userCards) {\n        const cardElement = card.querySelector('[data-user-name], .user-name, h3, h4, h5');\n        if (cardElement && cardElement.textContent && cardElement.textContent.trim() === user.name) {\n          userElement = card;\n          console.log('✅ Found user by name match');\n          break;\n        }\n      }\n    }\n\n    // If still not found, try a more aggressive search\n    if (!userElement) {\n      console.log('🔍 Trying aggressive search...');\n      const allElements = document.querySelectorAll('*');\n      for (const element of allElements) {\n        if (element.dataset && element.dataset.userId) {\n          const elementUserId = element.dataset.userId;\n          if (String(elementUserId) === String(user._id) || elementUserId === user._id || String(elementUserId).includes(String(user._id)) || String(user._id).includes(String(elementUserId))) {\n            userElement = element;\n            console.log('✅ Found user with aggressive search');\n            break;\n          }\n        }\n      }\n    }\n    if (!userElement) {\n      console.log('❌ User element not found, debugging info:');\n      console.log('- User ID:', user._id);\n      console.log('- Available card IDs:', Array.from(userCards).map(c => c.dataset.userId));\n      console.log('- Selected league:', selectedLeague);\n      console.log('- League users:', leagueUsers.map(u => ({\n        id: u._id,\n        name: u.name\n      })));\n      console.log('- Ranking data:', rankingData.slice(0, 5).map(u => ({\n        id: u._id,\n        name: u.name\n      })));\n\n      // User not found in current view\n      if (selectedLeague) {\n        message.info('You are not in the currently selected league. Switching to your league...');\n\n        // Find user's league and switch to it\n        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n          const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n          if (userInLeague) {\n            setSelectedLeague(leagueKey);\n            setShowLeagueView(true);\n            setLeagueUsers(leagueData.users);\n\n            // Scroll after league switch\n            setTimeout(() => {\n              findCurrentUser();\n            }, 500);\n            return;\n          }\n        }\n        message.warning('Could not find your position in any league');\n      } else {\n        message.warning('Could not find your position in the ranking. You may need to take a quiz first to appear in rankings.');\n      }\n    }\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && {\n            _t: Date.now()\n          })\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            var _event$detail;\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if ((_event$detail = event.detail) !== null && _event$detail !== void 0 && _event$detail.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user._id)) return;\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1052,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1051,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1070,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1237,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1238,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1239,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1235,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1245,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1243,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1289,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1279,\n                  columnNumber: 17\n                }, this), userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1329,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1392,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1419,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1394,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1435,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1357,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1452,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1448,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1345,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1469,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1327,\n                  columnNumber: 17\n                }, this), user && /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: findCurrentUser,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1485,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Find Me\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1486,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1476,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1501,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1491,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1275,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1274,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1268,\n          columnNumber: 9\n        }, this), false && ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1524,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1525,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1523,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1517,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1544,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1545,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1565,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1584,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1585,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1564,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1552,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1610,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1633,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1627,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1695,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1700,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1711,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1683,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1643,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1549,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1548,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1543,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1536,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1736,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1741,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1731,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1763,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${isCurrentUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1816,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1815,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1830,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1833,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1855,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1861,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1845,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1844,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1876,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1883,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1888,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1891,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1887,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1827,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1820,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1777,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${isCurrentUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '320px',\n                    transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1943,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1942,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1952,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1947,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1969,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1972,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1995,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2001,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1985,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2021,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2014,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1984,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2028,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2039,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2027,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2068,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2054,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2073,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2084,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2085,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2083,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2089,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2082,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2093,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2094,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2092,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2098,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2091,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2081,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2072,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1963,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1956,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1902,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${isCurrentUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2148,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2147,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2162,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2165,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2187,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2193,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2177,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2176,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2208,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2215,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2220,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2223,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2219,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2159,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2152,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2109,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1774,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1757,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2255,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2269,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2272,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2254,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                        filter: isCurrentUser ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: isCurrentUser ? '4px solid #FFD700' : 'none',\n                        borderRadius: isCurrentUser ? '16px' : '0px',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2331,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2337,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2336,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full w-full h-full\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2362,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2368,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2352,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2389,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2382,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2351,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2334,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2400,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2411,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2399,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2429,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2397,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2396,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2438,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2458,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2459,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2451,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2468,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2469,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2461,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2450,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2436,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2324,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2318,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2290,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2284,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2283,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2247,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2494,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2508,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2493,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2534,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2545,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2555,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2544,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2533,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2560,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2532,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          \"data-user-id\": champion._id,\n                          \"data-user-rank\": leagueRank,\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2600,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2603,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                                  style: {\n                                    background: '#f0f0f0',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    width: '40px',\n                                    height: '40px'\n                                  },\n                                  children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: champion.profilePicture,\n                                    alt: champion.name,\n                                    className: \"object-cover rounded-full w-full h-full\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2630,\n                                    columnNumber: 47\n                                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                    style: {\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    },\n                                    children: champion.name.charAt(0).toUpperCase()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2636,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2620,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2656,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2649,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2615,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2668,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2662,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2672,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2677,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2680,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2676,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2597,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2591,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2577,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2571,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2694,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2693,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2521,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2514,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2485,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2719,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2726,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2729,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2725,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2732,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2735,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2731,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2738,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2741,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2737,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2724,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2744,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2718,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2712,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2760,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2765,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2770,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2759,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2793,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2789,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2795,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2800,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2808,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2788,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2782,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2826,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2827,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2832,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2821,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1753,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1747,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1266,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"UH7MdPhjf7aKdBvLF5Dzg6Z9jFc=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "reduxUser", "user", "localStorageUser", "userData", "localStorage", "getItem", "JSON", "parse", "tokenUser", "token", "payload", "atob", "split", "console", "log", "redux", "final", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "findCurrentUser", "warning", "userId", "userIdType", "userName", "name", "leagueUsersCount", "rankingDataCount", "targetUsers", "userElement", "userInData", "find", "String", "userCards", "querySelectorAll", "cardIds", "Array", "from", "map", "card", "id", "dataset", "type", "element", "tagName", "cardUserId", "cleanCardId", "replace", "cleanUserId", "headerOffset", "elementPosition", "getBoundingClientRect", "top", "offsetPosition", "window", "pageYOffset", "scrollTo", "border", "borderRadius", "zIndex", "position", "pulseCount", "pulseInterval", "setInterval", "clearInterval", "success", "cardElement", "textContent", "trim", "allElements", "elementUserId", "includes", "c", "slice", "info", "leagueData", "userInLeague", "fetchRankingData", "forceRefresh", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "_t", "Date", "now", "data", "filteredData", "totalQuizzesTaken", "transformedData", "index", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "reports", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "isAdmin", "role", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "exactMatch", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "randomQuote", "random", "animationTimer", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "removeItem", "refreshWithRetry", "attempts", "i", "_event$detail", "newTotalXP", "updatedUser", "Promise", "resolve", "addEventListener", "removeEventListener", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "scrollToUser", "podiumSection", "userInCurrentView", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "y", "x", "delay", "left", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_leagueGroups$leagueK2", "isSelected", "userCount", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "height", "width", "src", "alt", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Find Me functionality - scroll to current user's position\n  const findCurrentUser = () => {\n    if (!user) {\n      message.warning('Please log in to find your position');\n      return;\n    }\n\n    console.log('🔍 Find Me Debug - User Info:', {\n      userId: user._id,\n      userIdType: typeof user._id,\n      userName: user.name,\n      selectedLeague: selectedLeague,\n      leagueUsersCount: leagueUsers.length,\n      rankingDataCount: rankingData.length\n    });\n\n    // Find user in current view (either selected league or all users)\n    let targetUsers = selectedLeague ? leagueUsers : rankingData;\n    let userElement = null;\n\n    // Debug: Check if user exists in target data\n    const userInData = targetUsers.find(u => String(u._id) === String(user._id));\n    console.log('🔍 User in target data:', userInData);\n\n    // Try to find user element by data attributes\n    const userCards = document.querySelectorAll('[data-user-id]');\n    console.log('🔍 Found user cards:', userCards.length);\n\n    // Debug: Log all card IDs\n    const cardIds = Array.from(userCards).map(card => ({\n      id: card.dataset.userId,\n      type: typeof card.dataset.userId,\n      element: card.tagName\n    }));\n    console.log('🔍 All card IDs:', cardIds);\n\n    // Try multiple matching strategies\n    for (const card of userCards) {\n      const cardUserId = card.dataset.userId;\n\n      // Strategy 1: Exact match\n      if (cardUserId === user._id) {\n        userElement = card;\n        console.log('✅ Found user with exact match');\n        break;\n      }\n\n      // Strategy 2: String comparison\n      if (String(cardUserId) === String(user._id)) {\n        userElement = card;\n        console.log('✅ Found user with string match');\n        break;\n      }\n\n      // Strategy 3: Loose comparison (remove ObjectId wrapper if present)\n      const cleanCardId = String(cardUserId).replace(/ObjectId\\(\"(.+)\"\\)/, '$1');\n      const cleanUserId = String(user._id).replace(/ObjectId\\(\"(.+)\"\\)/, '$1');\n      if (cleanCardId === cleanUserId) {\n        userElement = card;\n        console.log('✅ Found user with clean match');\n        break;\n      }\n    }\n\n    if (userElement) {\n      // Smooth scroll to user with offset for header\n      const headerOffset = 120;\n      const elementPosition = userElement.getBoundingClientRect().top;\n      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;\n\n      window.scrollTo({\n        top: offsetPosition,\n        behavior: 'smooth'\n      });\n\n      // Add pulsing highlight effect\n      userElement.style.transition = 'all 0.6s ease-in-out';\n      userElement.style.transform = 'scale(1.08)';\n      userElement.style.boxShadow = '0 0 40px rgba(255, 215, 0, 1), 0 0 80px rgba(255, 215, 0, 0.5)';\n      userElement.style.border = '4px solid #FFD700';\n      userElement.style.borderRadius = '16px';\n      userElement.style.zIndex = '1000';\n      userElement.style.position = 'relative';\n\n      // Add pulsing animation\n      let pulseCount = 0;\n      const pulseInterval = setInterval(() => {\n        if (pulseCount < 6) {\n          userElement.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n          userElement.style.boxShadow = pulseCount % 2 === 0\n            ? '0 0 40px rgba(255, 215, 0, 1), 0 0 80px rgba(255, 215, 0, 0.5)'\n            : '0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.3)';\n          pulseCount++;\n        } else {\n          clearInterval(pulseInterval);\n          // Gradually remove highlight\n          setTimeout(() => {\n            userElement.style.transition = 'all 1s ease-out';\n            userElement.style.transform = '';\n            userElement.style.boxShadow = '';\n            userElement.style.border = '';\n            userElement.style.borderRadius = '';\n            userElement.style.zIndex = '';\n            userElement.style.position = '';\n          }, 500);\n        }\n      }, 400);\n\n      const userRank = userElement.dataset.userRank;\n      message.success(`Found your position! You are ranked #${userRank}`, 3);\n    }\n\n    // If still not found, try finding by name as fallback\n    if (!userElement && user.name) {\n      console.log('🔍 Trying to find user by name:', user.name);\n      for (const card of userCards) {\n        const cardElement = card.querySelector('[data-user-name], .user-name, h3, h4, h5');\n        if (cardElement && cardElement.textContent && cardElement.textContent.trim() === user.name) {\n          userElement = card;\n          console.log('✅ Found user by name match');\n          break;\n        }\n      }\n    }\n\n    // If still not found, try a more aggressive search\n    if (!userElement) {\n      console.log('🔍 Trying aggressive search...');\n      const allElements = document.querySelectorAll('*');\n      for (const element of allElements) {\n        if (element.dataset && element.dataset.userId) {\n          const elementUserId = element.dataset.userId;\n          if (String(elementUserId) === String(user._id) ||\n              elementUserId === user._id ||\n              String(elementUserId).includes(String(user._id)) ||\n              String(user._id).includes(String(elementUserId))) {\n            userElement = element;\n            console.log('✅ Found user with aggressive search');\n            break;\n          }\n        }\n      }\n    }\n\n    if (!userElement) {\n      console.log('❌ User element not found, debugging info:');\n      console.log('- User ID:', user._id);\n      console.log('- Available card IDs:', Array.from(userCards).map(c => c.dataset.userId));\n      console.log('- Selected league:', selectedLeague);\n      console.log('- League users:', leagueUsers.map(u => ({ id: u._id, name: u.name })));\n      console.log('- Ranking data:', rankingData.slice(0, 5).map(u => ({ id: u._id, name: u.name })));\n\n      // User not found in current view\n      if (selectedLeague) {\n        message.info('You are not in the currently selected league. Switching to your league...');\n\n        // Find user's league and switch to it\n        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n          const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n          if (userInLeague) {\n            setSelectedLeague(leagueKey);\n            setShowLeagueView(true);\n            setLeagueUsers(leagueData.users);\n\n            // Scroll after league switch\n            setTimeout(() => {\n              findCurrentUser();\n            }, 500);\n            return;\n          }\n        }\n        message.warning('Could not find your position in any league');\n      } else {\n        message.warning('Could not find your position in the ranking. You may need to take a quiz first to appear in rankings.');\n      }\n    }\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && { _t: Date.now() })\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id) // Filter out invalid users only (admins included for testing)\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if (event.detail?.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!user?._id) return;\n\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\">\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n\n\n\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n                {/* Find Me Button */}\n                {user && (\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={findCurrentUser}\n                    className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                    style={{\n                      fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                    }}\n                  >\n                    <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>Find Me</span>\n                  </motion.button>\n                )}\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice - DISABLED FOR TESTING */}\n        {false && (user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          isCurrentUser(topPerformers[1]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[1]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          isCurrentUser(topPerformers[0]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '320px',\n                          transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[0]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          isCurrentUser(topPerformers[2]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[2]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n\n\n\n\n\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                isCurrentUser\n                                  ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                  : ''\n                              }`}\n                              style={{\n                                transform: isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                                filter: isCurrentUser\n                                  ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                  : 'none',\n                                transition: 'all 0.3s ease',\n                                border: isCurrentUser ? '4px solid #FFD700' : 'none',\n                                borderRadius: isCurrentUser ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                        style={{\n                                          background: '#f0f0f0',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      >\n                                        {champion.profilePicture ? (\n                                          <img\n                                            src={champion.profilePicture}\n                                            alt={champion.name}\n                                            className=\"object-cover rounded-full w-full h-full\"\n                                          />\n                                        ) : (\n                                          <div\n                                            className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                            style={{\n                                              background: '#25D366',\n                                              color: '#FFFFFF',\n                                              fontSize: '12px'\n                                            }}\n                                          >\n                                            {champion.name.charAt(0).toUpperCase()}\n                                          </div>\n                                        )}\n                                      </div>\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    data-user-id={champion._id}\n                                    data-user-rank={leagueRank}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      isCurrentUser\n                                        ? 'ring-2 ring-yellow-400/60'\n                                        : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser\n                                            ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                            : ''\n                                        }`}>\n                                          <div\n                                            className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                            style={{\n                                              background: '#f0f0f0',\n                                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          >\n                                            {champion.profilePicture ? (\n                                              <img\n                                                src={champion.profilePicture}\n                                                alt={champion.name}\n                                                className=\"object-cover rounded-full w-full h-full\"\n                                              />\n                                            ) : (\n                                              <div\n                                                className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                                style={{\n                                                  background: '#25D366',\n                                                  color: '#FFFFFF',\n                                                  fontSize: '16px'\n                                                }}\n                                              >\n                                                {champion.name.charAt(0).toUpperCase()}\n                                              </div>\n                                            )}\n                                          </div>\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,SAAS,GAAGH,SAAS,CAACI,IAAI,IAAI,IAAI;;EAExC;EACA,MAAMC,gBAAgB,GAAG,CAAC,MAAM;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;EAEJ,MAAMK,SAAS,GAAG,CAAC,MAAM;IACvB,IAAI;MACF,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAII,KAAK,EAAE;QACT,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACI,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,OAAOF,OAAO;MAChB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMT,IAAI,GAAGD,SAAS,IAAIE,gBAAgB,IAAIM,SAAS;;EAEvD;EACAK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;IACnCC,KAAK,EAAEf,SAAS;IAChBI,YAAY,EAAEF,gBAAgB;IAC9BO,KAAK,EAAED,SAAS;IAChBQ,KAAK,EAAEf;EACT,CAAC,CAAC;EACF,MAAMgB,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMiF,UAAU,GAAG/E,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMgF,SAAS,GAAGhF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiF,cAAc,GAAGjF,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMkF,aAAa,GAAGlF,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMmF,WAAW,GAAGnF,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMoF,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEvF,OAAO;MACbwF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE7E,SAAS;MACf8E,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEvE,QAAQ;MACdwE,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAExE,OAAO;MACbyE,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExF,QAAQ;MACdyF,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhF,OAAO;MACbiF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEtF,MAAM;MACZuF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE9E,QAAQ;MACd+E,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIhF,KAAK,IAAK;IACpC,MAAMiF,OAAO,GAAG,CAAC,CAAC;IAElBjF,KAAK,CAACkF,OAAO,CAAChF,IAAI,IAAI;MACpB,MAAMiF,UAAU,GAAGT,aAAa,CAACxE,IAAI,CAACkF,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClBnF,KAAK,EAAE;QACT,CAAC;MACH;MACAiF,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAAC5E,KAAK,CAACqF,IAAI,CAAC;QACpC,GAAGnF,IAAI;QACPoF,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAACxF,KAAK,CAACyF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMjD,WAAW,GAAG0D,QAAQ,CAACE,MAAM,CAAC7F,IAAI,IAAI;MAC1C,MAAM0E,MAAM,GAAGF,aAAa,CAACxE,IAAI,CAACkF,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClBnF,KAAK,EAAEmC,WAAW;MAClB6D,QAAQ,EAAE7D,WAAW,CAAC8D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAEjE,WAAW,CAACkE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IAAA,IAAAe,qBAAA;IACxCzF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyE,SAAS,CAAC;;IAE7C;IACAhD,iBAAiB,CAACgD,SAAS,CAAC;IAC5BlD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAAmE,qBAAA,GAAA9D,YAAY,CAAC+C,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyBvG,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACAwG,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBnB,SAAU,IAAG,CAAC,IACvDkB,QAAQ,CAACE,cAAc,CAAE,UAASpB,SAAU,EAAC,CAAC,IAC9C7C,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC;MAElD,IAAIiB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACxB,MAAM,CAACnB,MAAM,IAAInC,YAAY,CAACmC,MAAM,CAAC,IAAInC,YAAY,CAACmC,MAAM,CAAC,CAAC5E,KAAK,CAACqG,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACtH,IAAI,EAAE;MACTjC,OAAO,CAACwJ,OAAO,CAAC,qCAAqC,CAAC;MACtD;IACF;IAEA3G,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3C2G,MAAM,EAAExH,IAAI,CAACiG,GAAG;MAChBwB,UAAU,EAAE,OAAOzH,IAAI,CAACiG,GAAG;MAC3ByB,QAAQ,EAAE1H,IAAI,CAAC2H,IAAI;MACnBtF,cAAc,EAAEA,cAAc;MAC9BuF,gBAAgB,EAAE3F,WAAW,CAACkE,MAAM;MACpC0B,gBAAgB,EAAE5G,WAAW,CAACkF;IAChC,CAAC,CAAC;;IAEF;IACA,IAAI2B,WAAW,GAAGzF,cAAc,GAAGJ,WAAW,GAAGhB,WAAW;IAC5D,IAAI8G,WAAW,GAAG,IAAI;;IAEtB;IACA,MAAMC,UAAU,GAAGF,WAAW,CAACG,IAAI,CAACjC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;IAC5ErF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmH,UAAU,CAAC;;IAElD;IACA,MAAMG,SAAS,GAAG3B,QAAQ,CAAC4B,gBAAgB,CAAC,gBAAgB,CAAC;IAC7DxH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsH,SAAS,CAAChC,MAAM,CAAC;;IAErD;IACA,MAAMkC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,GAAG,CAACC,IAAI,KAAK;MACjDC,EAAE,EAAED,IAAI,CAACE,OAAO,CAACnB,MAAM;MACvBoB,IAAI,EAAE,OAAOH,IAAI,CAACE,OAAO,CAACnB,MAAM;MAChCqB,OAAO,EAAEJ,IAAI,CAACK;IAChB,CAAC,CAAC,CAAC;IACHlI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwH,OAAO,CAAC;;IAExC;IACA,KAAK,MAAMI,IAAI,IAAIN,SAAS,EAAE;MAC5B,MAAMY,UAAU,GAAGN,IAAI,CAACE,OAAO,CAACnB,MAAM;;MAEtC;MACA,IAAIuB,UAAU,KAAK/I,IAAI,CAACiG,GAAG,EAAE;QAC3B8B,WAAW,GAAGU,IAAI;QAClB7H,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C;MACF;;MAEA;MACA,IAAIqH,MAAM,CAACa,UAAU,CAAC,KAAKb,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,EAAE;QAC3C8B,WAAW,GAAGU,IAAI;QAClB7H,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C;MACF;;MAEA;MACA,MAAMmI,WAAW,GAAGd,MAAM,CAACa,UAAU,CAAC,CAACE,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC;MAC1E,MAAMC,WAAW,GAAGhB,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAACgD,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC;MACxE,IAAID,WAAW,KAAKE,WAAW,EAAE;QAC/BnB,WAAW,GAAGU,IAAI;QAClB7H,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C;MACF;IACF;IAEA,IAAIkH,WAAW,EAAE;MACf;MACA,MAAMoB,YAAY,GAAG,GAAG;MACxB,MAAMC,eAAe,GAAGrB,WAAW,CAACsB,qBAAqB,CAAC,CAAC,CAACC,GAAG;MAC/D,MAAMC,cAAc,GAAGH,eAAe,GAAGI,MAAM,CAACC,WAAW,GAAGN,YAAY;MAE1EK,MAAM,CAACE,QAAQ,CAAC;QACdJ,GAAG,EAAEC,cAAc;QACnB1C,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACAkB,WAAW,CAACf,KAAK,CAACE,UAAU,GAAG,sBAAsB;MACrDa,WAAW,CAACf,KAAK,CAACC,SAAS,GAAG,aAAa;MAC3Cc,WAAW,CAACf,KAAK,CAACG,SAAS,GAAG,gEAAgE;MAC9FY,WAAW,CAACf,KAAK,CAAC2C,MAAM,GAAG,mBAAmB;MAC9C5B,WAAW,CAACf,KAAK,CAAC4C,YAAY,GAAG,MAAM;MACvC7B,WAAW,CAACf,KAAK,CAAC6C,MAAM,GAAG,MAAM;MACjC9B,WAAW,CAACf,KAAK,CAAC8C,QAAQ,GAAG,UAAU;;MAEvC;MACA,IAAIC,UAAU,GAAG,CAAC;MAClB,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;QACtC,IAAIF,UAAU,GAAG,CAAC,EAAE;UAClBhC,WAAW,CAACf,KAAK,CAACC,SAAS,GAAG8C,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,aAAa;UAClFhC,WAAW,CAACf,KAAK,CAACG,SAAS,GAAG4C,UAAU,GAAG,CAAC,KAAK,CAAC,GAC9C,gEAAgE,GAChE,kEAAkE;UACtEA,UAAU,EAAE;QACd,CAAC,MAAM;UACLG,aAAa,CAACF,aAAa,CAAC;UAC5B;UACA1D,UAAU,CAAC,MAAM;YACfyB,WAAW,CAACf,KAAK,CAACE,UAAU,GAAG,iBAAiB;YAChDa,WAAW,CAACf,KAAK,CAACC,SAAS,GAAG,EAAE;YAChCc,WAAW,CAACf,KAAK,CAACG,SAAS,GAAG,EAAE;YAChCY,WAAW,CAACf,KAAK,CAAC2C,MAAM,GAAG,EAAE;YAC7B5B,WAAW,CAACf,KAAK,CAAC4C,YAAY,GAAG,EAAE;YACnC7B,WAAW,CAACf,KAAK,CAAC6C,MAAM,GAAG,EAAE;YAC7B9B,WAAW,CAACf,KAAK,CAAC8C,QAAQ,GAAG,EAAE;UACjC,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,EAAE,GAAG,CAAC;MAEP,MAAMhE,QAAQ,GAAGiC,WAAW,CAACY,OAAO,CAAC7C,QAAQ;MAC7C/H,OAAO,CAACoM,OAAO,CAAE,wCAAuCrE,QAAS,EAAC,EAAE,CAAC,CAAC;IACxE;;IAEA;IACA,IAAI,CAACiC,WAAW,IAAI/H,IAAI,CAAC2H,IAAI,EAAE;MAC7B/G,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEb,IAAI,CAAC2H,IAAI,CAAC;MACzD,KAAK,MAAMc,IAAI,IAAIN,SAAS,EAAE;QAC5B,MAAMiC,WAAW,GAAG3B,IAAI,CAAChC,aAAa,CAAC,0CAA0C,CAAC;QAClF,IAAI2D,WAAW,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACC,IAAI,CAAC,CAAC,KAAKtK,IAAI,CAAC2H,IAAI,EAAE;UAC1FI,WAAW,GAAGU,IAAI;UAClB7H,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC;QACF;MACF;IACF;;IAEA;IACA,IAAI,CAACkH,WAAW,EAAE;MAChBnH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAM0J,WAAW,GAAG/D,QAAQ,CAAC4B,gBAAgB,CAAC,GAAG,CAAC;MAClD,KAAK,MAAMS,OAAO,IAAI0B,WAAW,EAAE;QACjC,IAAI1B,OAAO,CAACF,OAAO,IAAIE,OAAO,CAACF,OAAO,CAACnB,MAAM,EAAE;UAC7C,MAAMgD,aAAa,GAAG3B,OAAO,CAACF,OAAO,CAACnB,MAAM;UAC5C,IAAIU,MAAM,CAACsC,aAAa,CAAC,KAAKtC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,IAC1CuE,aAAa,KAAKxK,IAAI,CAACiG,GAAG,IAC1BiC,MAAM,CAACsC,aAAa,CAAC,CAACC,QAAQ,CAACvC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC,IAChDiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAACwE,QAAQ,CAACvC,MAAM,CAACsC,aAAa,CAAC,CAAC,EAAE;YACpDzC,WAAW,GAAGc,OAAO;YACrBjI,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;YAClD;UACF;QACF;MACF;IACF;IAEA,IAAI,CAACkH,WAAW,EAAE;MAChBnH,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEb,IAAI,CAACiG,GAAG,CAAC;MACnCrF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyH,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,GAAG,CAACkC,CAAC,IAAIA,CAAC,CAAC/B,OAAO,CAACnB,MAAM,CAAC,CAAC;MACtF5G,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwB,cAAc,CAAC;MACjDzB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoB,WAAW,CAACuG,GAAG,CAACxC,CAAC,KAAK;QAAE0C,EAAE,EAAE1C,CAAC,CAACC,GAAG;QAAE0B,IAAI,EAAE3B,CAAC,CAAC2B;MAAK,CAAC,CAAC,CAAC,CAAC;MACnF/G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,WAAW,CAAC0J,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnC,GAAG,CAACxC,CAAC,KAAK;QAAE0C,EAAE,EAAE1C,CAAC,CAACC,GAAG;QAAE0B,IAAI,EAAE3B,CAAC,CAAC2B;MAAK,CAAC,CAAC,CAAC,CAAC;;MAE/F;MACA,IAAItF,cAAc,EAAE;QAClBtE,OAAO,CAAC6M,IAAI,CAAC,2EAA2E,CAAC;;QAEzF;QACA,KAAK,MAAM,CAACtF,SAAS,EAAEuF,UAAU,CAAC,IAAIjG,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;UAClE,MAAMuI,YAAY,GAAGD,UAAU,CAAC/K,KAAK,CAACmI,IAAI,CAACjC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;UACnF,IAAI6E,YAAY,EAAE;YAChBxI,iBAAiB,CAACgD,SAAS,CAAC;YAC5BlD,iBAAiB,CAAC,IAAI,CAAC;YACvBF,cAAc,CAAC2I,UAAU,CAAC/K,KAAK,CAAC;;YAEhC;YACAwG,UAAU,CAAC,MAAM;cACfgB,eAAe,CAAC,CAAC;YACnB,CAAC,EAAE,GAAG,CAAC;YACP;UACF;QACF;QACAvJ,OAAO,CAACwJ,OAAO,CAAC,4CAA4C,CAAC;MAC/D,CAAC,MAAM;QACLxJ,OAAO,CAACwJ,OAAO,CAAC,uGAAuG,CAAC;MAC1H;IACF;EACF,CAAC;;EAED;EACA,MAAMwD,gBAAgB,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACvD,IAAI;MACF5J,UAAU,CAAC,IAAI,CAAC;MAChBR,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEmK,YAAY,GAAG,iBAAiB,GAAG,EAAE,CAAC;;MAE7F;MACA,IAAI;QACFpK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMoK,qBAAqB,GAAG,MAAM9L,gBAAgB,CAAC;UACnD+L,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAAnL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoL,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE,KAAK;UACtB;UACA,IAAIL,YAAY,IAAI;YAAEM,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;UAAE,CAAC;QACxC,CAAC,CAAC;QAEF5K,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoK,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACd,OAAO,IAAIc,qBAAqB,CAACQ,IAAI,EAAE;UACxF7K,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAM6K,YAAY,GAAGT,qBAAqB,CAACQ,IAAI,CAAC5F,MAAM,CAAC3F,QAAQ,IAC5DA,QAAQ,CAACgF,OAAO,IAAIhF,QAAQ,CAACgF,OAAO,GAAG,CAAC,IACxChF,QAAQ,CAACyL,iBAAiB,IAAIzL,QAAQ,CAACyL,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGF,YAAY,CAAClD,GAAG,CAAC,CAACtI,QAAQ,EAAE2L,KAAK,MAAM;YAC7D5F,GAAG,EAAE/F,QAAQ,CAAC+F,GAAG;YACjB0B,IAAI,EAAEzH,QAAQ,CAACyH,IAAI,IAAI,oBAAoB;YAC3CmE,KAAK,EAAE5L,QAAQ,CAAC4L,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE7L,QAAQ,CAAC6L,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAElL,QAAQ,CAACkL,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAE9L,QAAQ,CAAC+L,YAAY,IAAI,EAAE;YAC3C/G,OAAO,EAAEhF,QAAQ,CAACgF,OAAO,IAAI,CAAC;YAC9ByG,iBAAiB,EAAEzL,QAAQ,CAACyL,iBAAiB,IAAI,CAAC;YAClDO,YAAY,EAAEhM,QAAQ,CAACgM,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEjM,QAAQ,CAACiM,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAElM,QAAQ,CAACkM,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEnM,QAAQ,CAACmM,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAET,KAAK,GAAG,CAAC;YACfzG,IAAI,EAAEZ,aAAa,CAACtE,QAAQ,CAACgF,OAAO,IAAI,CAAC,CAAC;YAC1CqH,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEtM,QAAQ,CAACsM,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEvM,QAAQ,CAACuM,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAExM,QAAQ,CAACwM,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEzM,QAAQ,CAACyM,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAE1M,QAAQ,CAAC0M,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAE3M,QAAQ,CAAC2M,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEH5L,cAAc,CAAC0K,eAAe,CAAC;;UAE/B;UACA,MAAMmB,aAAa,GAAGnB,eAAe,CAAC7F,SAAS,CAACiH,IAAI,IAAIA,IAAI,CAAC/G,GAAG,MAAKjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG,EAAC;UAC/E3E,kBAAkB,CAACyL,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAI/M,IAAI,EAAE;YACR,MAAMiN,cAAc,GAAGvH,wBAAwB,CAACkG,eAAe,EAAE5L,IAAI,CAAC;YACtEgC,oBAAoB,CAACiL,cAAc,CAAC;YACpC/K,cAAc,CAAC,CAAA+K,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnN,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAMoN,OAAO,GAAGpI,kBAAkB,CAAC8G,eAAe,CAAC;UACnDpJ,eAAe,CAAC0K,OAAO,CAAC;UAExB9L,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO+L,OAAO,EAAE;QAChBvM,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsM,OAAO,CAAC;MACpE;;MAEA;MACAvM,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIuM,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFzM,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDuM,eAAe,GAAG,MAAMlO,uBAAuB,CAAC,CAAC;QACjD0B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCwM,aAAa,GAAG,MAAMhO,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOiO,KAAK,EAAE;QACd1M,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyM,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMhO,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOkO,SAAS,EAAE;UAClB3M,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0M,SAAS,CAAC;QACpD;MACF;MAEA,IAAI3B,eAAe,GAAG,EAAE;MAExB,IAAIyB,aAAa,IAAIA,aAAa,CAAClD,OAAO,IAAIkD,aAAa,CAAC5B,IAAI,EAAE;QAChE7K,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAM2M,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAACjD,OAAO,IAAIiD,eAAe,CAAC3B,IAAI,EAAE;UACtE2B,eAAe,CAAC3B,IAAI,CAACzG,OAAO,CAACgI,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMjG,MAAM,GAAG,EAAAiG,UAAA,GAAAT,IAAI,CAAChN,IAAI,cAAAyN,UAAA,uBAATA,UAAA,CAAWxH,GAAG,KAAI+G,IAAI,CAACxF,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVgG,cAAc,CAAChG,MAAM,CAAC,GAAGwF,IAAI,CAACU,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEA9B,eAAe,GAAGyB,aAAa,CAAC5B,IAAI,CACjC5F,MAAM,CAAC3F,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAC+F,GAAG,CAAC,CAAC;QAAA,CAC7CuC,GAAG,CAAC,CAACtI,QAAQ,EAAE2L,KAAK,KAAK;UACxB;UACA,MAAM8B,WAAW,GAAGH,cAAc,CAACtN,QAAQ,CAAC+F,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAI2H,YAAY,GAAGD,WAAW,CAACxH,MAAM,IAAIjG,QAAQ,CAACyL,iBAAiB,IAAI,CAAC;UACxE,IAAIkC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAI/B,YAAY,GAAG0B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAG1N,QAAQ,CAACgM,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAACyB,WAAW,CAACxH,MAAM,IAAIjG,QAAQ,CAACkO,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACrO,QAAQ,CAACkO,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAACjL,GAAG,CAAC,EAAE,EAAEiL,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIpO,QAAQ,CAACkO,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BnC,YAAY,GAAGgC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACjC,YAAY,GAAG0B,YAAY,CAAC;YAEpDhN,OAAO,CAACC,GAAG,CAAE,0BAAyBX,QAAQ,CAACyH,IAAK,KAAI0G,gBAAiB,aAAYG,gBAAiB,cAAatO,QAAQ,CAACkO,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAIlJ,OAAO,GAAGhF,QAAQ,CAACgF,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIhF,QAAQ,CAACkO,WAAW,EAAE;cACxB;cACAlJ,OAAO,GAAGgJ,IAAI,CAACK,KAAK,CAClBrO,QAAQ,CAACkO,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACA1I,OAAO,GAAGgJ,IAAI,CAACK,KAAK,CACjBrC,YAAY,GAAG0B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB1B,YAAY,GAAG,EAAE,GAAG0B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAIzB,aAAa,GAAGjM,QAAQ,CAACiM,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGlM,QAAQ,CAACkM,UAAU,IAAI,CAAC;UAEzC,IAAIuB,WAAW,CAACxH,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIsI,UAAU,GAAG,CAAC;YAClBd,WAAW,CAAC3I,OAAO,CAACgJ,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZrC,UAAU,GAAG8B,IAAI,CAACI,GAAG,CAAClC,UAAU,EAAEqC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFtC,aAAa,GAAGsC,UAAU;UAC5B,CAAC,MAAM,IAAIvO,QAAQ,CAACkO,WAAW,IAAI,CAACjC,aAAa,EAAE;YACjD;YACA,MAAMuC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAG1N,QAAQ,CAACkO,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBvC,aAAa,GAAG+B,IAAI,CAACjL,GAAG,CAAC2K,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxEtC,UAAU,GAAG8B,IAAI,CAACI,GAAG,CAACnC,aAAa,EAAE+B,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLzI,GAAG,EAAE/F,QAAQ,CAAC+F,GAAG;YACjB0B,IAAI,EAAEzH,QAAQ,CAACyH,IAAI,IAAI,oBAAoB;YAC3CmE,KAAK,EAAE5L,QAAQ,CAAC4L,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE7L,QAAQ,CAAC6L,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAElL,QAAQ,CAACkL,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAE9L,QAAQ,CAAC8L,cAAc,IAAI,EAAE;YAC7C9G,OAAO,EAAEA,OAAO;YAChByG,iBAAiB,EAAEiC,YAAY;YAC/B1B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEnM,QAAQ,CAACmM,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAET,KAAK,GAAG,CAAC;YACfzG,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5BqH,UAAU,EAAE,IAAI;YAChB;YACAoC,cAAc,EAAEzO,QAAQ,CAACkO,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAACxH,MAAM,GAAG,CAAC;YAClC2G,UAAU,EAAEa,WAAW,CAACxH,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGjG,QAAQ,CAACkO,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACAxC,eAAe,CAACrG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACA0G,eAAe,CAAC5G,OAAO,CAAC,CAAChF,IAAI,EAAE6L,KAAK,KAAK;UACvC7L,IAAI,CAACsM,IAAI,GAAGT,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEF3K,cAAc,CAAC0K,eAAe,CAAC;;QAE/B;QACA,IAAI9F,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI9F,IAAI,EAAE;UACR;UACA8F,QAAQ,GAAG8F,eAAe,CAAC7F,SAAS,CAACiH,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAKjG,IAAI,CAACiG,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAG8F,eAAe,CAAC7F,SAAS,CAACiH,IAAI,IAAI9E,MAAM,CAAC8E,IAAI,CAAC/G,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAI9F,IAAI,CAAC2H,IAAI,EAAE;YAChC7B,QAAQ,GAAG8F,eAAe,CAAC7F,SAAS,CAACiH,IAAI,IAAIA,IAAI,CAACrF,IAAI,KAAK3H,IAAI,CAAC2H,IAAI,CAAC;UACvE;QACF;QAEArG,kBAAkB,CAACwE,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAI9F,IAAI,EAAE;UACR,MAAMiN,cAAc,GAAGvH,wBAAwB,CAACkG,eAAe,EAAE5L,IAAI,CAAC;UACtEgC,oBAAoB,CAACiL,cAAc,CAAC;UACpC/K,cAAc,CAAC,CAAA+K,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnN,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAMoN,OAAO,GAAGpI,kBAAkB,CAAC8G,eAAe,CAAC;QACnDpJ,eAAe,CAAC0K,OAAO,CAAC;;QAExB;QACAtM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C+E,WAAW,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,IAAI;UACvBH,MAAM,EAAExH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG;UACjBwB,UAAU,EAAE,QAAOzH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG;UAC5B4I,OAAO,EAAE,CAAA7O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8O,IAAI,MAAK,OAAO,KAAI9O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6O,OAAO;UAChDE,MAAM,EAAE/O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,OAAO;UACrB6H,aAAa,EAAEjH,QAAQ;UACvBkJ,gBAAgB,EAAElJ,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrDmJ,gBAAgB,EAAErD,eAAe,CAACzF,MAAM;UACxC+I,eAAe,EAAEtD,eAAe,CAACjB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnC,GAAG,CAACxC,CAAC,KAAK;YAAE0C,EAAE,EAAE1C,CAAC,CAACC,GAAG;YAAE2C,IAAI,EAAE,OAAO5C,CAAC,CAACC,GAAG;YAAE0B,IAAI,EAAE3B,CAAC,CAAC2B;UAAK,CAAC,CAAC,CAAC;UACxGwH,UAAU,EAAEvD,eAAe,CAAC3D,IAAI,CAAC+E,IAAI,IAAIA,IAAI,CAAC/G,GAAG,MAAKjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG,EAAC;UAChEmJ,WAAW,EAAExD,eAAe,CAAC3D,IAAI,CAAC+E,IAAI,IAAI9E,MAAM,CAAC8E,IAAI,CAAC/G,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG,CAAC,CAAC;UACjFoJ,SAAS,EAAEzD,eAAe,CAAC3D,IAAI,CAAC+E,IAAI,IAAIA,IAAI,CAACrF,IAAI,MAAK3H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAM2H,WAAW,GAAG;UAClB5B,OAAO,EAAE9B,eAAe,CAAC/F,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,SAAS,CAAC,CAAC3G,MAAM;UACvEoJ,aAAa,EAAE3D,eAAe,CAAC/F,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,eAAe,CAAC,CAAC3G,MAAM;UACnFqJ,SAAS,EAAE5D,eAAe,CAAC/F,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,WAAW,CAAC,CAAC3G;QACvE,CAAC;QAEDvF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE+K,eAAe,CAACzF,MAAM,EAAE,gBAAgB,CAAC;QACxFvF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyO,WAAW,CAAC;QAC5C1O,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+K,eAAe,CAACjB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnC,GAAG,CAACxC,CAAC,KAAK;UACvE2B,IAAI,EAAE3B,CAAC,CAAC2B,IAAI;UACZlD,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACbuK,OAAO,EAAEzJ,CAAC,CAAC2F,iBAAiB;UAC5B+D,GAAG,EAAE1J,CAAC,CAACkG,YAAY;UACnByD,MAAM,EAAE3J,CAAC,CAAC8G;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLlM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCK,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvD,OAAO,CAACwJ,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO+F,KAAK,EAAE;MACd1M,OAAO,CAAC0M,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDvP,OAAO,CAACuP,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACRlM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3D,SAAS,CAAC,MAAM;IACdsN,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAM6E,WAAW,GAAG9M,kBAAkB,CAACoL,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG/M,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FrE,oBAAoB,CAAC8N,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAG7F,WAAW,CAAC,MAAM;MACvCrI,iBAAiB,CAACmO,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9BpP,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DkK,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMkF,mBAAmB,GAAIC,KAAK,IAAK;MACrCtP,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqP,KAAK,CAACC,MAAM,CAAC;;MAEnE;MACAhQ,YAAY,CAACiQ,UAAU,CAAC,cAAc,CAAC;MACvCjQ,YAAY,CAACiQ,UAAU,CAAC,qBAAqB,CAAC;MAC9CjQ,YAAY,CAACiQ,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,GAAG,CAAC,KAAK;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UACjC,IAAI;YAAA,IAAAC,aAAA;YACF5P,OAAO,CAACC,GAAG,CAAE,uCAAsC0P,CAAC,GAAG,CAAE,IAAGD,QAAS,GAAE,CAAC;YACxE,MAAMvF,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;YAE9B;YACA,IAAI,CAAAyF,aAAA,GAAAN,KAAK,CAACC,MAAM,cAAAK,aAAA,eAAZA,aAAA,CAAcC,UAAU,IAAIzQ,IAAI,EAAE;cACpC,MAAM0Q,WAAW,GAAGzP,WAAW,CAACgH,IAAI,CAACjC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;cAC7E,IAAIyK,WAAW,IAAIA,WAAW,CAACxL,OAAO,IAAIgL,KAAK,CAACC,MAAM,CAACM,UAAU,EAAE;gBACjE7P,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD;cACF;YACF;;YAEA;YACA,IAAI0P,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAItK,UAAU,CAACsK,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;YACd1M,OAAO,CAAC0M,KAAK,CAAE,6BAA4BiD,CAAC,GAAG,CAAE,UAAS,EAAEjD,KAAK,CAAC;YAClE,IAAIiD,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAItK,UAAU,CAACsK,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF;QACF;MACF,CAAC;;MAED;MACAtK,UAAU,CAAC,MAAM;QACf+J,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAED7G,MAAM,CAACqH,gBAAgB,CAAC,OAAO,EAAEb,iBAAiB,CAAC;IACnDxG,MAAM,CAACqH,gBAAgB,CAAC,eAAe,EAAEZ,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACX/F,aAAa,CAAC4F,cAAc,CAAC;MAC7B;MACAtG,MAAM,CAACsH,mBAAmB,CAAC,OAAO,EAAEd,iBAAiB,CAAC;MACtDxG,MAAM,CAACsH,mBAAmB,CAAC,eAAe,EAAEb,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxS,SAAS,CAAC,MAAM;IACd,IAAIuC,IAAI,IAAIuC,YAAY,IAAIqC,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,IAAI,CAAC9D,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAACiD,SAAS,EAAEuF,UAAU,CAAC,IAAIjG,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;QAClE,MAAMuI,YAAY,GAAGD,UAAU,CAAC/K,KAAK,CAACmI,IAAI,CAACjC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;QACnF,IAAI6E,YAAY,EAAE;UAChBlK,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEyE,SAAS,CAAC;UACxDhD,iBAAiB,CAACgD,SAAS,CAAC;UAC5BlD,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAAC2I,UAAU,CAAC/K,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACE,IAAI,EAAEuC,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAM0O,aAAa,GAAG9P,WAAW,CAAC0J,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMqG,eAAe,GAAG/P,WAAW,CAAC0J,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMsG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACjR,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiG,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAMiL,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIlJ,MAAM,CAACkJ,SAAS,CAACnL,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;IAC9F,IAAIiL,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAChL,SAAS,CAACqL,SAAS,IAAIlJ,MAAM,CAACkJ,SAAS,CAACnL,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACL2C,IAAI,EAAE,QAAQ;QACdkB,QAAQ,EAAEuH,cAAc;QACxB3M,MAAM,EAAE,iBAAiB;QACzBY,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEuF,UAAU,CAAC,IAAIjG,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;MAAA,IAAA+O,iBAAA;MAClE,MAAMxG,YAAY,IAAAwG,iBAAA,GAAGzG,UAAU,CAAC/K,KAAK,cAAAwR,iBAAA,uBAAhBA,iBAAA,CAAkBrJ,IAAI,CAACjC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;MACpF,IAAI6E,YAAY,EAAE;QAChB,MAAMhB,QAAQ,GAAGe,UAAU,CAAC/K,KAAK,CAACiG,SAAS,CAACC,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACL2C,IAAI,EAAE,QAAQ;UACdkB,QAAQ,EAAEA,QAAQ;UAClBpF,MAAM,EAAEmG,UAAU,CAACpH,KAAK;UACxB6B,SAAS,EAAEA,SAAS;UACpBiM,UAAU,EAAE1G,UAAU,CAAC/K,KAAK,CAACqG;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMqL,cAAc,GAAGP,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMQ,aAAa,GAAIjK,MAAM,IAAK;IAChC,OAAOxH,IAAI,IAAIkI,MAAM,CAACV,MAAM,CAAC,KAAKU,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC;EACpD,CAAC;;EAED;EACAxI,SAAS,CAAC,MAAM;IACd,IAAI,EAACuC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiG,GAAG,GAAE;IAEhB,MAAMyL,YAAY,GAAGA,CAAA,KAAM;MACzB;MACA,MAAMR,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIlJ,MAAM,CAACkJ,SAAS,CAACnL,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;MAE9F,IAAIiL,UAAU,EAAE;QACd;QACA,MAAMS,aAAa,GAAGnL,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvE,IAAIkL,aAAa,EAAE;UACjBrL,UAAU,CAAC,MAAM;YACfqL,aAAa,CAAC/K,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM,IAAI9E,WAAW,CAACkE,MAAM,GAAG,CAAC,EAAE;QACjC;QACA,MAAMyL,iBAAiB,GAAG3P,WAAW,CAACkP,IAAI,CAACnL,CAAC,IAAIkC,MAAM,CAAClC,CAAC,CAACC,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,CAAC;QACnF,IAAI2L,iBAAiB,EAAE;UACrB;UACA,MAAM7J,WAAW,GAAGvB,QAAQ,CAACC,aAAa,CAAE,kBAAiBzG,IAAI,CAACiG,GAAI,IAAG,CAAC;UAC1E,IAAI8B,WAAW,EAAE;YACfzB,UAAU,CAAC,MAAM;cACfyB,WAAW,CAACnB,cAAc,CAAC;gBACzBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE,QAAQ;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM8K,KAAK,GAAGvL,UAAU,CAACoL,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMI,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAAC7R,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,GAAG,EAAE8K,aAAa,EAAE9O,WAAW,CAAC,CAAC;;EAE3C;EACA,MAAM8P,oBAAoB,GAAGA,CAAC1F,kBAAkB,EAAE2F,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAM3G,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAM6G,OAAO,GAAGJ,mBAAmB,GAAG,IAAIzG,IAAI,CAACyG,mBAAmB,CAAC,GAAG,IAAI;IAE1EpR,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCwL,kBAAkB;MAClB2F,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfE,OAAO;MACP5G,GAAG;MACH6G,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAG5G,GAAG;MAClC2G;IACF,CAAC,CAAC;;IAEF;IACA,IAAI9F,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC+F,OAAO,IAAIA,OAAO,GAAG5G,GAAG,EAAE;QAC7B;QACA,OAAO;UACL8G,IAAI,EAAE,WAAW;UACjBpP,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL2O,IAAI,EAAE,SAAS;UACfpP,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL2O,IAAI,EAAE,SAAS;QACfpP,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAIxC,OAAO,IAAIF,WAAW,CAACkF,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE5G,OAAA;MAAKgT,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzB3L,UAAU,EAAE;YAAE4L,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF9T,OAAA;UAAGgT,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE9T,OAAA,CAAAE,SAAA;IAAA+S,QAAA,gBACEjT,OAAA;MAAAiT,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACV9T,OAAA;MAAKgT,SAAS,EAAC,kIAAkI;MAAAC,QAAA,gBAEjJjT,OAAA;QAAKgT,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CjT,OAAA;UAAKgT,SAAS,EAAC;QAA2H;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJ9T,OAAA;UAAKgT,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtK9T,OAAA;UAAKgT,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnK9T,OAAA;UAAKgT,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGN9T,OAAA;QAAKgT,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGlK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACE,GAAG,CAAC,CAAC8K,CAAC,EAAE/C,CAAC,kBACvBhR,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAEtF,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnC8C,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFzL,UAAU,EAAE;YACV4L,QAAQ,EAAE,CAAC,GAAG5E,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BkD,MAAM,EAAEC,QAAQ;YAChBS,KAAK,EAAEvF,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACF7I,KAAK,EAAE;YACL0M,IAAI,EAAG,GAAExF,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BvG,GAAG,EAAG,GAAE4E,IAAI,CAAC2B,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGU,CAAC;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9T,OAAA;QAAKgT,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCX,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BrM,UAAU,EAAE;YAAE4L,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtEjT,OAAA;YAAKgT,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCjT,OAAA;cAAKgT,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnIjT,OAAA;gBAAKgT,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGjT,OAAA,CAAC5B,MAAM,CAACgW,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM/S,QAAQ,CAAC,WAAW,CAAE;kBACrCuR,SAAS,EAAC,gNAAgN;kBAC1NvL,KAAK,EAAE;oBACLgN,QAAQ,EAAExK,MAAM,CAACyK,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAAzB,QAAA,gBAEFjT,OAAA,CAACjB,MAAM;oBAACiU,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5C9T,OAAA;oBAAAiT,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGf7B,cAAc,iBACbjS,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAG,CAAE;kBAC/BX,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAE,CAAE;kBAC9BhB,SAAS,EAAC,mJAAmJ;kBAC7JvL,KAAK,EAAE;oBACLkN,UAAU,EAAE1C,cAAc,CAAC5I,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/C1F,KAAK,EAAEsO,cAAc,CAAC5I,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/DzB,SAAS,EAAE,oCAAoC;oBAC/C6M,QAAQ,EAAExK,MAAM,CAACyK,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAAzB,QAAA,gBAEFjT,OAAA,CAACvB,QAAQ;oBAACuU,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C9T,OAAA;oBAAAiT,QAAA,EACGhB,cAAc,CAAC5I,IAAI,KAAK,QAAQ,GAC5B,cAAa4I,cAAc,CAAC1H,QAAS,EAAC,GACtC,GAAE0H,cAAc,CAAC9M,MAAO,KAAI8M,cAAc,CAAC1H,QAAS;kBAAC;oBAAAoJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,eAaD9T,OAAA;kBAAKgT,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpIjT,OAAA,CAAC5B,MAAM,CAACwW,EAAE;oBACR5B,SAAS,EAAC,sCAAsC;oBAChDvL,KAAK,EAAE;sBACLkN,UAAU,EAAE,mDAAmD;sBAC/DE,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCC,UAAU,EAAE,6BAA6B;sBACzCzO,MAAM,EAAE;oBACV,CAAE;oBACF+M,OAAO,EAAE;sBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjC3M,UAAU,EAAE;sBAAE4L,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAR,QAAA,EAC/C;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZ9T,OAAA;oBAAKgT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvEpL,iBAAiB,CAAC,CAAC,CAACoB,GAAG,CAAElD,SAAS,IAAK;sBAAA,IAAAiP,sBAAA;sBACtC,MAAM7P,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAMkP,UAAU,GAAGnS,cAAc,KAAKiD,SAAS;sBAC/C,MAAMmP,SAAS,GAAG,EAAAF,sBAAA,GAAAhS,YAAY,CAAC+C,SAAS,CAAC,cAAAiP,sBAAA,uBAAvBA,sBAAA,CAAyBzU,KAAK,CAACqG,MAAM,KAAI,CAAC;sBAE5D,oBACE5G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;wBAETF,SAAS,EAAC,kCAAkC;wBAC5CqB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAArB,QAAA,gBAE5BjT,OAAA,CAAC5B,MAAM,CAACgW,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAEN,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCO,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BE,OAAO,EAAEA,CAAA,KAAM3N,kBAAkB,CAACd,SAAS,CAAE;0BAC7CiN,SAAS,EAAG,+GACViC,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACHxN,KAAK,EAAE;4BACLkN,UAAU,EAAEM,UAAU,GACjB,2BAA0B9P,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7EuG,MAAM,EAAG,aAAY6K,UAAU,GAAG,SAAS,GAAG9P,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzEwD,SAAS,EAAEqN,UAAU,GAChB,YAAW9P,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxC2D,SAAS,EAAEuN,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjD3O,MAAM,EAAE2O,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACF5B,OAAO,EAAE4B,UAAU,GAAG;4BACpBrN,SAAS,EAAE,CACR,YAAWzC,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACDuQ,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACP3M,UAAU,EAAE;4BACV4L,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAEyB,UAAU,GAAGxB,QAAQ,GAAG,CAAC;4BACjCC,IAAI,EAAE;0BACR,CAAE;0BACFxP,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAWgR,SAAU,SAAS;0BAAAjC,QAAA,gBAEnEjT,OAAA;4BAAMgT,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE9N,MAAM,CAACb;0BAAU;4BAAAqP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChEmB,UAAU,iBACTjV,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;4BACTC,OAAO,EAAE;8BAAEmB,KAAK,EAAE,CAAC;8BAAEhB,MAAM,EAAE,CAAC,GAAG;8BAAEF,OAAO,EAAE;4BAAE,CAAE;4BAChDC,OAAO,EAAE;8BACPiB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClBhB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBF,OAAO,EAAE,CAAC;8BACVxL,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACV2M,KAAK,EAAE;gCAAEf,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BAC3DJ,MAAM,EAAE;gCAAEC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAS,CAAC;8BACzD9L,SAAS,EAAE;gCAAE2L,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BACjEN,OAAO,EAAE;gCAAEG,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFP,SAAS,EAAC,8KAA8K;4BACxLvL,KAAK,EAAE;8BACLkN,UAAU,EAAE,mDAAmD;8BAC/DvK,MAAM,EAAE,iBAAiB;8BACzBE,MAAM,EAAE;4BACV,CAAE;4BAAA2I,QAAA,eAEFjT,OAAA,CAAC5B,MAAM,CAAC+W,IAAI;8BACVnC,SAAS,EAAC,kCAAkC;8BAC5CK,OAAO,EAAE;gCACPiB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClBhB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACF3L,UAAU,EAAE;gCACV4L,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChBC,IAAI,EAAE;8BACR,CAAE;8BAAAT,QAAA,EACH;4BAED;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACD9T,OAAA;4BACEgT,SAAS,EAAC,2HAA2H;4BACrIvL,KAAK,EAAE;8BACLkN,UAAU,EAAExP,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChB8Q,QAAQ,EAAE;4BACZ,CAAE;4BAAAxB,QAAA,EAEDiC;0BAAS;4BAAAvB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhB9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;0BACTF,SAAS,EAAC,aAAa;0BACvBqB,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAArB,QAAA,eAE5BjT,OAAA;4BACEgT,SAAS,EAAC,mDAAmD;4BAC7DvL,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBiR,UAAU,EAAG,eAAc5P,MAAM,CAACpB,WAAY,EAAC;8BAC/C4Q,UAAU,EAAG,GAAExP,MAAM,CAACf,WAAY,IAAG;8BACrCgG,MAAM,EAAG,aAAYjF,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAA6O,QAAA,EAED9N,MAAM,CAACjB;0BAAK;4BAAAyP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GR/N,SAAS;wBAAA4N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEN9T,OAAA;oBAAGgT,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGLrT,IAAI,iBACHT,OAAA,CAAC5B,MAAM,CAACgW,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEzM,eAAgB;kBACzBiL,SAAS,EAAC,mMAAmM;kBAC7MvL,KAAK,EAAE;oBACLgN,QAAQ,EAAExK,MAAM,CAACyK,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAAzB,QAAA,gBAEFjT,OAAA,CAACnB,QAAQ;oBAACmU,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C9T,OAAA;oBAAAiT,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAChB,eAGD9T,OAAA,CAAC5B,MAAM,CAACgW,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEhB,MAAM,EAAE;kBAAI,CAAE;kBACzCiB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEhJ,gBAAiB;kBAC1B4J,QAAQ,EAAExT,OAAQ;kBAClBoR,SAAS,EAAC,qNAAqN;kBAC/NvL,KAAK,EAAE;oBACLgN,QAAQ,EAAExK,MAAM,CAACyK,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAAzB,QAAA,gBAEFjT,OAAA,CAAChB,SAAS;oBAACgU,SAAS,EAAG,yBAAwBpR,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAA+R,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClF9T,OAAA;oBAAAiT,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,KAAK,KAAK,CAAArT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8O,IAAI,MAAK,OAAO,KAAI9O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6O,OAAO,EAAC,iBACjDtP,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCX,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BrM,UAAU,EAAE;YAAE4L,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CjT,OAAA;YAAKgT,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCjT,OAAA;cAAKgT,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HjT,OAAA;gBAAKgT,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCjT,OAAA;kBAAKgT,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFjT,OAAA;oBAAMgT,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN9T,OAAA;kBAAAiT,QAAA,gBACEjT,OAAA;oBAAIgT,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpD9T,OAAA;oBAAGgT,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGD9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCX,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BrM,UAAU,EAAE;YAAE4L,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzCjT,OAAA;YAAKgT,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GjT,OAAA;cAAKgT,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnG9T,OAAA;cAAKgT,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrG9T,OAAA;cAAKgT,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFjT,OAAA;gBAAKgT,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBACTG,OAAO,EAAE;oBACPiB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBe,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACF1N,UAAU,EAAE;oBACV4L,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBjT,OAAA;oBAAIgT,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GjT,OAAA,CAAC5B,MAAM,CAAC+W,IAAI;sBACV9B,OAAO,EAAE;wBACPiC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACF3N,UAAU,EAAE;wBACV4L,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIvL,KAAK,EAAE;wBACL8N,cAAc,EAAE,WAAW;wBAC3BV,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCxO,MAAM,EAAE;sBACV,CAAE;sBAAA2M,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACd9T,OAAA;sBAAA2T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9T,OAAA,CAAC5B,MAAM,CAAC+W,IAAI;sBACV9B,OAAO,EAAE;wBACP0B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACFpN,UAAU,EAAE;wBACV4L,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFjM,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChB6R,UAAU,EAAE,KAAK;wBACjBT,UAAU,EAAE;sBACd,CAAE;sBAAA9B,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGb9T,OAAA,CAAC5B,MAAM,CAACqX,CAAC;kBACPtC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAG,CAAE;kBAC/BX,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAE,CAAE;kBAC9BrM,UAAU,EAAE;oBAAEuM,KAAK,EAAE,GAAG;oBAAEX,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHvL,KAAK,EAAE;oBACL9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCJ,UAAU,EAAE,0CAA0C;oBACtDE,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAA7B,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGX9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEkB,KAAK,EAAE;kBAAI,CAAE;kBACpCjB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEkB,KAAK,EAAE;kBAAE,CAAE;kBAClC3M,UAAU,EAAE;oBAAEuM,KAAK,EAAE,GAAG;oBAAEX,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBjT,OAAA;oBAAGgT,SAAS,EAAC,6JAA6J;oBACvKvL,KAAK,EAAE;sBACLsN,UAAU,EAAE,6BAA6B;sBACzCW,SAAS,EAAE;oBACb,CAAE;oBAAAzC,QAAA,EACF3Q;kBAAiB;oBAAAqR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGb9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAG,CAAE;kBAC/BX,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE;kBAAE,CAAE;kBAC9BrM,UAAU,EAAE;oBAAEuM,KAAK,EAAE,CAAC;oBAAEX,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACEhP,IAAI,EAAE1E,OAAO;oBACboW,KAAK,EAAEjU,WAAW,CAACkF,MAAM;oBACzBgP,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB1R,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAExF,QAAQ;oBACdkX,KAAK,EAAEnE,aAAa,CAAC5K,MAAM;oBAC3BgP,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpB1R,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAErF,OAAO;oBACb+W,KAAK,EAAEjU,WAAW,CAAC4E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACmG,aAAa,GAAG,CAAC,CAAC,CAAChG,MAAM;oBAC1DgP,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpB1R,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEtF,MAAM;oBACZgX,KAAK,EAAEjU,WAAW,CAAC6M,MAAM,CAAC,CAACC,GAAG,EAAE/H,CAAC,KAAK+H,GAAG,IAAI/H,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACoQ,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB1R,WAAW,EAAE;kBACf,CAAC,CACF,CAAC6E,GAAG,CAAC,CAAC+M,IAAI,EAAE1J,KAAK,kBAChBtM,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEkB,KAAK,EAAE;oBAAI,CAAE;oBACpCjB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEkB,KAAK,EAAE;oBAAE,CAAE;oBAClC3M,UAAU,EAAE;sBAAEuM,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,GAAG;sBAAEiH,QAAQ,EAAE;oBAAI,CAAE;oBACxDc,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnChB,SAAS,EAAG,qBAAoBgD,IAAI,CAACH,UAAW,8EAA8E;oBAC9HpO,KAAK,EAAE;sBACL2C,MAAM,EAAG,aAAY4L,IAAI,CAAC5R,WAAY,IAAG;sBACzCwD,SAAS,EAAG,cAAaoO,IAAI,CAAC5R,WAAY;oBAC5C,CAAE;oBAAA6O,QAAA,gBAEFjT,OAAA;sBAAKgT,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF9T,OAAA,CAACgW,IAAI,CAAC/R,IAAI;sBACR+O,SAAS,EAAC,kDAAkD;sBAC5DvL,KAAK,EAAE;wBAAE9D,KAAK,EAAEqS,IAAI,CAACF,SAAS;wBAAExP,MAAM,EAAE;sBAAyC;oBAAE;sBAAAqN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACF9T,OAAA;sBACEgT,SAAS,EAAC,0EAA0E;sBACpFvL,KAAK,EAAE;wBACL9D,KAAK,EAAEqS,IAAI,CAACF,SAAS;wBACrBf,UAAU,EAAG,6BAA4B;wBACzCzO,MAAM,EAAE,oCAAoC;wBAC5CmO,QAAQ,EAAE;sBACZ,CAAE;sBAAAxB,QAAA,EAED+C,IAAI,CAACL;oBAAK;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACN9T,OAAA;sBACEgT,SAAS,EAAC,4CAA4C;sBACtDvL,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChBoR,UAAU,EAAE,6BAA6B;wBACzCN,QAAQ,EAAE;sBACZ,CAAE;sBAAAxB,QAAA,EAED+C,IAAI,CAACJ;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDxH,KAAK;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZlS,OAAO,iBACN5B,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzB3L,UAAU,EAAE;cAAE4L,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACF9T,OAAA;YAAGgT,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAAClS,OAAO,iBACP5B,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAG,CAAE;UAC/BX,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BrM,UAAU,EAAE;YAAEuM,KAAK,EAAE,GAAG;YAAEX,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjEjT,OAAA;YAAKgT,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BzB,aAAa,CAAC5K,MAAM,GAAG,CAAC,iBACvB5G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cACpCjB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAE,CAAE;cAClC3M,UAAU,EAAE;gBAAEuM,KAAK,EAAE,GAAG;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBjT,OAAA;gBAAIgT,SAAS,EAAC,gGAAgG;gBAACvL,KAAK,EAAE;kBACpHkN,UAAU,EAAE,mDAAmD;kBAC/DE,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzCzO,MAAM,EAAE;gBACV,CAAE;gBAAA2M,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGL9T,OAAA;gBAAKgT,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/GzB,aAAa,CAAC,CAAC,CAAC,iBACfxR,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBAET+C,GAAG,EAAExV,IAAI,IAAIkI,MAAM,CAAC6I,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmO,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI;kBACnC,kBAAgB,CAAE;kBAClByM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCX,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVa,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBe,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACF1N,UAAU,EAAE;oBACVuM,KAAK,EAAE,GAAG;oBACVX,QAAQ,EAAE,GAAG;oBACbe,KAAK,EAAE;sBAAEf,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3D2B,OAAO,EAAE;sBAAE9B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFW,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpChB,SAAS,EAAG,oBACVd,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLyO,MAAM,EAAE,OAAO;oBACfxO,SAAS,EAAEwK,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE4L,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3ByC,MAAM,EAAE8H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1E2D,YAAY,EAAE6H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEiO,UAAU,EAAEzC,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAuM,QAAA,gBAGFjT,OAAA;oBAAKgT,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnKjT,OAAA;sBAAMgT,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGN9T,OAAA;oBACEgT,SAAS,EAAG,8BAA6BxB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAClC,KAAM,mBAAkB6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4J,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,IAAG;sBAC9DoS,KAAK,EAAE;oBACT,CAAE;oBAAAlD,QAAA,eAEFjT,OAAA;sBACEgT,SAAS,EAAG,GAAExB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAqP,QAAA,gBAEnHjT,OAAA;wBAAKgT,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF9T,OAAA;wBACEgT,SAAS,EAAC,oMAAoM;wBAC9MvL,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChByG,MAAM,EAAE;wBACV,CAAE;wBAAA6I,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN9T,OAAA;wBAAKgT,SAAS,EAAG,yBAAwBvS,IAAI,IAAI+Q,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,KAAKjG,IAAI,CAACiG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuM,QAAA,eACnIjT,OAAA;0BACEgT,SAAS,EAAC,wEAAwE;0BAClFvL,KAAK,EAAE;4BACLkN,UAAU,EAAE,SAAS;4BACrB/M,SAAS,EAAE,4BAA4B;4BACvCuO,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAjD,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAc,gBAC9BzM,OAAA;4BACEoW,GAAG,EAAE5E,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAe;4BACrC4J,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAK;4BAC3B4K,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF9T,OAAA;4BACEgT,SAAS,EAAC,2EAA2E;4BACrFvL,KAAK,EAAE;8BACLkN,UAAU,EAAE,SAAS;8BACrBhR,KAAK,EAAE,SAAS;8BAChB8Q,QAAQ,EAAE;4BACZ,CAAE;4BAAAxB,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAI,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN9T,OAAA;wBACEgT,SAAS,EAAC,iCAAiC;wBAC3CvL,KAAK,EAAE;0BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC/B;wBAAU,CAAE;wBAAAmP,QAAA,EAEjDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ;sBAAI;wBAAAuL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL9T,OAAA;wBAAKgT,SAAS,EAAC,yBAAyB;wBAACvL,KAAK,EAAE;0BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;wBAAU,CAAE;wBAAAoP,QAAA,GACxFzB,aAAa,CAAC,CAAC,CAAC,CAAC7L,OAAO,CAACoQ,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN9T,OAAA;wBAAKgT,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDjT,OAAA;0BAAMyH,KAAK,EAAE;4BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;0BAAU,CAAE;0BAAAoP,QAAA,GAAC,eACpD,EAACzB,aAAa,CAAC,CAAC,CAAC,CAACpF,iBAAiB;wBAAA;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP9T,OAAA;0BAAMyH,KAAK,EAAE;4BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;0BAAU,CAAE;0BAAAoP,QAAA,GAAC,eACpD,EAACzB,aAAa,CAAC,CAAC,CAAC,CAAC5E,aAAa;wBAAA;0BAAA+G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtHA,UAAStC,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI,EAAC;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuH3B,CACb,EAGAtC,aAAa,CAAC,CAAC,CAAC,iBACfxR,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBAET+C,GAAG,EAAExV,IAAI,IAAIkI,MAAM,CAAC6I,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmO,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI;kBACnC,kBAAgB,CAAE;kBAClByM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEY,CAAC,EAAE,CAAC,GAAG;oBAAEM,KAAK,EAAE;kBAAI,CAAE;kBAC7CjB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVY,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC;oBACRe,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxBrB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFrM,UAAU,EAAE;oBACVuM,KAAK,EAAE,GAAG;oBACVX,QAAQ,EAAE,GAAG;oBACb8B,OAAO,EAAE;sBAAE9B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DM,CAAC,EAAE;sBAAET,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFW,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpChB,SAAS,EAAG,yBACVd,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLyO,MAAM,EAAE,OAAO;oBACfxO,SAAS,EAAEwK,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE4L,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3ByC,MAAM,EAAE8H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1E2D,YAAY,EAAE6H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEiO,UAAU,EAAEzC,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAAuM,QAAA,gBAIrBjT,OAAA;oBAAKgT,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzKjT,OAAA;sBAAMgT,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGN9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDrM,UAAU,EAAE;sBAAE4L,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErEjT,OAAA,CAACtB,OAAO;sBAACsU,SAAS,EAAC;oBAA0C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGb9T,OAAA;oBACEgT,SAAS,EAAG,8BAA6BxB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAClC,KAAM,sBAAqB6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5JyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4J,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/FoS,KAAK,EAAE;oBACT,CAAE;oBAAAlD,QAAA,eAEFjT,OAAA;sBACEgT,SAAS,EAAG,GAAExB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH6D,KAAK,EAAE;wBACLkN,UAAU,EAAG,GAAEnD,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAqP,QAAA,gBAEFjT,OAAA;wBAAKgT,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlG9T,OAAA;wBACEgT,SAAS,EAAC,wMAAwM;wBAClNvL,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChBoR,UAAU,EAAE,6BAA6B;0BACzC3K,MAAM,EAAE;wBACV,CAAE;wBAAA6I,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN9T,OAAA;wBAAKgT,SAAS,EAAG,yBAAwBvS,IAAI,IAAI+Q,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,KAAKjG,IAAI,CAACiG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuM,QAAA,gBACnIjT,OAAA;0BACEgT,SAAS,EAAC,wEAAwE;0BAClFvL,KAAK,EAAE;4BACLkN,UAAU,EAAE,SAAS;4BACrB/M,SAAS,EAAE,4BAA4B;4BACvCuO,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAjD,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAc,gBAC9BzM,OAAA;4BACEoW,GAAG,EAAE5E,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAe;4BACrC4J,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAK;4BAC3B4K,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF9T,OAAA;4BACEgT,SAAS,EAAC,2EAA2E;4BACrFvL,KAAK,EAAE;8BACLkN,UAAU,EAAE,SAAS;8BACrBhR,KAAK,EAAE,SAAS;8BAChB8Q,QAAQ,EAAE;4BACZ,CAAE;4BAAAxB,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAI,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLrT,IAAI,IAAI+Q,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,KAAKjG,IAAI,CAACiG,GAAG,iBACxC1G,OAAA;0BACEgT,SAAS,EAAC,4DAA4D;0BACtEvL,KAAK,EAAE;4BACLkN,UAAU,EAAE,0CAA0C;4BACtD/M,SAAS,EAAE;0BACb,CAAE;0BAAAqL,QAAA,eAEFjT,OAAA,CAACrB,MAAM;4BAACqU,SAAS,EAAC;0BAAuB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN9T,OAAA;wBAAKgT,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1DjT,OAAA;0BACEgT,SAAS,EAAC,6BAA6B;0BACvCvL,KAAK,EAAE;4BACL9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC/B,SAAS;4BACtCiR,UAAU,EAAG,eAAcvD,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA2M,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ;wBAAI;0BAAAuL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJ5B,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,iBAClC1G,OAAA;0BACEgT,SAAS,EAAC,yDAAyD;0BACnEvL,KAAK,EAAE;4BACLkN,UAAU,EAAE,0CAA0C;4BACtDhR,KAAK,EAAE,SAAS;4BAChBiE,SAAS,EAAE,+BAA+B;4BAC1CwC,MAAM,EAAE,mBAAmB;4BAC3BqK,QAAQ,EAAE;0BACZ,CAAE;0BAAAxB,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAEN9T,OAAA;wBACEgT,SAAS,EAAG,6DAA4DxB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ8D,KAAK,EAAE;0BACLkN,UAAU,EAAG,2BAA0BnD,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAACzB,WAAY,KAAIoN,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChBoR,UAAU,EAAE,6BAA6B;0BACzCnN,SAAS,EAAG,cAAa4J,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,IAAG;0BAC9DqG,MAAM,EAAE;wBACV,CAAE;wBAAA6I,QAAA,GAEDzB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC5B,IAAI,iBAAIjG,KAAK,CAACwY,aAAa,CAAChF,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC5B,IAAI,EAAE;0BAC7E+O,SAAS,EAAE,SAAS;0BACpBvL,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACF3D,OAAA;0BAAMyH,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU,CAAE;0BAAAsP,QAAA,EAAEzB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC3B;wBAAK;0BAAAyP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGN9T,OAAA;wBAAKgT,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCjT,OAAA;0BAAKgT,SAAS,EAAC,oBAAoB;0BAACvL,KAAK,EAAE;4BACzC9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC/B,SAAS;4BACtCiR,UAAU,EAAG,eAAcvD,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA2M,QAAA,GACCzB,aAAa,CAAC,CAAC,CAAC,CAAC7L,OAAO,CAACoQ,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN9T,OAAA;0BAAKgT,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDjT,OAAA;4BAAKgT,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BjT,OAAA;8BAAKgT,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDjT,OAAA,CAAClB,OAAO;gCAACkU,SAAS,EAAC,SAAS;gCAACvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;gCAAU;8BAAE;gCAAA8P,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClF9T,OAAA;gCAAMgT,SAAS,EAAC,WAAW;gCAACvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;gCAAU,CAAE;gCAAAoP,QAAA,EAC3EzB,aAAa,CAAC,CAAC,CAAC,CAACpF;8BAAiB;gCAAAuH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN9T,OAAA;8BAAKgT,SAAS,EAAC,oBAAoB;8BAACvL,KAAK,EAAE;gCAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;8BAAU,CAAE;8BAAAoP,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACN9T,OAAA;4BAAKgT,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BjT,OAAA;8BAAKgT,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDjT,OAAA,CAACpB,OAAO;gCAACoU,SAAS,EAAC,SAAS;gCAACvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE;gCAAU;8BAAE;gCAAAgQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5D9T,OAAA;gCAAMgT,SAAS,EAAC,WAAW;gCAACvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;gCAAU,CAAE;gCAAAoP,QAAA,EAC3EzB,aAAa,CAAC,CAAC,CAAC,CAAC5E;8BAAa;gCAAA+G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN9T,OAAA;8BAAKgT,SAAS,EAAC,oBAAoB;8BAACvL,KAAK,EAAE;gCAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;8BAAU,CAAE;8BAAAoP,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxMA,SAAQtC,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI,EAAC;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyM1B,CACb,EAGAtC,aAAa,CAAC,CAAC,CAAC,iBACfxR,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBAET+C,GAAG,EAAExV,IAAI,IAAIkI,MAAM,CAAC6I,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcmO,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI;kBACnC,kBAAgB,CAAE;kBAClByM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEa,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCX,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVa,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBe,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACF1N,UAAU,EAAE;oBACVuM,KAAK,EAAE,GAAG;oBACVX,QAAQ,EAAE,GAAG;oBACbe,KAAK,EAAE;sBAAEf,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3D2B,OAAO,EAAE;sBAAE9B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFW,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpChB,SAAS,EAAG,oBACVd,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAC/B,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACLyO,MAAM,EAAE,OAAO;oBACfxO,SAAS,EAAEwK,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE4L,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GACvC,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3ByC,MAAM,EAAE8H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1E2D,YAAY,EAAE6H,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEiO,UAAU,EAAEzC,aAAa,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAuM,QAAA,gBAGFjT,OAAA;oBAAKgT,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtKjT,OAAA;sBAAMgT,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGN9T,OAAA;oBACEgT,SAAS,EAAG,8BAA6BxB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAClC,KAAM,mBAAkB6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAa4J,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC9B,WAAY,IAAG;sBAC9DoS,KAAK,EAAE;oBACT,CAAE;oBAAAlD,QAAA,eAEFjT,OAAA;sBACEgT,SAAS,EAAG,GAAExB,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAqP,QAAA,gBAEnHjT,OAAA;wBAAKgT,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF9T,OAAA;wBACEgT,SAAS,EAAC,sMAAsM;wBAChNvL,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChByG,MAAM,EAAE;wBACV,CAAE;wBAAA6I,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN9T,OAAA;wBAAKgT,SAAS,EAAG,yBAAwBvS,IAAI,IAAI+Q,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAG,KAAKjG,IAAI,CAACiG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuM,QAAA,eACnIjT,OAAA;0BACEgT,SAAS,EAAC,wEAAwE;0BAClFvL,KAAK,EAAE;4BACLkN,UAAU,EAAE,SAAS;4BACrB/M,SAAS,EAAE,4BAA4B;4BACvCuO,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAjD,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAc,gBAC9BzM,OAAA;4BACEoW,GAAG,EAAE5E,aAAa,CAAC,CAAC,CAAC,CAAC/E,cAAe;4BACrC4J,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAK;4BAC3B4K,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF9T,OAAA;4BACEgT,SAAS,EAAC,2EAA2E;4BACrFvL,KAAK,EAAE;8BACLkN,UAAU,EAAE,SAAS;8BACrBhR,KAAK,EAAE,SAAS;8BAChB8Q,QAAQ,EAAE;4BACZ,CAAE;4BAAAxB,QAAA,EAEDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ,IAAI,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN9T,OAAA;wBACEgT,SAAS,EAAC,iCAAiC;wBAC3CvL,KAAK,EAAE;0BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAC/B;wBAAU,CAAE;wBAAAmP,QAAA,EAEjDzB,aAAa,CAAC,CAAC,CAAC,CAACpJ;sBAAI;wBAAAuL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL9T,OAAA;wBAAKgT,SAAS,EAAC,yBAAyB;wBAACvL,KAAK,EAAE;0BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;wBAAU,CAAE;wBAAAoP,QAAA,GACxFzB,aAAa,CAAC,CAAC,CAAC,CAAC7L,OAAO,CAACoQ,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN9T,OAAA;wBAAKgT,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDjT,OAAA;0BAAMyH,KAAK,EAAE;4BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;0BAAU,CAAE;0BAAAoP,QAAA,GAAC,eACpD,EAACzB,aAAa,CAAC,CAAC,CAAC,CAACpF,iBAAiB;wBAAA;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP9T,OAAA;0BAAMyH,KAAK,EAAE;4BAAE9D,KAAK,EAAE6N,aAAa,CAAC,CAAC,CAAC,CAAC3L,IAAI,CAAChC;0BAAU,CAAE;0BAAAoP,QAAA,GAAC,eACpD,EAACzB,aAAa,CAAC,CAAC,CAAC,CAAC5E,aAAa;wBAAA;0BAAA+G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtHA,SAAQtC,aAAa,CAAC,CAAC,CAAC,CAAC9K,GAAI,EAAC;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuH1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASI,CACb,EAGAhR,cAAc,GACb;YACAJ,WAAW,CAACkE,MAAM,GAAG,CAAC,iBACpB5G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAG,CAAE;cAC/BX,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAE,CAAE;cAC9BrM,UAAU,EAAE;gBAAEuM,KAAK,EAAE,CAAC;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtCjT,OAAA;gBAAKgT,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCjT,OAAA,CAAC5B,MAAM,CAACqY,EAAE;kBACRzD,SAAS,EAAC,kDAAkD;kBAC5DvL,KAAK,EAAE;oBACLkN,UAAU,EAAG,0BAAyBnR,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY,KAAIZ,YAAY,CAACV,cAAc,CAAC,CAACe,SAAU,GAAE;oBAC5HgR,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzCzO,MAAM,EAAG,wBAAuB9C,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY;kBAC3E,CAAE;kBACFiP,OAAO,EAAE;oBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC3M,UAAU,EAAE;oBAAE4L,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,GAE7CzP,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU,EAAC,GAAC,EAACd,YAAY,CAACV,cAAc,CAAC,CAACoB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU;gBAAA;kBAAAqP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZ9T,OAAA;kBAAGgT,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1DvQ,WAAW,CAACkE,MAAM,EAAC,2BACtB;gBAAA;kBAAA+M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ9T,OAAA,CAAC5B,MAAM,CAACgW,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMzR,iBAAiB,CAAC,IAAI,CAAE;kBACvCiQ,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGN9T,OAAA;gBAAKgT,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCjT,OAAA;kBAAKgT,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjCvQ,WAAW,CAACuG,GAAG,CAAC,CAACyN,QAAQ,EAAEpK,KAAK,KAAK;oBACpC,MAAMqK,UAAU,GAAGrK,KAAK,GAAG,CAAC;oBAC5B,MAAM4F,aAAa,GAAGzR,IAAI,IAAIkI,MAAM,CAAC+N,QAAQ,CAAChQ,GAAG,CAAC,KAAKiC,MAAM,CAAClI,IAAI,CAACiG,GAAG,CAAC;oBAEvE,oBACE1G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;sBAET+C,GAAG,EAAE/D,aAAa,GAAG5O,WAAW,GAAG,IAAK;sBACxC,gBAAcoT,QAAQ,CAAChQ,GAAI;sBAC3B,kBAAgBiQ,UAAW;sBAC3BxD,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEY,CAAC,EAAE;sBAAG,CAAE;sBAC/BX,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEY,CAAC,EAAE;sBAAE,CAAE;sBAC9BrM,UAAU,EAAE;wBAAEuM,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,IAAI;wBAAEiH,QAAQ,EAAE;sBAAI,CAAE;sBACzDc,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEN,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnChB,SAAS,EAAG,+BACVd,aAAa,GACT,yCAAyC,GACzC,EACL,EAAE;sBACHzK,KAAK,EAAE;wBACLC,SAAS,EAAEwK,aAAa,GAAG,aAAa,GAAG,UAAU;wBACrD5L,MAAM,EAAE4L,aAAa,GACjB,2EAA2E,GAC3E,MAAM;wBACVvK,UAAU,EAAE,eAAe;wBAC3ByC,MAAM,EAAE8H,aAAa,GAAG,mBAAmB,GAAG,MAAM;wBACpD7H,YAAY,EAAE6H,aAAa,GAAG,MAAM,GAAG,KAAK;wBAC5CyC,UAAU,EAAEzC,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvH3H,QAAQ,EAAE,UAAU;wBACpBD,MAAM,EAAE4H,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAe,QAAA,eAGFjT,OAAA;wBACEgT,SAAS,EAAG,oBAAmB0D,QAAQ,CAAC7Q,IAAI,CAAClC,KAAM,sBAAqB+S,QAAQ,CAAC7Q,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJyD,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAa8O,QAAQ,CAAC7Q,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAkP,QAAA,eAEFjT,OAAA;0BACEgT,SAAS,EAAG,GAAE0D,QAAQ,CAAC7Q,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH6D,KAAK,EAAE;4BACL2C,MAAM,EAAG,aAAYsM,QAAQ,CAAC7Q,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAA6O,QAAA,gBAGFjT,OAAA;4BAAKgT,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjG9T,OAAA;4BAAKgT,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDjT,OAAA;8BAAKgT,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvBjT,OAAA;gCACEgT,SAAS,EAAC,kJAAkJ;gCAC5JvL,KAAK,EAAE;kCACL9D,KAAK,EAAE,SAAS;kCAChBoR,UAAU,EAAE,6BAA6B;kCACzC3K,MAAM,EAAE,iCAAiC;kCACzCxC,SAAS,EAAE;gCACb,CAAE;gCAAAqL,QAAA,GACH,GACE,EAAC0D,UAAU;8BAAA;gCAAAhD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGN9T,OAAA;8BAAKgT,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBjT,OAAA;gCACEgT,SAAS,EAAC,gEAAgE;gCAC1EvL,KAAK,EAAE;kCACLkN,UAAU,EAAE,SAAS;kCACrB/M,SAAS,EAAE,4BAA4B;kCACvCuO,KAAK,EAAE,MAAM;kCACbD,MAAM,EAAE;gCACV,CAAE;gCAAAjD,QAAA,EAEDyD,QAAQ,CAACjK,cAAc,gBACtBzM,OAAA;kCACEoW,GAAG,EAAEM,QAAQ,CAACjK,cAAe;kCAC7B4J,GAAG,EAAEK,QAAQ,CAACtO,IAAK;kCACnB4K,SAAS,EAAC;gCAAyC;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACpD,CAAC,gBAEF9T,OAAA;kCACEgT,SAAS,EAAC,2EAA2E;kCACrFvL,KAAK,EAAE;oCACLkN,UAAU,EAAE,SAAS;oCACrBhR,KAAK,EAAE,SAAS;oCAChB8Q,QAAQ,EAAE;kCACZ,CAAE;kCAAAxB,QAAA,EAEDyD,QAAQ,CAACtO,IAAI,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAA5C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAEL5B,aAAa,iBACZlS,OAAA;gCACEgT,SAAS,EAAC,8FAA8F;gCACxGvL,KAAK,EAAE;kCACLkN,UAAU,EAAE,0CAA0C;kCACtD/M,SAAS,EAAE;gCACb,CAAE;gCAAAqL,QAAA,eAEFjT,OAAA,CAACrB,MAAM;kCAACqU,SAAS,EAAC;gCAA2B;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN9T,OAAA;4BAAKgT,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCjT,OAAA;8BAAKgT,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBjT,OAAA;gCAAKgT,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3CjT,OAAA;kCACEgT,SAAS,EAAC,yCAAyC;kCACnDvL,KAAK,EAAE;oCACL9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAC/B,SAAS;oCAC9BiR,UAAU,EAAG,eAAc2B,QAAQ,CAAC7Q,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAA2M,QAAA,EAEDyD,QAAQ,CAACtO;gCAAI;kCAAAuL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJ5B,aAAa,iBACZlS,OAAA;kCACEgT,SAAS,EAAC,yDAAyD;kCACnEvL,KAAK,EAAE;oCACLkN,UAAU,EAAE,mDAAmD;oCAC/DhR,KAAK,EAAE,SAAS;oCAChBiE,SAAS,EAAE,8DAA8D;oCACzEwC,MAAM,EAAE,mBAAmB;oCAC3B2K,UAAU,EAAE,6BAA6B;oCACzCN,QAAQ,EAAE,MAAM;oCAChBe,UAAU,EAAE;kCACd,CAAE;kCAAAvC,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN9T,OAAA;gCAAKgT,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1CyD,QAAQ,CAAC7K,KAAK,EAAC,gBAAS,EAAC6K,QAAQ,CAAClK,KAAK;8BAAA;gCAAAmH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN9T,OAAA;4BAAKgT,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DjT,OAAA;8BACEgT,SAAS,EAAC,oCAAoC;8BAC9CvL,KAAK,EAAE;gCACL9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAC/B,SAAS;gCAC9BiR,UAAU,EAAG,eAAc2B,QAAQ,CAAC7Q,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAA2M,QAAA,GAEDyD,QAAQ,CAAC/Q,OAAO,CAACoQ,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAApC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGN9T,OAAA;8BAAKgT,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9CjT,OAAA;gCACEgT,SAAS,EAAC,8CAA8C;gCACxDvL,KAAK,EAAE;kCACLmP,eAAe,EAAG,GAAEF,QAAQ,CAAC7Q,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAChC;gCACvB,CAAE;gCAAAoP,QAAA,gBAEFjT,OAAA,CAAClB,OAAO;kCAACkU,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B9T,OAAA;kCAAMgT,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEyD,QAAQ,CAACtK;gCAAiB;kCAAAuH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACN9T,OAAA;gCACEgT,SAAS,EAAC,8CAA8C;gCACxDvL,KAAK,EAAE;kCACLmP,eAAe,EAAE,WAAW;kCAC5BjT,KAAK,EAAE;gCACT,CAAE;gCAAAsP,QAAA,gBAEFjT,OAAA,CAACpB,OAAO;kCAACoU,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B9T,OAAA;kCAAMgT,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEyD,QAAQ,CAAC9J;gCAAa;kCAAA+G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvLD4C,QAAQ,CAAChQ,GAAG;sBAAAiN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwLP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACAzO,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,iBAClC5G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAG,CAAE;cAC/BX,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAE,CAAE;cAC9BrM,UAAU,EAAE;gBAAEuM,KAAK,EAAE,CAAC;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cACtC7J,EAAE,EAAC,yBAAyB;cAAA8J,QAAA,gBAG5BjT,OAAA;gBAAKgT,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCjT,OAAA,CAAC5B,MAAM,CAACqY,EAAE;kBACRzD,SAAS,EAAC,kDAAkD;kBAC5DvL,KAAK,EAAE;oBACLkN,UAAU,EAAE,mDAAmD;oBAC/DE,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzCzO,MAAM,EAAE;kBACV,CAAE;kBACF+M,OAAO,EAAE;oBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC3M,UAAU,EAAE;oBAAE4L,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ9T,OAAA;kBAAGgT,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN9T,OAAA;gBAAKgT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CpL,iBAAiB,CAAC,CAAC,CAACoB,GAAG,CAAElD,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMuF,UAAU,GAAGtI,YAAY,CAAC+C,SAAS,CAAC;kBAC1C,MAAM8Q,QAAQ,GAAGvL,UAAU,CAAC/K,KAAK,CAAC6K,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACEpL,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;oBAET+C,GAAG,EAAGa,EAAE,IAAM5T,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC,GAAG+Q,EAAI;oBAClD3D,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEY,CAAC,EAAE;oBAAG,CAAE;oBAC/BX,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEY,CAAC,EAAE;oBAAE,CAAE;oBAC9BrM,UAAU,EAAE;sBAAEuM,KAAK,EAAE,GAAG;sBAAEX,QAAQ,EAAE;oBAAI,CAAE;oBAC1CP,SAAS,EAAC,mGAAmG;oBAC7G7J,EAAE,EAAG,UAASpD,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAkN,QAAA,gBAGvBjT,OAAA;sBAAKgT,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDjT,OAAA;wBAAKgT,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCjT,OAAA;0BACEgT,SAAS,EAAC,gEAAgE;0BAC1EvL,KAAK,EAAE;4BACLkN,UAAU,EAAG,2BAA0BxP,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrFuG,MAAM,EAAG,aAAYjF,MAAM,CAACf,WAAY,IAAG;4BAC3CwD,SAAS,EAAG,cAAazC,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAkP,QAAA,EAED9N,MAAM,CAACb;wBAAU;0BAAAqP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACN9T,OAAA;0BAAAiT,QAAA,gBACEjT,OAAA;4BACEgT,SAAS,EAAC,0BAA0B;4BACpCvL,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBiR,UAAU,EAAG,eAAc5P,MAAM,CAACpB,WAAY,EAAC;8BAC/CuC,MAAM,EAAE;4BACV,CAAE;4BAAA2M,QAAA,GAED9N,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAAyP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL9T,OAAA;4BAAGgT,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjC3H,UAAU,CAAC/K,KAAK,CAACqG,MAAM,EAAC,oBAAa,EAACzB,MAAM,CAAChB,WAAW;0BAAA;4BAAAwP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN9T,OAAA,CAAC5B,MAAM,CAACgW,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BE,OAAO,EAAEA,CAAA,KAAM3N,kBAAkB,CAACd,SAAS,CAAE;wBAC7CiN,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAAC3H,UAAU,CAAC/K,KAAK,CAACqG,MAAM,EAAC,GACrC;sBAAA;wBAAA+M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGN9T,OAAA;sBAAKgT,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClE4D,QAAQ,CAAC5N,GAAG,CAAC,CAACyN,QAAQ,EAAEpK,KAAK,KAAK;wBACjC,MAAM4F,aAAa,GAAGzR,IAAI,IAAIiW,QAAQ,CAAChQ,GAAG,KAAKjG,IAAI,CAACiG,GAAG;wBACvD,MAAMqQ,UAAU,GAAGzK,KAAK,GAAG,CAAC;wBAE5B,oBACEtM,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;0BAET,gBAAcwD,QAAQ,CAAChQ,GAAI;0BAC3B,kBAAgBqQ,UAAW;0BAC3B5D,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEkB,KAAK,EAAE;0BAAI,CAAE;0BACpCjB,OAAO,EAAE;4BAAED,OAAO,EAAE,CAAC;4BAAEkB,KAAK,EAAE;0BAAE,CAAE;0BAClC3M,UAAU,EAAE;4BAAEuM,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,GAAG;4BAAEiH,QAAQ,EAAE;0BAAI,CAAE;0BACxDc,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAEN,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnChB,SAAS,EAAG,YACVd,aAAa,GACT,2BAA2B,GAC3B,EACL,EAAE;0BAAAe,QAAA,eAEHjT,OAAA;4BACEgT,SAAS,EAAG,qBAAoB0D,QAAQ,CAAC7Q,IAAI,CAAClC,KAAM,qBAAoB+S,QAAQ,CAAC7Q,IAAI,CAAC7B,IAAK,YAAY;4BACvGyD,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAa8O,QAAQ,CAAC7Q,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAAkP,QAAA,eAEFjT,OAAA;8BACEgT,SAAS,EAAG,GAAE0D,QAAQ,CAAC7Q,IAAI,CAACjC,OAAQ,uEAAuE;8BAAAqP,QAAA,gBAE3GjT,OAAA;gCAAKgT,SAAS,EAAC;8BAAgE;gCAAAW,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtF9T,OAAA;gCACEgT,SAAS,EAAC,mGAAmG;gCAC7GvL,KAAK,EAAE;kCACLkN,UAAU,EAAExP,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChByG,MAAM,EAAE;gCACV,CAAE;gCAAA6I,QAAA,GACH,GACE,EAAC8D,UAAU;8BAAA;gCAAApD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGN9T,OAAA;gCAAKgT,SAAS,EAAG,yBACfd,aAAa,GACT,wCAAwC,GACxC,EACL,EAAE;gCAAAe,QAAA,gBACDjT,OAAA;kCACEgT,SAAS,EAAC,wEAAwE;kCAClFvL,KAAK,EAAE;oCACLkN,UAAU,EAAE,SAAS;oCACrB/M,SAAS,EAAE,4BAA4B;oCACvCuO,KAAK,EAAE,MAAM;oCACbD,MAAM,EAAE;kCACV,CAAE;kCAAAjD,QAAA,EAEDyD,QAAQ,CAACjK,cAAc,gBACtBzM,OAAA;oCACEoW,GAAG,EAAEM,QAAQ,CAACjK,cAAe;oCAC7B4J,GAAG,EAAEK,QAAQ,CAACtO,IAAK;oCACnB4K,SAAS,EAAC;kCAAyC;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpD,CAAC,gBAEF9T,OAAA;oCACEgT,SAAS,EAAC,2EAA2E;oCACrFvL,KAAK,EAAE;sCACLkN,UAAU,EAAE,SAAS;sCACrBhR,KAAK,EAAE,SAAS;sCAChB8Q,QAAQ,EAAE;oCACZ,CAAE;oCAAAxB,QAAA,EAEDyD,QAAQ,CAACtO,IAAI,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kCAAC;oCAAA5C,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACnC;gCACN;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,EACL5B,aAAa,iBACZlS,OAAA;kCACEgT,SAAS,EAAC,iGAAiG;kCAC3GvL,KAAK,EAAE;oCACLkN,UAAU,EAAE,0CAA0C;oCACtD/M,SAAS,EAAE;kCACb,CAAE;kCAAAqL,QAAA,eAEFjT,OAAA,CAACrB,MAAM;oCAACqU,SAAS,EAAC;kCAA2B;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN9T,OAAA;gCACEgT,SAAS,EAAC,iCAAiC;gCAC3CvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAC/B;gCAAU,CAAE;gCAAAmP,QAAA,GAEzCyD,QAAQ,CAACtO,IAAI,EACb8J,aAAa,iBACZlS,OAAA;kCAAMgT,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAEL9T,OAAA;gCAAKgT,SAAS,EAAC,yBAAyB;gCAACvL,KAAK,EAAE;kCAAE9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAChC;gCAAU,CAAE;gCAAAoP,QAAA,GAChFyD,QAAQ,CAAC/Q,OAAO,CAACoQ,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAApC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAEN9T,OAAA;gCAAKgT,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChDjT,OAAA;kCAAMyH,KAAK,EAAE;oCAAE9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAChC;kCAAU,CAAE;kCAAAoP,QAAA,GAAC,eAC5C,EAACyD,QAAQ,CAACtK,iBAAiB;gCAAA;kCAAAuH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACP9T,OAAA;kCAAMyH,KAAK,EAAE;oCAAE9D,KAAK,EAAE+S,QAAQ,CAAC7Q,IAAI,CAAChC;kCAAU,CAAE;kCAAAoP,QAAA,GAAC,eAC5C,EAACyD,QAAQ,CAAC9J,aAAa;gCAAA;kCAAA+G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GA3GD4C,QAAQ,CAAChQ,GAAG;0BAAAiN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA4GP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGLxI,UAAU,CAAC/K,KAAK,CAACqG,MAAM,GAAG,CAAC,iBAC1B5G,OAAA;sBAAKgT,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BjT,OAAA;wBAAGgT,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC3H,UAAU,CAAC/K,KAAK,CAACqG,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAA+M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GAhLI/N,SAAS;oBAAA4N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiLJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMApS,WAAW,CAACkF,MAAM,GAAG,CAAC,iBACrB5G,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAG,CAAE;cAC/BX,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAE,CAAE;cAC9BrM,UAAU,EAAE;gBAAEuM,KAAK,EAAE,GAAG;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJjT,OAAA;gBAAKgT,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjT,OAAA;kBAAIgT,SAAS,EAAC,wBAAwB;kBAACvL,KAAK,EAAE;oBAC5C9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC9T,OAAA;kBAAKgT,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DjT,OAAA;oBAAKgT,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CjT,OAAA;sBAAKgT,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CvR,WAAW,CAAC4E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,SAAS,CAAC,CAAC3G;oBAAM;sBAAA+M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN9T,OAAA;sBAAKgT,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN9T,OAAA;oBAAKgT,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CjT,OAAA;sBAAKgT,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CvR,WAAW,CAAC4E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,eAAe,CAAC,CAAC3G;oBAAM;sBAAA+M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN9T,OAAA;sBAAKgT,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN9T,OAAA;oBAAKgT,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CjT,OAAA;sBAAKgT,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CvR,WAAW,CAAC4E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8G,UAAU,KAAK,WAAW,CAAC,CAAC3G;oBAAM;sBAAA+M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN9T,OAAA;sBAAKgT,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9T,OAAA;kBAAGgT,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAhS,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrC9B,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cACpCjB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAE,CAAE;cAClC3M,UAAU,EAAE;gBAAEuM,KAAK,EAAE,GAAG;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJjT,OAAA;gBAAKgT,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjT,OAAA;kBAAIgT,SAAS,EAAC,yBAAyB;kBAACvL,KAAK,EAAE;oBAC7C9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B9T,OAAA;kBAAKgT,SAAS,EAAC,0BAA0B;kBAACvL,KAAK,EAAE;oBAC/C9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,GAAC,GAAC,EAACnR,eAAe;gBAAA;kBAAA6R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3B9T,OAAA;kBAAGgT,SAAS,EAAC,SAAS;kBAACvL,KAAK,EAAE;oBAC5B9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGD9T,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAG,CAAE;cAC/BX,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEY,CAAC,EAAE;cAAE,CAAE;cAC9BrM,UAAU,EAAE;gBAAEuM,KAAK,EAAE,CAAC;gBAAEX,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BjT,OAAA;gBAAKgT,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IjT,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;kBACTG,OAAO,EAAE;oBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC3M,UAAU,EAAE;oBAAE4L,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9CjT,OAAA,CAACb,QAAQ;oBAAC6T,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACb9T,OAAA;kBAAIgT,SAAS,EAAC,yBAAyB;kBAACvL,KAAK,EAAE;oBAC7C9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B9T,OAAA;kBAAGgT,SAAS,EAAC,gCAAgC;kBAACvL,KAAK,EAAE;oBACnD9D,KAAK,EAAE,SAAS;oBAChBoR,UAAU,EAAE,6BAA6B;oBACzCS,UAAU,EAAE;kBACd,CAAE;kBAAAvC,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ9T,OAAA,CAAC5B,MAAM,CAACgW,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BtB,SAAS,EAAC,sJAAsJ;kBAChKwB,OAAO,EAAEA,CAAA,KAAMvK,MAAM,CAAC+M,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAhE,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZpS,WAAW,CAACkF,MAAM,KAAK,CAAC,IAAI,CAAChF,OAAO,iBACnC5B,OAAA,CAAC5B,MAAM,CAAC8U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cACpCjB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAE,CAAE;cAClCtB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BjT,OAAA,CAACvB,QAAQ;gBAACuU,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7D9T,OAAA;gBAAIgT,SAAS,EAAC,yBAAyB;gBAACvL,KAAK,EAAE;kBAC7C9D,KAAK,EAAE,SAAS;kBAChBoR,UAAU,EAAE,6BAA6B;kBACzCS,UAAU,EAAE;gBACd,CAAE;gBAAAvC,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB9T,OAAA;gBAAGgT,SAAS,EAAC,SAAS;gBAACvL,KAAK,EAAE;kBAC5B9D,KAAK,EAAE,SAAS;kBAChBoR,UAAU,EAAE,6BAA6B;kBACzCS,UAAU,EAAE;gBACd,CAAE;gBAAAvC,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC1T,EAAA,CAnwFID,kBAAkB;EAAA,QACJ7B,WAAW,EAoCZC,WAAW;AAAA;AAAA2Y,EAAA,GArCxB/W,kBAAkB;AAqwFxB,eAAeA,kBAAkB;AAAC,IAAA+W,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}