{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nconst AdminProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    return null;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    return null;\n  }\n\n  // If user is admin, render the children\n  return children;\n};\n_s(AdminProtectedRoute, \"mL2t1COiLoZ5+Kl1K33h74o9/R8=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AdminProtectedRoute;\nexport default AdminProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminProtectedRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useNavigate", "message", "AdminProtectedRoute", "children", "_s", "user", "state", "navigate", "isAdmin", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\n\nconst AdminProtectedRoute = ({ children }) => {\n  const { user } = useSelector((state) => state.user);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    return null;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    return null;\n  }\n\n  // If user is admin, render the children\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAE9B,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM;IAAEC;EAAK,CAAC,GAAGN,WAAW,CAAEO,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,IAAIO,IAAI,IAAI,CAACA,IAAI,CAACG,OAAO,EAAE;MACzBP,OAAO,CAACQ,KAAK,CAAC,2CAA2C,CAAC;MAC1DF,QAAQ,CAAC,WAAW,CAAC;IACvB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;;EAEpB;EACA,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACA,IAAI,CAACG,OAAO,EAAE;IACjB,OAAO,IAAI;EACb;;EAEA;EACA,OAAOL,QAAQ;AACjB,CAAC;AAACC,EAAA,CAxBIF,mBAAmB;EAAA,QACNH,WAAW,EACXC,WAAW;AAAA;AAAAU,EAAA,GAFxBR,mBAAmB;AA0BzB,eAAeA,mBAAmB;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}