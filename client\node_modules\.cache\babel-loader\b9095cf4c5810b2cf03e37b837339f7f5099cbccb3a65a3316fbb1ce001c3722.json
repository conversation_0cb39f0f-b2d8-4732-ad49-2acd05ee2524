{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbQuestionMark, TbUsers, TbTrophy, TbPlayerPlay, TbStar, TbTarget, TbBrain, TbChevronRight, TbCheck, TbX } from 'react-icons/tb';\nimport Card from './Card';\nimport Button from './Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizCard = ({\n  quiz,\n  onStart,\n  onView,\n  showResults = false,\n  userResult = null,\n  className = '',\n  ...props\n}) => {\n  var _quiz$questions, _quiz$questions2;\n  const getDifficultyColor = difficulty => {\n    switch (difficulty === null || difficulty === void 0 ? void 0 : difficulty.toLowerCase()) {\n      case 'easy':\n        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white';\n      case 'medium':\n        return 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white';\n      case 'hard':\n        return 'bg-gradient-to-r from-red-500 to-pink-500 text-white';\n      default:\n        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white';\n    }\n  };\n\n  // Get quiz status and colors based on user result\n  const getQuizStatus = () => {\n    if (!userResult) {\n      return {\n        status: 'not-attempted',\n        color: 'from-blue-500 to-indigo-500',\n        borderColor: 'border-blue-300',\n        bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-100',\n        headerColor: 'bg-gradient-to-br from-blue-600 via-indigo-700 to-blue-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-white'\n      };\n    }\n    const passed = userResult.percentage >= (quiz.passingMarks || 60);\n    if (passed) {\n      return {\n        status: 'passed',\n        color: 'from-green-500 to-emerald-500',\n        borderColor: 'border-green-400',\n        bgColor: 'bg-gradient-to-br from-green-50 to-emerald-100',\n        headerColor: 'bg-gradient-to-br from-green-600 via-emerald-700 to-green-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-gradient-to-br from-green-50 to-emerald-50'\n      };\n    } else {\n      return {\n        status: 'failed',\n        color: 'from-red-500 to-pink-500',\n        borderColor: 'border-red-400',\n        bgColor: 'bg-gradient-to-br from-red-50 to-pink-100',\n        headerColor: 'bg-gradient-to-br from-red-600 via-pink-700 to-red-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-gradient-to-br from-red-50 to-pink-50'\n      };\n    }\n  };\n  const quizStatus = getQuizStatus();\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    whileHover: {\n      y: -12,\n      scale: 1.03\n    },\n    transition: {\n      duration: 0.4,\n      ease: \"easeOut\"\n    },\n    className: `quiz-card-modern group ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `quiz-card overflow-hidden h-full border-2 shadow-2xl hover:shadow-3xl transition-all duration-500 relative rounded-2xl ${quizStatus.cardBg} ${quizStatus.borderColor} ${quizStatus.textColor}`,\n      ...props,\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0,\n          rotate: -10\n        },\n        animate: {\n          scale: 1,\n          rotate: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        className: \"absolute top-4 right-4 z-10\",\n        children: userResult ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-3 py-1.5 rounded-lg text-xs font-bold shadow-lg border backdrop-blur-sm bg-gradient-to-r ${quizStatus.color} text-white ${quizStatus.borderColor}`,\n            children: quizStatus.status === 'passed' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-3 h-3 inline mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 21\n              }, this), \"PASSED\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-3 h-3 inline mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 21\n              }, this), \"FAILED\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-2 py-1 rounded text-xs font-semibold bg-white/90 text-gray-800 text-center\",\n            children: [userResult.percentage, \"% \\u2022 \", userResult.xpEarned || 0, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-3 py-1.5 rounded-lg text-xs font-bold shadow-lg border backdrop-blur-sm bg-gradient-to-r from-gray-500 to-slate-500 text-white border-gray-300\",\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"w-3 h-3 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), \"NOT ATTEMPTED\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${quizStatus.headerColor} p-6 text-white relative`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0 right-0 w-40 h-40 bg-white rounded-full -translate-y-20 translate-x-20 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full translate-y-16 -translate-x-16 animate-pulse delay-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-1/2 left-1/2 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse delay-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-8 w-2 h-2 bg-white rounded-full animate-bounce delay-100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-12 right-16 w-1 h-1 bg-white rounded-full animate-bounce delay-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute bottom-8 left-12 w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shimmer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-14 h-14 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30 shadow-xl group-hover:scale-110 transition-transform duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                      className: \"w-7 h-7 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap items-center gap-2 mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white text-xs font-bold uppercase tracking-wider bg-blue-600/90 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\",\n                        children: [\"Class \", quiz.class || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 25\n                      }, this), quiz.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white text-xs font-bold uppercase tracking-wider bg-gradient-to-r from-indigo-500 to-purple-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\",\n                        children: [\"\\uD83D\\uDCD6 \", quiz.subject]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 149,\n                        columnNumber: 27\n                      }, this), quiz.category && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white text-xs font-bold uppercase tracking-wider bg-gradient-to-r from-orange-500 to-red-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\",\n                        children: [\"\\uD83D\\uDCDA \", quiz.category]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 154,\n                        columnNumber: 27\n                      }, this), quiz.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white text-xs font-bold tracking-wider bg-gradient-to-r from-purple-500 to-pink-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\",\n                        children: [\"\\uD83C\\uDFAF \", quiz.topic]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 159,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-100 text-sm font-bold flex flex-wrap items-center gap-2 drop-shadow-md\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\",\n                        children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                          className: \"w-3 h-3 text-blue-200\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-100 text-xs\",\n                          children: ((_quiz$questions = quiz.questions) === null || _quiz$questions === void 0 ? void 0 : _quiz$questions.length) || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\",\n                        children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                          className: \"w-3 h-3 text-blue-200\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 170,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-100 text-xs\",\n                          children: [quiz.duration || 30, \"m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 171,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\",\n                        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                          className: \"w-3 h-3 text-blue-200\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 174,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-100 text-xs\",\n                          children: [quiz.passingMarks || 70, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 175,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 173,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center space-x-1 bg-yellow-600/30 px-2 py-1 rounded-lg border border-yellow-300/20\",\n                        children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                          className: \"w-3 h-3 text-yellow-200\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 179,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-yellow-100 text-xs\",\n                          children: [quiz.xpPoints || 100, \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 180,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 178,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-2 line-clamp-2 text-blue-50 drop-shadow-xl leading-tight\",\n                  style: {\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.7)'\n                  },\n                  children: quiz.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-2 mb-3\",\n                  children: [quiz.subject && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-blue-700/40 text-blue-100 border border-blue-300/30\",\n                    children: [\"\\uD83D\\uDCDA \", quiz.subject]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this), quiz.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-purple-700/40 text-purple-100 border border-purple-300/30\",\n                    children: [\"\\uD83C\\uDFAF \", quiz.topic]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-sm line-clamp-2 font-medium leading-relaxed bg-blue-900/20 px-3 py-2 rounded-lg backdrop-blur-sm border border-blue-300/20 mb-3\",\n                  children: quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), quiz.category && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white shadow-xl border-2 border-white/30 backdrop-blur-sm\",\n                    style: {\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                      className: \"w-4 h-4 mr-2 drop-shadow-md\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 25\n                    }, this), quiz.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 opacity-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500 to-purple-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-200/60 rounded-2xl p-4 text-center border border-blue-200/70 hover:border-blue-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-700 mb-1\",\n                  children: ((_quiz$questions2 = quiz.questions) === null || _quiz$questions2 === void 0 ? void 0 : _quiz$questions2.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-600 font-semibold uppercase tracking-wide\",\n                  children: \"Questions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-emerald-50 via-emerald-100/80 to-emerald-200/60 rounded-2xl p-4 text-center border border-emerald-200/70 hover:border-emerald-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbClock, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-emerald-700 mb-1\",\n                  children: quiz.duration || 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-emerald-600 font-semibold uppercase tracking-wide\",\n                  children: \"Minutes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              className: \"bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-200/60 rounded-2xl p-4 text-center border border-purple-200/70 hover:border-purple-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-purple-700 mb-1\",\n                  children: quiz.passingMarks || 70\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-purple-600 font-semibold uppercase tracking-wide\",\n                  children: \"Pass %\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [quiz.topic && /*#__PURE__*/_jsxDEV(motion.span, {\n              whileHover: {\n                scale: 1.05\n              },\n              className: \"inline-flex items-center px-3 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-xl border-2 border-white/30 backdrop-blur-sm\",\n              style: {\n                textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n              },\n              children: [\"\\uD83C\\uDFAF \", quiz.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), quiz.difficulty && /*#__PURE__*/_jsxDEV(motion.span, {\n              whileHover: {\n                scale: 1.05\n              },\n              className: `inline-flex items-center px-3 py-2 rounded-xl text-sm font-bold shadow-lg border-2 border-white/30 ${getDifficultyColor(quiz.difficulty)}`,\n              style: {\n                textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n              },\n              children: quiz.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), quiz.attempts > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-xl border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-4 h-4 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-gray-700\",\n              children: [quiz.attempts, \" attempts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), userResult && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: `border-2 rounded-2xl p-5 mb-6 relative overflow-hidden ${quizStatus.status === 'passed' ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100/50 border-green-200/70' : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100/50 border-red-200/70'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 ${quizStatus.status === 'passed' ? 'bg-green-500' : 'bg-red-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute bottom-0 left-0 w-16 h-16 rounded-full translate-y-8 -translate-x-8 ${quizStatus.status === 'passed' ? 'bg-emerald-500' : 'bg-pink-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${quizStatus.status === 'passed' ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'}`,\n                  children: quizStatus.status === 'passed' ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm font-bold ${quizStatus.status === 'passed' ? 'text-green-900' : 'text-red-900'}`,\n                    style: {\n                      textShadow: '1px 1px 2px rgba(255,255,255,0.5)'\n                    },\n                    children: [\"Last Attempt - \", quiz.subject || 'Subject']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-xs font-semibold ${quizStatus.status === 'passed' ? 'text-green-800' : 'text-red-800'}`,\n                    style: {\n                      textShadow: '1px 1px 2px rgba(255,255,255,0.5)'\n                    },\n                    children: new Date(userResult.completedAt).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-3xl font-black drop-shadow-lg ${quizStatus.status === 'passed' ? 'text-green-900' : 'text-red-900'}`,\n                style: {\n                  textShadow: '2px 2px 4px rgba(255,255,255,0.5)'\n                },\n                children: [userResult.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-center p-3 rounded-xl ${quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-lg font-bold ${quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'}`,\n                  children: userResult.correctAnswers || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-xs font-semibold ${quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'}`,\n                  children: \"Correct\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-center p-3 rounded-xl ${quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-lg font-bold ${quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'}`,\n                  children: (userResult.totalQuestions || 0) - (userResult.correctAnswers || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-xs font-semibold ${quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'}`,\n                  children: \"Wrong\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-center p-3 rounded-xl ${quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-lg font-bold ${quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'}`,\n                  children: userResult.xpEarned || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-xs font-semibold ${quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'}`,\n                  children: \"XP Gained\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-full rounded-full h-3 mb-3 overflow-hidden ${quizStatus.status === 'passed' ? 'bg-green-200/50' : 'bg-red-200/50'}`,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  width: 0\n                },\n                animate: {\n                  width: `${userResult.percentage}%`\n                },\n                transition: {\n                  duration: 1,\n                  ease: \"easeOut\"\n                },\n                className: `h-full rounded-full shadow-sm ${quizStatus.status === 'passed' ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-red-500 to-pink-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-4 py-2 rounded-full font-bold shadow-md text-white ${quizStatus.status === 'passed' ? 'bg-gradient-to-r from-green-600 to-emerald-600 border border-green-400' : 'bg-gradient-to-r from-red-600 to-pink-600 border border-red-400'}`,\n                style: {\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\n                },\n                children: quizStatus.status === 'passed' ? '✅ PASSED' : '❌ FAILED'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 pb-6 bg-gradient-to-br from-gray-50/80 to-white border-t border-gray-200/30 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10 pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                size: \"md\",\n                className: \"w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-800 border-0 shadow-xl hover:shadow-2xl transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 font-bold text-white relative overflow-hidden group\",\n                onClick: onStart,\n                icon: /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n                  className: \"group-hover:scale-125 group-hover:rotate-12 transition-all duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10 flex items-center justify-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: showResults && userResult ? 'Retake Quiz' : 'Start Quiz'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n                    className: \"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), showResults && onView && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: 20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"md\",\n                className: \"bg-white/90 backdrop-blur-sm border-2 border-indigo-200/70 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-400 hover:text-indigo-700 transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 shadow-lg hover:shadow-xl font-semibold relative overflow-hidden group\",\n                onClick: onView,\n                icon: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 27\n                }, this),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10\",\n                  children: \"Results\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-700 font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gray-100 px-3 py-1 rounded-full\",\n              children: \"Click to start your learning journey\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), quiz.progress && quiz.progress > 0 && quiz.progress < 100 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-sm mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold flex items-center space-x-2 text-gray-800\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-4 h-4 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Learning Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded-lg\",\n            children: [quiz.progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full bg-gray-200/70 rounded-full h-3 overflow-hidden shadow-inner\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              width: 0\n            },\n            animate: {\n              width: `${quiz.progress}%`\n            },\n            transition: {\n              duration: 1,\n              ease: \"easeOut\"\n            },\n            className: \"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full shadow-sm relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/30 to-transparent rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-700 font-medium bg-gray-100 px-3 py-1 rounded-full\",\n            children: \"Keep going! You're making great progress.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl\",\n        whileHover: {\n          opacity: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        whileHover: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(TbChevronRight, {\n          className: \"w-4 h-4 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_c = QuizCard;\nexport const QuizGrid = ({\n  quizzes,\n  onQuizStart,\n  onQuizView,\n  showResults = false,\n  userResults = {},\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `quiz-grid-container ${className}`,\n    children: quizzes.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.3,\n        delay: Math.min(index * 0.1, 0.8)\n      },\n      className: \"h-full\",\n      children: /*#__PURE__*/_jsxDEV(QuizCard, {\n        quiz: quiz,\n        onStart: () => onQuizStart(quiz),\n        onView: onQuizView ? () => onQuizView(quiz) : undefined,\n        showResults: showResults,\n        userResult: userResults[quiz._id],\n        className: \"h-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 11\n      }, this)\n    }, quiz._id || index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 548,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizGrid;\nexport default QuizCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"QuizCard\");\n$RefreshReg$(_c2, \"QuizGrid\");", "map": {"version": 3, "names": ["React", "motion", "TbClock", "TbQuestionMark", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbStar", "TbTarget", "TbBrain", "TbChevronRight", "TbCheck", "TbX", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizCard", "quiz", "onStart", "onView", "showResults", "userResult", "className", "props", "_quiz$questions", "_quiz$questions2", "getDifficultyColor", "difficulty", "toLowerCase", "getQuizStatus", "status", "color", "borderColor", "bgColor", "headerColor", "textColor", "cardBg", "passed", "percentage", "passingMarks", "quizStatus", "div", "initial", "opacity", "y", "animate", "whileHover", "scale", "transition", "duration", "ease", "children", "rotate", "delay", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xpEarned", "class", "subject", "category", "topic", "questions", "length", "xpPoints", "style", "textShadow", "name", "description", "span", "attempts", "Date", "completedAt", "toLocaleDateString", "correctAnswers", "totalQuestions", "width", "variant", "size", "onClick", "icon", "x", "progress", "_c", "QuizGrid", "quizzes", "onQuizStart", "onQuizView", "userResults", "map", "index", "Math", "min", "undefined", "_id", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbQuestionMark, TbUsers, TbTrophy, TbPlayerPlay, TbStar, TbTarget, TbBrain, TbChevronRight, TbCheck, TbX } from 'react-icons/tb';\nimport Card from './Card';\nimport Button from './Button';\n\nconst QuizCard = ({\n  quiz,\n  onStart,\n  onView,\n  showResults = false,\n  userResult = null,\n  className = '',\n  ...props\n}) => {\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty?.toLowerCase()) {\n      case 'easy':\n        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white';\n      case 'medium':\n        return 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white';\n      case 'hard':\n        return 'bg-gradient-to-r from-red-500 to-pink-500 text-white';\n      default:\n        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white';\n    }\n  };\n\n  // Get quiz status and colors based on user result\n  const getQuizStatus = () => {\n    if (!userResult) {\n      return {\n        status: 'not-attempted',\n        color: 'from-blue-500 to-indigo-500',\n        borderColor: 'border-blue-300',\n        bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-100',\n        headerColor: 'bg-gradient-to-br from-blue-600 via-indigo-700 to-blue-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-white'\n      };\n    }\n\n    const passed = userResult.percentage >= (quiz.passingMarks || 60);\n    if (passed) {\n      return {\n        status: 'passed',\n        color: 'from-green-500 to-emerald-500',\n        borderColor: 'border-green-400',\n        bgColor: 'bg-gradient-to-br from-green-50 to-emerald-100',\n        headerColor: 'bg-gradient-to-br from-green-600 via-emerald-700 to-green-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-gradient-to-br from-green-50 to-emerald-50'\n      };\n    } else {\n      return {\n        status: 'failed',\n        color: 'from-red-500 to-pink-500',\n        borderColor: 'border-red-400',\n        bgColor: 'bg-gradient-to-br from-red-50 to-pink-100',\n        headerColor: 'bg-gradient-to-br from-red-600 via-pink-700 to-red-800',\n        textColor: 'text-gray-800',\n        cardBg: 'bg-gradient-to-br from-red-50 to-pink-50'\n      };\n    }\n  };\n\n  const quizStatus = getQuizStatus();\n\n\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{ y: -12, scale: 1.03 }}\n      transition={{ duration: 0.4, ease: \"easeOut\" }}\n      className={`quiz-card-modern group ${className}`}\n    >\n      <div\n        className={`quiz-card overflow-hidden h-full border-2 shadow-2xl hover:shadow-3xl transition-all duration-500 relative rounded-2xl ${quizStatus.cardBg} ${quizStatus.borderColor} ${quizStatus.textColor}`}\n        {...props}\n      >\n        {/* Status Tag - Top Right */}\n        <motion.div\n          initial={{ scale: 0, rotate: -10 }}\n          animate={{ scale: 1, rotate: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          className=\"absolute top-4 right-4 z-10\"\n        >\n          {userResult ? (\n            <div className=\"flex flex-col gap-1\">\n              <div className={`px-3 py-1.5 rounded-lg text-xs font-bold shadow-lg border backdrop-blur-sm bg-gradient-to-r ${quizStatus.color} text-white ${quizStatus.borderColor}`}>\n                {quizStatus.status === 'passed' ? (\n                  <>\n                    <TbCheck className=\"w-3 h-3 inline mr-1\" />\n                    PASSED\n                  </>\n                ) : (\n                  <>\n                    <TbX className=\"w-3 h-3 inline mr-1\" />\n                    FAILED\n                  </>\n                )}\n              </div>\n              <div className=\"px-2 py-1 rounded text-xs font-semibold bg-white/90 text-gray-800 text-center\">\n                {userResult.percentage}% • {userResult.xpEarned || 0} XP\n              </div>\n            </div>\n          ) : (\n            <div className=\"px-3 py-1.5 rounded-lg text-xs font-bold shadow-lg border backdrop-blur-sm bg-gradient-to-r from-gray-500 to-slate-500 text-white border-gray-300\">\n              <TbClock className=\"w-3 h-3 inline mr-1\" />\n              NOT ATTEMPTED\n            </div>\n          )}\n        </motion.div>\n        {/* Enhanced Header with Dynamic Gradient */}\n        <div className=\"relative overflow-hidden\">\n          <div className={`${quizStatus.headerColor} p-6 text-white relative`}>\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 opacity-20\">\n              <div className=\"absolute top-0 right-0 w-40 h-40 bg-white rounded-full -translate-y-20 translate-x-20 animate-pulse\"></div>\n              <div className=\"absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full translate-y-16 -translate-x-16 animate-pulse delay-700\"></div>\n              <div className=\"absolute top-1/2 left-1/2 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse delay-300\"></div>\n            </div>\n\n            {/* Floating Particles */}\n            <div className=\"absolute inset-0\">\n              <div className=\"absolute top-4 right-8 w-2 h-2 bg-white rounded-full animate-bounce delay-100\"></div>\n              <div className=\"absolute top-12 right-16 w-1 h-1 bg-white rounded-full animate-bounce delay-500\"></div>\n              <div className=\"absolute bottom-8 left-12 w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-300\"></div>\n            </div>\n\n            {/* Shimmer Effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shimmer\"></div>\n\n            <div className=\"relative z-10\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-4\">\n                    <div className=\"w-14 h-14 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30 shadow-xl group-hover:scale-110 transition-transform duration-300\">\n                      <TbBrain className=\"w-7 h-7 text-white\" />\n                    </div>\n                    <div>\n                      <div className=\"flex flex-wrap items-center gap-2 mb-3\">\n                        <span className=\"text-white text-xs font-bold uppercase tracking-wider bg-blue-600/90 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\">\n                          Class {quiz.class || 'N/A'}\n                        </span>\n                        {quiz.subject && (\n                          <span className=\"text-white text-xs font-bold uppercase tracking-wider bg-gradient-to-r from-indigo-500 to-purple-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\">\n                            📖 {quiz.subject}\n                          </span>\n                        )}\n                        {quiz.category && (\n                          <span className=\"text-white text-xs font-bold uppercase tracking-wider bg-gradient-to-r from-orange-500 to-red-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\">\n                            📚 {quiz.category}\n                          </span>\n                        )}\n                        {quiz.topic && (\n                          <span className=\"text-white text-xs font-bold tracking-wider bg-gradient-to-r from-purple-500 to-pink-500 px-3 py-1.5 rounded-full border border-white/30 shadow-lg\">\n                            🎯 {quiz.topic}\n                          </span>\n                        )}\n                      </div>\n                      <div className=\"text-blue-100 text-sm font-bold flex flex-wrap items-center gap-2 drop-shadow-md\">\n                        <span className=\"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\">\n                          <TbQuestionMark className=\"w-3 h-3 text-blue-200\" />\n                          <span className=\"text-blue-100 text-xs\">{quiz.questions?.length || 0}</span>\n                        </span>\n                        <span className=\"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\">\n                          <TbClock className=\"w-3 h-3 text-blue-200\" />\n                          <span className=\"text-blue-100 text-xs\">{quiz.duration || 30}m</span>\n                        </span>\n                        <span className=\"flex items-center space-x-1 bg-blue-800/30 px-2 py-1 rounded-lg border border-blue-300/20\">\n                          <TbTarget className=\"w-3 h-3 text-blue-200\" />\n                          <span className=\"text-blue-100 text-xs\">{quiz.passingMarks || 70}%</span>\n                        </span>\n                        {/* XP Points */}\n                        <span className=\"flex items-center space-x-1 bg-yellow-600/30 px-2 py-1 rounded-lg border border-yellow-300/20\">\n                          <TbStar className=\"w-3 h-3 text-yellow-200\" />\n                          <span className=\"text-yellow-100 text-xs\">{quiz.xpPoints || 100} XP</span>\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-2 line-clamp-2 text-blue-50 drop-shadow-xl leading-tight\" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n                    {quiz.name}\n                  </h3>\n\n                  {/* Subject and Topic */}\n                  <div className=\"flex flex-wrap gap-2 mb-3\">\n                    {quiz.subject && (\n                      <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-blue-700/40 text-blue-100 border border-blue-300/30\">\n                        📚 {quiz.subject}\n                      </span>\n                    )}\n                    {quiz.topic && (\n                      <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-purple-700/40 text-purple-100 border border-purple-300/30\">\n                        🎯 {quiz.topic}\n                      </span>\n                    )}\n                  </div>\n\n                  <p className=\"text-blue-100 text-sm line-clamp-2 font-medium leading-relaxed bg-blue-900/20 px-3 py-2 rounded-lg backdrop-blur-sm border border-blue-300/20 mb-3\">\n                    {quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'}\n                  </p>\n\n                  {/* Subject Name Below Description */}\n                  {quiz.category && (\n                    <div className=\"mb-4\">\n                      <span className=\"inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white shadow-xl border-2 border-white/30 backdrop-blur-sm\"\n                        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>\n                        <TbBrain className=\"w-4 h-4 mr-2 drop-shadow-md\" />\n                        {quiz.category}\n                      </span>\n                    </div>\n                  )}\n                </div>\n\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Stats Section */}\n        <div className=\"p-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white relative\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-5\">\n            <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500 to-purple-600\"></div>\n          </div>\n\n          <div className=\"relative z-10\">\n            {/* Quick Stats Row */}\n            <div className=\"grid grid-cols-3 gap-3 mb-6\">\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-200/60 rounded-2xl p-4 text-center border border-blue-200/70 hover:border-blue-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbQuestionMark className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-blue-700 mb-1\">{quiz.questions?.length || 0}</div>\n                  <div className=\"text-xs text-blue-600 font-semibold uppercase tracking-wide\">Questions</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-emerald-50 via-emerald-100/80 to-emerald-200/60 rounded-2xl p-4 text-center border border-emerald-200/70 hover:border-emerald-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbClock className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-emerald-700 mb-1\">{quiz.duration || 30}</div>\n                  <div className=\"text-xs text-emerald-600 font-semibold uppercase tracking-wide\">Minutes</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                whileHover={{ scale: 1.05, y: -2 }}\n                className=\"bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-200/60 rounded-2xl p-4 text-center border border-purple-200/70 hover:border-purple-400/70 transition-all duration-300 hover:shadow-xl group relative overflow-hidden\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <TbStar className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-purple-700 mb-1\">{quiz.passingMarks || 70}</div>\n                  <div className=\"text-xs text-purple-600 font-semibold uppercase tracking-wide\">Pass %</div>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n\n          {/* Topic & Difficulty Display */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-3\">\n              {quiz.topic && (\n                <motion.span\n                  whileHover={{ scale: 1.05 }}\n                  className=\"inline-flex items-center px-3 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-xl border-2 border-white/30 backdrop-blur-sm\"\n                  style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n                >\n                  🎯 {quiz.topic}\n                </motion.span>\n              )}\n              {quiz.difficulty && (\n                <motion.span\n                  whileHover={{ scale: 1.05 }}\n                  className={`inline-flex items-center px-3 py-2 rounded-xl text-sm font-bold shadow-lg border-2 border-white/30 ${getDifficultyColor(quiz.difficulty)}`}\n                  style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n                >\n                  {quiz.difficulty}\n                </motion.span>\n              )}\n            </div>\n\n            {quiz.attempts > 0 && (\n              <div className=\"flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-xl border border-gray-200\">\n                <TbUsers className=\"w-4 h-4 text-gray-600\" />\n                <span className=\"text-sm font-semibold text-gray-700\">{quiz.attempts} attempts</span>\n              </div>\n            )}\n          </div>\n\n          {/* Enhanced Last Attempt Results Section */}\n          {userResult && (\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              className={`border-2 rounded-2xl p-5 mb-6 relative overflow-hidden ${\n                quizStatus.status === 'passed'\n                  ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100/50 border-green-200/70'\n                  : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100/50 border-red-200/70'\n              }`}\n            >\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className={`absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 ${\n                  quizStatus.status === 'passed' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <div className={`absolute bottom-0 left-0 w-16 h-16 rounded-full translate-y-8 -translate-x-8 ${\n                  quizStatus.status === 'passed' ? 'bg-emerald-500' : 'bg-pink-500'\n                }`}></div>\n              </div>\n\n              <div className=\"relative z-10\">\n                {/* Header with Subject and Status */}\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${\n                      quizStatus.status === 'passed'\n                        ? 'bg-gradient-to-br from-green-500 to-emerald-600'\n                        : 'bg-gradient-to-br from-red-500 to-pink-600'\n                    }`}>\n                      {quizStatus.status === 'passed' ? (\n                        <TbCheck className=\"w-5 h-5 text-white\" />\n                      ) : (\n                        <TbX className=\"w-5 h-5 text-white\" />\n                      )}\n                    </div>\n                    <div>\n                      <span className={`text-sm font-bold ${\n                        quizStatus.status === 'passed' ? 'text-green-900' : 'text-red-900'\n                      }`} style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.5)' }}>\n                        Last Attempt - {quiz.subject || 'Subject'}\n                      </span>\n                      <div className={`text-xs font-semibold ${\n                        quizStatus.status === 'passed' ? 'text-green-800' : 'text-red-800'\n                      }`} style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.5)' }}>\n                        {new Date(userResult.completedAt).toLocaleDateString()}\n                      </div>\n                    </div>\n                  </div>\n                  <div className={`text-3xl font-black drop-shadow-lg ${\n                    quizStatus.status === 'passed' ? 'text-green-900' : 'text-red-900'\n                  }`} style={{ textShadow: '2px 2px 4px rgba(255,255,255,0.5)' }}>\n                    {userResult.percentage}%\n                  </div>\n                </div>\n\n                {/* Detailed Results Grid */}\n                <div className=\"grid grid-cols-3 gap-3 mb-4\">\n                  <div className={`text-center p-3 rounded-xl ${\n                    quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'\n                  }`}>\n                    <div className={`text-lg font-bold ${\n                      quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {userResult.correctAnswers || 0}\n                    </div>\n                    <div className={`text-xs font-semibold ${\n                      quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      Correct\n                    </div>\n                  </div>\n                  <div className={`text-center p-3 rounded-xl ${\n                    quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'\n                  }`}>\n                    <div className={`text-lg font-bold ${\n                      quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {(userResult.totalQuestions || 0) - (userResult.correctAnswers || 0)}\n                    </div>\n                    <div className={`text-xs font-semibold ${\n                      quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      Wrong\n                    </div>\n                  </div>\n                  <div className={`text-center p-3 rounded-xl ${\n                    quizStatus.status === 'passed' ? 'bg-green-100/70' : 'bg-red-100/70'\n                  }`}>\n                    <div className={`text-lg font-bold ${\n                      quizStatus.status === 'passed' ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {userResult.xpEarned || 0}\n                    </div>\n                    <div className={`text-xs font-semibold ${\n                      quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      XP Gained\n                    </div>\n                  </div>\n                </div>\n\n                {/* Progress Bar */}\n                <div className={`w-full rounded-full h-3 mb-3 overflow-hidden ${\n                  quizStatus.status === 'passed' ? 'bg-green-200/50' : 'bg-red-200/50'\n                }`}>\n                  <motion.div\n                    initial={{ width: 0 }}\n                    animate={{ width: `${userResult.percentage}%` }}\n                    transition={{ duration: 1, ease: \"easeOut\" }}\n                    className={`h-full rounded-full shadow-sm ${\n                      quizStatus.status === 'passed'\n                        ? 'bg-gradient-to-r from-green-500 to-emerald-500'\n                        : 'bg-gradient-to-r from-red-500 to-pink-500'\n                    }`}\n                  />\n                </div>\n\n                {/* Status Badge */}\n                <div className=\"flex justify-center\">\n                  <span className={`px-4 py-2 rounded-full font-bold shadow-md text-white ${\n                    quizStatus.status === 'passed'\n                      ? 'bg-gradient-to-r from-green-600 to-emerald-600 border border-green-400'\n                      : 'bg-gradient-to-r from-red-600 to-pink-600 border border-red-400'\n                  }`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}>\n                    {quizStatus.status === 'passed' ? '✅ PASSED' : '❌ FAILED'}\n                  </span>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n\n        {/* Enhanced Action Buttons */}\n        <div className=\"px-6 pb-6 bg-gradient-to-br from-gray-50/80 to-white border-t border-gray-200/30 relative\">\n          {/* Background Glow */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n\n          <div className=\"relative z-10 pt-6\">\n            <div className=\"flex space-x-3\">\n              <motion.div className=\"flex-1\">\n                <Button\n                  variant=\"primary\"\n                  size=\"md\"\n                  className=\"w-full bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-800 border-0 shadow-xl hover:shadow-2xl transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 font-bold text-white relative overflow-hidden group\"\n                  onClick={onStart}\n                  icon={<TbPlayerPlay className=\"group-hover:scale-125 group-hover:rotate-12 transition-all duration-300\" />}\n                >\n                  <span className=\"relative z-10 flex items-center justify-center space-x-2\">\n                    <span>{showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}</span>\n                    <TbChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                  </span>\n\n                  {/* Animated Background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n\n                  {/* Shine Effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-700\"></div>\n                </Button>\n              </motion.div>\n\n              {showResults && onView && (\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  <Button\n                    variant=\"secondary\"\n                    size=\"md\"\n                    className=\"bg-white/90 backdrop-blur-sm border-2 border-indigo-200/70 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-400 hover:text-indigo-700 transform hover:scale-105 hover:-translate-y-2 transition-all duration-400 shadow-lg hover:shadow-xl font-semibold relative overflow-hidden group\"\n                    onClick={onView}\n                    icon={<TbTrophy className=\"group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 text-yellow-500\" />}\n                  >\n                    <span className=\"relative z-10\">Results</span>\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                  </Button>\n                </motion.div>\n              )}\n            </div>\n\n            {/* Quick Action Hint */}\n            <div className=\"mt-4 text-center\">\n              <span className=\"text-xs text-gray-700 font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gray-100 px-3 py-1 rounded-full\">\n                Click to start your learning journey\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Progress Section */}\n        {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (\n          <div className=\"px-6 pb-4 bg-gradient-to-br from-gray-50/50 to-white\">\n            <div className=\"flex items-center justify-between text-sm mb-3\">\n              <span className=\"font-semibold flex items-center space-x-2 text-gray-800\">\n                <TbTarget className=\"w-4 h-4 text-blue-600\" />\n                <span>Learning Progress</span>\n              </span>\n              <span className=\"font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded-lg\">{quiz.progress}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200/70 rounded-full h-3 overflow-hidden shadow-inner\">\n              <motion.div\n                initial={{ width: 0 }}\n                animate={{ width: `${quiz.progress}%` }}\n                transition={{ duration: 1, ease: \"easeOut\" }}\n                className=\"h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full shadow-sm relative\"\n              >\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/30 to-transparent rounded-full\"></div>\n              </motion.div>\n            </div>\n            <div className=\"mt-2 text-xs text-center\">\n              <span className=\"text-gray-700 font-medium bg-gray-100 px-3 py-1 rounded-full\">\n                Keep going! You're making great progress.\n              </span>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced Hover Effects */}\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl\"\n          whileHover={{ opacity: 1 }}\n        />\n\n        {/* Floating Action Indicator */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0 }}\n          whileHover={{ opacity: 1, scale: 1 }}\n          className=\"absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg pointer-events-none\"\n        >\n          <TbChevronRight className=\"w-4 h-4 text-white\" />\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {\n  return (\n    <div className={`quiz-grid-container ${className}`}>\n      {quizzes.map((quiz, index) => (\n        <motion.div\n          key={quiz._id || index}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}\n          className=\"h-full\"\n        >\n          <QuizCard\n            quiz={quiz}\n            onStart={() => onQuizStart(quiz)}\n            onView={onQuizView ? () => onQuizView(quiz) : undefined}\n            showResults={showResults}\n            userResult={userResults[quiz._id]}\n            className=\"h-full\"\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n};\n\nexport default QuizCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,GAAG,QAAQ,gBAAgB;AAClJ,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,IAAI;EACJC,OAAO;EACPC,MAAM;EACNC,WAAW,GAAG,KAAK;EACnBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACJ,MAAMC,kBAAkB,GAAIC,UAAU,IAAK;IACzC,QAAQA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC;MAC/B,KAAK,MAAM;QACT,OAAO,2DAA2D;MACpE,KAAK,QAAQ;QACX,OAAO,2DAA2D;MACpE,KAAK,MAAM;QACT,OAAO,sDAAsD;MAC/D;QACE,OAAO,wDAAwD;IACnE;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACR,UAAU,EAAE;MACf,OAAO;QACLS,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE,iBAAiB;QAC9BC,OAAO,EAAE,8CAA8C;QACvDC,WAAW,EAAE,4DAA4D;QACzEC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC;IACH;IAEA,MAAMC,MAAM,GAAGhB,UAAU,CAACiB,UAAU,KAAKrB,IAAI,CAACsB,YAAY,IAAI,EAAE,CAAC;IACjE,IAAIF,MAAM,EAAE;MACV,OAAO;QACLP,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,+BAA+B;QACtCC,WAAW,EAAE,kBAAkB;QAC/BC,OAAO,EAAE,gDAAgD;QACzDC,WAAW,EAAE,+DAA+D;QAC5EC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLN,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE,0BAA0B;QACjCC,WAAW,EAAE,gBAAgB;QAC7BC,OAAO,EAAE,2CAA2C;QACpDC,WAAW,EAAE,wDAAwD;QACrEC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC;IACH;EACF,CAAC;EAED,MAAMI,UAAU,GAAGX,aAAa,CAAC,CAAC;EAIlC,oBACEhB,OAAA,CAACf,MAAM,CAAC2C,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEF,CAAC,EAAE,CAAC,EAAE;MAAEG,KAAK,EAAE;IAAK,CAAE;IACpCC,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAU,CAAE;IAC/C5B,SAAS,EAAG,0BAAyBA,SAAU,EAAE;IAAA6B,QAAA,eAEjDtC,OAAA;MACES,SAAS,EAAG,0HAAyHkB,UAAU,CAACJ,MAAO,IAAGI,UAAU,CAACR,WAAY,IAAGQ,UAAU,CAACL,SAAU,EAAE;MAAA,GACvMZ,KAAK;MAAA4B,QAAA,gBAGTtC,OAAA,CAACf,MAAM,CAAC2C,GAAG;QACTC,OAAO,EAAE;UAAEK,KAAK,EAAE,CAAC;UAAEK,MAAM,EAAE,CAAC;QAAG,CAAE;QACnCP,OAAO,EAAE;UAAEE,KAAK,EAAE,CAAC;UAAEK,MAAM,EAAE;QAAE,CAAE;QACjCJ,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEI,KAAK,EAAE;QAAI,CAAE;QAC1C/B,SAAS,EAAC,6BAA6B;QAAA6B,QAAA,EAEtC9B,UAAU,gBACTR,OAAA;UAAKS,SAAS,EAAC,qBAAqB;UAAA6B,QAAA,gBAClCtC,OAAA;YAAKS,SAAS,EAAG,+FAA8FkB,UAAU,CAACT,KAAM,eAAcS,UAAU,CAACR,WAAY,EAAE;YAAAmB,QAAA,EACpKX,UAAU,CAACV,MAAM,KAAK,QAAQ,gBAC7BjB,OAAA,CAAAE,SAAA;cAAAoC,QAAA,gBACEtC,OAAA,CAACL,OAAO;gBAACc,SAAS,EAAC;cAAqB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAE7C;YAAA,eAAE,CAAC,gBAEH5C,OAAA,CAAAE,SAAA;cAAAoC,QAAA,gBACEtC,OAAA,CAACJ,GAAG;gBAACa,SAAS,EAAC;cAAqB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEzC;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN5C,OAAA;YAAKS,SAAS,EAAC,+EAA+E;YAAA6B,QAAA,GAC3F9B,UAAU,CAACiB,UAAU,EAAC,WAAI,EAACjB,UAAU,CAACqC,QAAQ,IAAI,CAAC,EAAC,KACvD;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN5C,OAAA;UAAKS,SAAS,EAAC,mJAAmJ;UAAA6B,QAAA,gBAChKtC,OAAA,CAACd,OAAO;YAACuB,SAAS,EAAC;UAAqB;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAEb5C,OAAA;QAAKS,SAAS,EAAC,0BAA0B;QAAA6B,QAAA,eACvCtC,OAAA;UAAKS,SAAS,EAAG,GAAEkB,UAAU,CAACN,WAAY,0BAA0B;UAAAiB,QAAA,gBAElEtC,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAA6B,QAAA,gBAC1CtC,OAAA;cAAKS,SAAS,EAAC;YAAqG;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3H5C,OAAA;cAAKS,SAAS,EAAC;YAAiH;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvI5C,OAAA;cAAKS,SAAS,EAAC;YAAmH;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI,CAAC,eAGN5C,OAAA;YAAKS,SAAS,EAAC,kBAAkB;YAAA6B,QAAA,gBAC/BtC,OAAA;cAAKS,SAAS,EAAC;YAA+E;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrG5C,OAAA;cAAKS,SAAS,EAAC;YAAiF;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvG5C,OAAA;cAAKS,SAAS,EAAC;YAAsF;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG,CAAC,eAGN5C,OAAA;YAAKS,SAAS,EAAC;UAA2G;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEjI5C,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAA6B,QAAA,eAC5BtC,OAAA;cAAKS,SAAS,EAAC,uCAAuC;cAAA6B,QAAA,eACpDtC,OAAA;gBAAKS,SAAS,EAAC,QAAQ;gBAAA6B,QAAA,gBACrBtC,OAAA;kBAAKS,SAAS,EAAC,kCAAkC;kBAAA6B,QAAA,gBAC/CtC,OAAA;oBAAKS,SAAS,EAAC,8KAA8K;oBAAA6B,QAAA,eAC3LtC,OAAA,CAACP,OAAO;sBAACgB,SAAS,EAAC;oBAAoB;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACN5C,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAKS,SAAS,EAAC,wCAAwC;sBAAA6B,QAAA,gBACrDtC,OAAA;wBAAMS,SAAS,EAAC,gIAAgI;wBAAA6B,QAAA,GAAC,QACzI,EAAClC,IAAI,CAAC0C,KAAK,IAAI,KAAK;sBAAA;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,EACNxC,IAAI,CAAC2C,OAAO,iBACX/C,OAAA;wBAAMS,SAAS,EAAC,gKAAgK;wBAAA6B,QAAA,GAAC,eAC5K,EAAClC,IAAI,CAAC2C,OAAO;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CACP,EACAxC,IAAI,CAAC4C,QAAQ,iBACZhD,OAAA;wBAAMS,SAAS,EAAC,6JAA6J;wBAAA6B,QAAA,GAAC,eACzK,EAAClC,IAAI,CAAC4C,QAAQ;sBAAA;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CACP,EACAxC,IAAI,CAAC6C,KAAK,iBACTjD,OAAA;wBAAMS,SAAS,EAAC,oJAAoJ;wBAAA6B,QAAA,GAAC,eAChK,EAAClC,IAAI,CAAC6C,KAAK;sBAAA;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN5C,OAAA;sBAAKS,SAAS,EAAC,kFAAkF;sBAAA6B,QAAA,gBAC/FtC,OAAA;wBAAMS,SAAS,EAAC,2FAA2F;wBAAA6B,QAAA,gBACzGtC,OAAA,CAACb,cAAc;0BAACsB,SAAS,EAAC;wBAAuB;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACpD5C,OAAA;0BAAMS,SAAS,EAAC,uBAAuB;0BAAA6B,QAAA,EAAE,EAAA3B,eAAA,GAAAP,IAAI,CAAC8C,SAAS,cAAAvC,eAAA,uBAAdA,eAAA,CAAgBwC,MAAM,KAAI;wBAAC;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxE,CAAC,eACP5C,OAAA;wBAAMS,SAAS,EAAC,2FAA2F;wBAAA6B,QAAA,gBACzGtC,OAAA,CAACd,OAAO;0BAACuB,SAAS,EAAC;wBAAuB;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7C5C,OAAA;0BAAMS,SAAS,EAAC,uBAAuB;0BAAA6B,QAAA,GAAElC,IAAI,CAACgC,QAAQ,IAAI,EAAE,EAAC,GAAC;wBAAA;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC,eACP5C,OAAA;wBAAMS,SAAS,EAAC,2FAA2F;wBAAA6B,QAAA,gBACzGtC,OAAA,CAACR,QAAQ;0BAACiB,SAAS,EAAC;wBAAuB;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9C5C,OAAA;0BAAMS,SAAS,EAAC,uBAAuB;0BAAA6B,QAAA,GAAElC,IAAI,CAACsB,YAAY,IAAI,EAAE,EAAC,GAAC;wBAAA;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,eAEP5C,OAAA;wBAAMS,SAAS,EAAC,+FAA+F;wBAAA6B,QAAA,gBAC7GtC,OAAA,CAACT,MAAM;0BAACkB,SAAS,EAAC;wBAAyB;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9C5C,OAAA;0BAAMS,SAAS,EAAC,yBAAyB;0BAAA6B,QAAA,GAAElC,IAAI,CAACgD,QAAQ,IAAI,GAAG,EAAC,KAAG;wBAAA;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5C,OAAA;kBAAIS,SAAS,EAAC,+EAA+E;kBAAC4C,KAAK,EAAE;oBAAEC,UAAU,EAAE;kBAA8B,CAAE;kBAAAhB,QAAA,EAChJlC,IAAI,CAACmD;gBAAI;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eAGL5C,OAAA;kBAAKS,SAAS,EAAC,2BAA2B;kBAAA6B,QAAA,GACvClC,IAAI,CAAC2C,OAAO,iBACX/C,OAAA;oBAAMS,SAAS,EAAC,4HAA4H;oBAAA6B,QAAA,GAAC,eACxI,EAAClC,IAAI,CAAC2C,OAAO;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CACP,EACAxC,IAAI,CAAC6C,KAAK,iBACTjD,OAAA;oBAAMS,SAAS,EAAC,kIAAkI;oBAAA6B,QAAA,GAAC,eAC9I,EAAClC,IAAI,CAAC6C,KAAK;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN5C,OAAA;kBAAGS,SAAS,EAAC,oJAAoJ;kBAAA6B,QAAA,EAC9JlC,IAAI,CAACoD,WAAW,IAAI;gBAA0E;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,EAGHxC,IAAI,CAAC4C,QAAQ,iBACZhD,OAAA;kBAAKS,SAAS,EAAC,MAAM;kBAAA6B,QAAA,eACnBtC,OAAA;oBAAMS,SAAS,EAAC,yLAAyL;oBACvM4C,KAAK,EAAE;sBAAEC,UAAU,EAAE;oBAA8B,CAAE;oBAAAhB,QAAA,gBACrDtC,OAAA,CAACP,OAAO;sBAACgB,SAAS,EAAC;oBAA6B;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClDxC,IAAI,CAAC4C,QAAQ;kBAAA;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAKS,SAAS,EAAC,8DAA8D;QAAA6B,QAAA,gBAE3EtC,OAAA;UAAKS,SAAS,EAAC,4BAA4B;UAAA6B,QAAA,eACzCtC,OAAA;YAAKS,SAAS,EAAC;UAAmF;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC,eAEN5C,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAA6B,QAAA,eAE5BtC,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAA6B,QAAA,gBAC1CtC,OAAA,CAACf,MAAM,CAAC2C,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCtB,SAAS,EAAC,yNAAyN;cAAA6B,QAAA,gBAEnOtC,OAAA;gBAAKS,SAAS,EAAC;cAAqI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3J5C,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAA6B,QAAA,gBAC5BtC,OAAA;kBAAKS,SAAS,EAAC,kLAAkL;kBAAA6B,QAAA,eAC/LtC,OAAA,CAACb,cAAc;oBAACsB,SAAS,EAAC;kBAAoB;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAC,uCAAuC;kBAAA6B,QAAA,EAAE,EAAA1B,gBAAA,GAAAR,IAAI,CAAC8C,SAAS,cAAAtC,gBAAA,uBAAdA,gBAAA,CAAgBuC,MAAM,KAAI;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1F5C,OAAA;kBAAKS,SAAS,EAAC,6DAA6D;kBAAA6B,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEb5C,OAAA,CAACf,MAAM,CAAC2C,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCtB,SAAS,EAAC,wOAAwO;cAAA6B,QAAA,gBAElPtC,OAAA;gBAAKS,SAAS,EAAC;cAA2I;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjK5C,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAA6B,QAAA,gBAC5BtC,OAAA;kBAAKS,SAAS,EAAC,wLAAwL;kBAAA6B,QAAA,eACrMtC,OAAA,CAACd,OAAO;oBAACuB,SAAS,EAAC;kBAAoB;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAC,0CAA0C;kBAAA6B,QAAA,EAAElC,IAAI,CAACgC,QAAQ,IAAI;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrF5C,OAAA;kBAAKS,SAAS,EAAC,gEAAgE;kBAAA6B,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEb5C,OAAA,CAACf,MAAM,CAAC2C,GAAG;cACTK,UAAU,EAAE;gBAAEC,KAAK,EAAE,IAAI;gBAAEH,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCtB,SAAS,EAAC,mOAAmO;cAAA6B,QAAA,gBAE7OtC,OAAA;gBAAKS,SAAS,EAAC;cAAyI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/J5C,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAA6B,QAAA,gBAC5BtC,OAAA;kBAAKS,SAAS,EAAC,sLAAsL;kBAAA6B,QAAA,eACnMtC,OAAA,CAACT,MAAM;oBAACkB,SAAS,EAAC;kBAAoB;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAC,yCAAyC;kBAAA6B,QAAA,EAAElC,IAAI,CAACsB,YAAY,IAAI;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxF5C,OAAA;kBAAKS,SAAS,EAAC,+DAA+D;kBAAA6B,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5C,OAAA;UAAKS,SAAS,EAAC,wCAAwC;UAAA6B,QAAA,gBACrDtC,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAA6B,QAAA,GACzClC,IAAI,CAAC6C,KAAK,iBACTjD,OAAA,CAACf,MAAM,CAACwE,IAAI;cACVxB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BzB,SAAS,EAAC,+KAA+K;cACzL4C,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAA8B,CAAE;cAAAhB,QAAA,GACtD,eACI,EAAClC,IAAI,CAAC6C,KAAK;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACd,EACAxC,IAAI,CAACU,UAAU,iBACdd,OAAA,CAACf,MAAM,CAACwE,IAAI;cACVxB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BzB,SAAS,EAAG,sGAAqGI,kBAAkB,CAACT,IAAI,CAACU,UAAU,CAAE,EAAE;cACvJuC,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAA8B,CAAE;cAAAhB,QAAA,EAEpDlC,IAAI,CAACU;YAAU;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELxC,IAAI,CAACsD,QAAQ,GAAG,CAAC,iBAChB1D,OAAA;YAAKS,SAAS,EAAC,qFAAqF;YAAA6B,QAAA,gBAClGtC,OAAA,CAACZ,OAAO;cAACqB,SAAS,EAAC;YAAuB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C5C,OAAA;cAAMS,SAAS,EAAC,qCAAqC;cAAA6B,QAAA,GAAElC,IAAI,CAACsD,QAAQ,EAAC,WAAS;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLpC,UAAU,iBACTR,OAAA,CAACf,MAAM,CAAC2C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BtB,SAAS,EAAG,0DACVkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAC1B,oFAAoF,GACpF,2EACL,EAAE;UAAAqB,QAAA,gBAGHtC,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAA6B,QAAA,gBAC1CtC,OAAA;cAAKS,SAAS,EAAG,gFACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,cAAc,GAAG,YACnD;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACV5C,OAAA;cAAKS,SAAS,EAAG,gFACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,aACrD;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN5C,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAA6B,QAAA,gBAE5BtC,OAAA;cAAKS,SAAS,EAAC,wCAAwC;cAAA6B,QAAA,gBACrDtC,OAAA;gBAAKS,SAAS,EAAC,6BAA6B;gBAAA6B,QAAA,gBAC1CtC,OAAA;kBAAKS,SAAS,EAAG,mEACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAC1B,iDAAiD,GACjD,4CACL,EAAE;kBAAAqB,QAAA,EACAX,UAAU,CAACV,MAAM,KAAK,QAAQ,gBAC7BjB,OAAA,CAACL,OAAO;oBAACc,SAAS,EAAC;kBAAoB;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE1C5C,OAAA,CAACJ,GAAG;oBAACa,SAAS,EAAC;kBAAoB;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACtC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAMS,SAAS,EAAG,qBAChBkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;oBAACoC,KAAK,EAAE;sBAAEC,UAAU,EAAE;oBAAoC,CAAE;oBAAAhB,QAAA,GAAC,iBAC/C,EAAClC,IAAI,CAAC2C,OAAO,IAAI,SAAS;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACP5C,OAAA;oBAAKS,SAAS,EAAG,yBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;oBAACoC,KAAK,EAAE;sBAAEC,UAAU,EAAE;oBAAoC,CAAE;oBAAAhB,QAAA,EAC5D,IAAIqB,IAAI,CAACnD,UAAU,CAACoD,WAAW,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKS,SAAS,EAAG,sCACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;gBAACoC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAoC,CAAE;gBAAAhB,QAAA,GAC5D9B,UAAU,CAACiB,UAAU,EAAC,GACzB;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5C,OAAA;cAAKS,SAAS,EAAC,6BAA6B;cAAA6B,QAAA,gBAC1CtC,OAAA;gBAAKS,SAAS,EAAG,8BACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GAAG,eACtD,EAAE;gBAAAqB,QAAA,gBACDtC,OAAA;kBAAKS,SAAS,EAAG,qBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EACA9B,UAAU,CAACsD,cAAc,IAAI;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAG,yBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EAAC;gBAEJ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKS,SAAS,EAAG,8BACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GAAG,eACtD,EAAE;gBAAAqB,QAAA,gBACDtC,OAAA;kBAAKS,SAAS,EAAG,qBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EACA,CAAC9B,UAAU,CAACuD,cAAc,IAAI,CAAC,KAAKvD,UAAU,CAACsD,cAAc,IAAI,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAG,yBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EAAC;gBAEJ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKS,SAAS,EAAG,8BACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GAAG,eACtD,EAAE;gBAAAqB,QAAA,gBACDtC,OAAA;kBAAKS,SAAS,EAAG,qBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EACA9B,UAAU,CAACqC,QAAQ,IAAI;gBAAC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN5C,OAAA;kBAAKS,SAAS,EAAG,yBACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cACrD,EAAE;kBAAAqB,QAAA,EAAC;gBAEJ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5C,OAAA;cAAKS,SAAS,EAAG,gDACfkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GAAG,eACtD,EAAE;cAAAqB,QAAA,eACDtC,OAAA,CAACf,MAAM,CAAC2C,GAAG;gBACTC,OAAO,EAAE;kBAAEmC,KAAK,EAAE;gBAAE,CAAE;gBACtBhC,OAAO,EAAE;kBAAEgC,KAAK,EAAG,GAAExD,UAAU,CAACiB,UAAW;gBAAG,CAAE;gBAChDU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAU,CAAE;gBAC7C5B,SAAS,EAAG,iCACVkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAC1B,gDAAgD,GAChD,2CACL;cAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5C,OAAA;cAAKS,SAAS,EAAC,qBAAqB;cAAA6B,QAAA,eAClCtC,OAAA;gBAAMS,SAAS,EAAG,yDAChBkB,UAAU,CAACV,MAAM,KAAK,QAAQ,GAC1B,wEAAwE,GACxE,iEACL,EAAE;gBAACoC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAA8B,CAAE;gBAAAhB,QAAA,EACtDX,UAAU,CAACV,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAG;cAAU;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5C,OAAA;QAAKS,SAAS,EAAC,2FAA2F;QAAA6B,QAAA,gBAExGtC,OAAA;UAAKS,SAAS,EAAC;QAAoI;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAE1J5C,OAAA;UAAKS,SAAS,EAAC,oBAAoB;UAAA6B,QAAA,gBACjCtC,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAA6B,QAAA,gBAC7BtC,OAAA,CAACf,MAAM,CAAC2C,GAAG;cAACnB,SAAS,EAAC,QAAQ;cAAA6B,QAAA,eAC5BtC,OAAA,CAACF,MAAM;gBACLmE,OAAO,EAAC,SAAS;gBACjBC,IAAI,EAAC,IAAI;gBACTzD,SAAS,EAAC,oSAAoS;gBAC9S0D,OAAO,EAAE9D,OAAQ;gBACjB+D,IAAI,eAAEpE,OAAA,CAACV,YAAY;kBAACmB,SAAS,EAAC;gBAAyE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAN,QAAA,gBAE3GtC,OAAA;kBAAMS,SAAS,EAAC,0DAA0D;kBAAA6B,QAAA,gBACxEtC,OAAA;oBAAAsC,QAAA,EAAO/B,WAAW,IAAIC,UAAU,GAAG,aAAa,GAAG;kBAAY;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvE5C,OAAA,CAACN,cAAc;oBAACe,SAAS,EAAC;kBAAqE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,eAGP5C,OAAA;kBAAKS,SAAS,EAAC;gBAA+I;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAGrK5C,OAAA;kBAAKS,SAAS,EAAC;gBAAgL;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEZrC,WAAW,IAAID,MAAM,iBACpBN,OAAA,CAACf,MAAM,CAAC2C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEuC,CAAC,EAAE;cAAG,CAAE;cAC/BrC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEuC,CAAC,EAAE;cAAE,CAAE;cAC9BlC,UAAU,EAAE;gBAAEK,KAAK,EAAE;cAAI,CAAE;cAAAF,QAAA,eAE3BtC,OAAA,CAACF,MAAM;gBACLmE,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,IAAI;gBACTzD,SAAS,EAAC,+RAA+R;gBACzS0D,OAAO,EAAE7D,MAAO;gBAChB8D,IAAI,eAAEpE,OAAA,CAACX,QAAQ;kBAACoB,SAAS,EAAC;gBAAyF;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAN,QAAA,gBAEvHtC,OAAA;kBAAMS,SAAS,EAAC,eAAe;kBAAA6B,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9C5C,OAAA;kBAAKS,SAAS,EAAC;gBAAyI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5C,OAAA;YAAKS,SAAS,EAAC,kBAAkB;YAAA6B,QAAA,eAC/BtC,OAAA;cAAMS,SAAS,EAAC,0IAA0I;cAAA6B,QAAA,EAAC;YAE3J;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxC,IAAI,CAACkE,QAAQ,IAAIlE,IAAI,CAACkE,QAAQ,GAAG,CAAC,IAAIlE,IAAI,CAACkE,QAAQ,GAAG,GAAG,iBACxDtE,OAAA;QAAKS,SAAS,EAAC,sDAAsD;QAAA6B,QAAA,gBACnEtC,OAAA;UAAKS,SAAS,EAAC,gDAAgD;UAAA6B,QAAA,gBAC7DtC,OAAA;YAAMS,SAAS,EAAC,yDAAyD;YAAA6B,QAAA,gBACvEtC,OAAA,CAACR,QAAQ;cAACiB,SAAS,EAAC;YAAuB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5C,OAAA;cAAAsC,QAAA,EAAM;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACP5C,OAAA;YAAMS,SAAS,EAAC,0DAA0D;YAAA6B,QAAA,GAAElC,IAAI,CAACkE,QAAQ,EAAC,GAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eACN5C,OAAA;UAAKS,SAAS,EAAC,qEAAqE;UAAA6B,QAAA,eAClFtC,OAAA,CAACf,MAAM,CAAC2C,GAAG;YACTC,OAAO,EAAE;cAAEmC,KAAK,EAAE;YAAE,CAAE;YACtBhC,OAAO,EAAE;cAAEgC,KAAK,EAAG,GAAE5D,IAAI,CAACkE,QAAS;YAAG,CAAE;YACxCnC,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAU,CAAE;YAC7C5B,SAAS,EAAC,oGAAoG;YAAA6B,QAAA,eAE9GtC,OAAA;cAAKS,SAAS,EAAC;YAA6E;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN5C,OAAA;UAAKS,SAAS,EAAC,0BAA0B;UAAA6B,QAAA,eACvCtC,OAAA;YAAMS,SAAS,EAAC,8DAA8D;YAAA6B,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5C,OAAA,CAACf,MAAM,CAAC2C,GAAG;QACTnB,SAAS,EAAC,uLAAuL;QACjMwB,UAAU,EAAE;UAAEH,OAAO,EAAE;QAAE;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGF5C,OAAA,CAACf,MAAM,CAAC2C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEI,KAAK,EAAE;QAAE,CAAE;QAClCD,UAAU,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEI,KAAK,EAAE;QAAE,CAAE;QACrCzB,SAAS,EAAC,0JAA0J;QAAA6B,QAAA,eAEpKtC,OAAA,CAACN,cAAc;UAACe,SAAS,EAAC;QAAoB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC2B,EAAA,GAzhBIpE,QAAQ;AA2hBd,OAAO,MAAMqE,QAAQ,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW;EAAEC,UAAU;EAAEpE,WAAW,GAAG,KAAK;EAAEqE,WAAW,GAAG,CAAC,CAAC;EAAEnE,SAAS,GAAG;AAAG,CAAC,KAAK;EACvH,oBACET,OAAA;IAAKS,SAAS,EAAG,uBAAsBA,SAAU,EAAE;IAAA6B,QAAA,EAChDmC,OAAO,CAACI,GAAG,CAAC,CAACzE,IAAI,EAAE0E,KAAK,kBACvB9E,OAAA,CAACf,MAAM,CAAC2C,GAAG;MAETC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BI,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEI,KAAK,EAAEuC,IAAI,CAACC,GAAG,CAACF,KAAK,GAAG,GAAG,EAAE,GAAG;MAAE,CAAE;MACjErE,SAAS,EAAC,QAAQ;MAAA6B,QAAA,eAElBtC,OAAA,CAACG,QAAQ;QACPC,IAAI,EAAEA,IAAK;QACXC,OAAO,EAAEA,CAAA,KAAMqE,WAAW,CAACtE,IAAI,CAAE;QACjCE,MAAM,EAAEqE,UAAU,GAAG,MAAMA,UAAU,CAACvE,IAAI,CAAC,GAAG6E,SAAU;QACxD1E,WAAW,EAAEA,WAAY;QACzBC,UAAU,EAAEoE,WAAW,CAACxE,IAAI,CAAC8E,GAAG,CAAE;QAClCzE,SAAS,EAAC;MAAQ;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC,GAbGxC,IAAI,CAAC8E,GAAG,IAAIJ,KAAK;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcZ,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACuC,GAAA,GAvBWX,QAAQ;AAyBrB,eAAerE,QAAQ;AAAC,IAAAoE,EAAA,EAAAY,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}