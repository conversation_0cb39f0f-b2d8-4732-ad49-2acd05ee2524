{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const inspiringQuotes = [\"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\", \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\", \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\"];\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  const navigationItems = [{\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help from AI',\n    icon: FaRobot,\n    path: '/user/chat',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: 'Reports',\n    description: 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-orange-500 to-orange-600',\n    hoverColor: 'from-orange-600 to-orange-700'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Profile',\n    description: 'Manage your account',\n    icon: FaUser,\n    path: '/user/profile',\n    color: 'from-indigo-500 to-indigo-600',\n    hoverColor: 'from-indigo-600 to-indigo-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/user/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade your learning',\n    icon: FaCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600',\n    hoverColor: 'from-emerald-600 to-emerald-700'\n  }, {\n    title: 'About Us',\n    description: 'Learn about our mission',\n    icon: FaInfoCircle,\n    path: '/user/about-us',\n    color: 'from-cyan-500 to-cyan-600',\n    hoverColor: 'from-cyan-600 to-cyan-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-welcome relative\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"relative inline-block\",\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 1,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.span, {\n              className: \"relative inline-block mr-3\",\n              initial: {\n                opacity: 0,\n                rotateY: -90\n              },\n              animate: {\n                opacity: 1,\n                rotateY: 0,\n                textShadow: [\"0 0 10px rgba(59, 130, 246, 0.6)\", \"0 0 25px rgba(59, 130, 246, 0.9)\", \"0 0 10px rgba(59, 130, 246, 0.6)\"]\n              },\n              transition: {\n                duration: 1.2,\n                delay: 0.4,\n                textShadow: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              },\n              whileHover: {\n                scale: 1.1,\n                rotate: [0, -3, 3, 0],\n                transition: {\n                  duration: 0.4\n                }\n              },\n              style: {\n                background: 'linear-gradient(45deg, #1e40af, #3b82f6, #60a5fa)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                backgroundClip: 'text',\n                fontWeight: '900',\n                fontSize: 'inherit',\n                textShadow: '0 0 15px rgba(59, 130, 246, 0.6)'\n              },\n              children: [\"Study\", /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -top-2 -right-2 text-blue-500\",\n                animate: {\n                  rotate: [0, 10, -10, 0],\n                  scale: [1, 1.2, 1],\n                  opacity: [0.7, 1, 0.7]\n                },\n                transition: {\n                  duration: 2.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                style: {\n                  fontSize: '0.8em'\n                },\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n              className: \"relative inline-block\",\n              initial: {\n                opacity: 0,\n                rotateY: 90\n              },\n              animate: {\n                opacity: 1,\n                rotateY: 0,\n                textShadow: [\"0 0 10px rgba(16, 185, 129, 0.6)\", \"0 0 25px rgba(16, 185, 129, 0.9)\", \"0 0 10px rgba(16, 185, 129, 0.6)\"]\n              },\n              transition: {\n                duration: 1.2,\n                delay: 0.7,\n                textShadow: {\n                  duration: 2.8,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              },\n              whileHover: {\n                scale: 1.1,\n                rotate: [0, 3, -3, 0],\n                transition: {\n                  duration: 0.4\n                }\n              },\n              style: {\n                background: 'linear-gradient(45deg, #059669, #10b981, #34d399)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                backgroundClip: 'text',\n                fontWeight: '900',\n                fontSize: 'inherit',\n                textShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n              },\n              children: [\"Smarter\", /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -top-2 -right-2 text-green-500\",\n                animate: {\n                  scale: [1, 1.3, 1],\n                  rotate: [0, 15, -15, 0],\n                  opacity: [0.7, 1, 0.7]\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                style: {\n                  fontSize: '0.8em'\n                },\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n              className: \"ml-3\",\n              initial: {\n                opacity: 0,\n                scale: 0.5\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 1,\n                delay: 1\n              },\n              whileHover: {\n                scale: 1.15,\n                rotate: [0, 5, -5, 0],\n                transition: {\n                  duration: 0.3\n                }\n              },\n              style: {\n                fontWeight: '800',\n                color: '#1f2937',\n                textShadow: '0 0 10px rgba(31, 41, 55, 0.3)'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.name, \"!\", /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -top-1 -right-1\",\n                animate: {\n                  rotate: [0, 360],\n                  scale: [0.8, 1.2, 0.8],\n                  opacity: [0.6, 1, 0.6]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                style: {\n                  fontSize: '0.6em'\n                },\n                children: \"\\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute -bottom-2 left-0 h-1 rounded-full\",\n            initial: {\n              width: 0,\n              opacity: 0\n            },\n            animate: {\n              width: '100%',\n              opacity: 1,\n              background: ['linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)', 'linear-gradient(90deg, #10b981, #f59e0b, #3b82f6)', 'linear-gradient(90deg, #f59e0b, #3b82f6, #10b981)', 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)']\n            },\n            transition: {\n              duration: 1.5,\n              delay: 1.5,\n              background: {\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }\n            },\n            style: {\n              background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)',\n              boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: \"Ready to shine today? \\u2728 Choose your learning path below.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"yUKD1BuiW8zY+bONKPa0/Mswuw0=\", false, function () {\n  return [useNavigate, useSelector];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "currentQuote", "setCurrentQuote", "handleLogout", "localStorage", "removeItem", "success", "inspiringQuotes", "interval", "setInterval", "prev", "length", "clearInterval", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "span", "rotateY", "textShadow", "repeat", "Infinity", "ease", "whileHover", "scale", "rotate", "style", "background", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "fontWeight", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "width", "boxShadow", "marginRight", "marginLeft", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - <PERSON>\",\n    \"The future belongs to those who believe in the beauty of their dreams. - <PERSON>\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help from AI',\n      icon: FaRobot,\n      path: '/user/chat',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-orange-500 to-orange-600',\n      hoverColor: 'from-orange-600 to-orange-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/user/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/user/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade your learning',\n      icon: FaCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600',\n      hoverColor: 'from-emerald-600 to-emerald-700'\n    },\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Amazing Animated Study Smarter */}\n          <div className=\"hub-welcome relative\">\n            <motion.div\n              className=\"relative inline-block\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.2 }}\n            >\n              {/* Study - with book/learning animation */}\n              <motion.span\n                className=\"relative inline-block mr-3\"\n                initial={{ opacity: 0, rotateY: -90 }}\n                animate={{\n                  opacity: 1,\n                  rotateY: 0,\n                  textShadow: [\n                    \"0 0 10px rgba(59, 130, 246, 0.6)\",\n                    \"0 0 25px rgba(59, 130, 246, 0.9)\",\n                    \"0 0 10px rgba(59, 130, 246, 0.6)\"\n                  ]\n                }}\n                transition={{\n                  duration: 1.2,\n                  delay: 0.4,\n                  textShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                }}\n                whileHover={{\n                  scale: 1.1,\n                  rotate: [0, -3, 3, 0],\n                  transition: { duration: 0.4 }\n                }}\n                style={{\n                  background: 'linear-gradient(45deg, #1e40af, #3b82f6, #60a5fa)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  fontWeight: '900',\n                  fontSize: 'inherit',\n                  textShadow: '0 0 15px rgba(59, 130, 246, 0.6)'\n                }}\n              >\n                Study\n\n                {/* Book icon animation */}\n                <motion.div\n                  className=\"absolute -top-2 -right-2 text-blue-500\"\n                  animate={{\n                    rotate: [0, 10, -10, 0],\n                    scale: [1, 1.2, 1],\n                    opacity: [0.7, 1, 0.7]\n                  }}\n                  transition={{\n                    duration: 2.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  style={{ fontSize: '0.8em' }}\n                >\n                  📚\n                </motion.div>\n              </motion.span>\n\n              {/* Smarter - with brain/intelligence animation */}\n              <motion.span\n                className=\"relative inline-block\"\n                initial={{ opacity: 0, rotateY: 90 }}\n                animate={{\n                  opacity: 1,\n                  rotateY: 0,\n                  textShadow: [\n                    \"0 0 10px rgba(16, 185, 129, 0.6)\",\n                    \"0 0 25px rgba(16, 185, 129, 0.9)\",\n                    \"0 0 10px rgba(16, 185, 129, 0.6)\"\n                  ]\n                }}\n                transition={{\n                  duration: 1.2,\n                  delay: 0.7,\n                  textShadow: {\n                    duration: 2.8,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                }}\n                whileHover={{\n                  scale: 1.1,\n                  rotate: [0, 3, -3, 0],\n                  transition: { duration: 0.4 }\n                }}\n                style={{\n                  background: 'linear-gradient(45deg, #059669, #10b981, #34d399)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  fontWeight: '900',\n                  fontSize: 'inherit',\n                  textShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                }}\n              >\n                Smarter\n\n                {/* Brain icon animation */}\n                <motion.div\n                  className=\"absolute -top-2 -right-2 text-green-500\"\n                  animate={{\n                    scale: [1, 1.3, 1],\n                    rotate: [0, 15, -15, 0],\n                    opacity: [0.7, 1, 0.7]\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 0.5\n                  }}\n                  style={{ fontSize: '0.8em' }}\n                >\n                  🧠\n                </motion.div>\n              </motion.span>\n\n              {/* User name with better visibility */}\n              <motion.span\n                className=\"ml-3\"\n                initial={{ opacity: 0, scale: 0.5 }}\n                animate={{\n                  opacity: 1,\n                  scale: 1\n                }}\n                transition={{\n                  duration: 1,\n                  delay: 1\n                }}\n                whileHover={{\n                  scale: 1.15,\n                  rotate: [0, 5, -5, 0],\n                  transition: { duration: 0.3 }\n                }}\n                style={{\n                  fontWeight: '800',\n                  color: '#1f2937',\n                  textShadow: '0 0 10px rgba(31, 41, 55, 0.3)'\n                }}\n              >\n                {user?.name}!\n\n                {/* Celebration sparkles */}\n                <motion.div\n                  className=\"absolute -top-1 -right-1\"\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [0.8, 1.2, 0.8],\n                    opacity: [0.6, 1, 0.6]\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  style={{ fontSize: '0.6em' }}\n                >\n                  ✨\n                </motion.div>\n              </motion.span>\n            </motion.div>\n\n            {/* Magical underline effect */}\n            <motion.div\n              className=\"absolute -bottom-2 left-0 h-1 rounded-full\"\n              initial={{ width: 0, opacity: 0 }}\n              animate={{\n                width: '100%',\n                opacity: 1,\n                background: [\n                  'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)',\n                  'linear-gradient(90deg, #10b981, #f59e0b, #3b82f6)',\n                  'linear-gradient(90deg, #f59e0b, #3b82f6, #10b981)',\n                  'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)'\n                ]\n              }}\n              transition={{\n                duration: 1.5,\n                delay: 1.5,\n                background: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n              style={{\n                background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b)',\n                boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'\n              }}\n            />\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                >\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA1B,OAAO,CAAC2B,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAR,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMS,eAAe,GAAG,CACtB,+FAA+F,EAC/F,4FAA4F,EAC5F,4GAA4G,EAC5G,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,CAC5B;EAEDhC,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCP,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEnC,gBAAgB;IACtBoC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAElC,MAAM;IACZmC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAExB,OAAO;IACbyB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEjC,WAAW;IACjBkC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE3B,QAAQ;IACd4B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEhC,MAAM;IACZiC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE/B,UAAU;IAChBgC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE9B,YAAY;IAClB+B,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,iCAAiC;IACxCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAE7B,YAAY;IAClB8B,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1B,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAG1B1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAKtB1B,OAAA;UAAKyB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;YACTF,SAAS,EAAC,uBAAuB;YACjCG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAR,QAAA,gBAGxC1B,OAAA,CAACjB,MAAM,CAACoD,IAAI;cACVV,SAAS,EAAC,4BAA4B;cACtCG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,OAAO,EAAE,CAAC;cAAG,CAAE;cACtCL,OAAO,EAAE;gBACPF,OAAO,EAAE,CAAC;gBACVO,OAAO,EAAE,CAAC;gBACVC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;cAEtC,CAAE;cACFL,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbC,KAAK,EAAE,GAAG;gBACVG,UAAU,EAAE;kBACVJ,QAAQ,EAAE,CAAC;kBACXK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cACF,CAAE;cACFC,UAAU,EAAE;gBACVC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACrBX,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI;cAC9B,CAAE;cACFW,KAAK,EAAE;gBACLC,UAAU,EAAE,mDAAmD;gBAC/DC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,cAAc,EAAE,MAAM;gBACtBC,UAAU,EAAE,KAAK;gBACjBC,QAAQ,EAAE,SAAS;gBACnBb,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,GACH,OAGC,eACA1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,wCAAwC;gBAClDM,OAAO,EAAE;kBACPY,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACvBD,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBb,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,GAAG;kBACbK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFI,KAAK,EAAE;kBAAEM,QAAQ,EAAE;gBAAQ,CAAE;gBAAAxB,QAAA,EAC9B;cAED;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGdtD,OAAA,CAACjB,MAAM,CAACoD,IAAI;cACVV,SAAS,EAAC,uBAAuB;cACjCG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,OAAO,EAAE;cAAG,CAAE;cACrCL,OAAO,EAAE;gBACPF,OAAO,EAAE,CAAC;gBACVO,OAAO,EAAE,CAAC;gBACVC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;cAEtC,CAAE;cACFL,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbC,KAAK,EAAE,GAAG;gBACVG,UAAU,EAAE;kBACVJ,QAAQ,EAAE,GAAG;kBACbK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cACF,CAAE;cACFC,UAAU,EAAE;gBACVC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrBX,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI;cAC9B,CAAE;cACFW,KAAK,EAAE;gBACLC,UAAU,EAAE,mDAAmD;gBAC/DC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,cAAc,EAAE,MAAM;gBACtBC,UAAU,EAAE,KAAK;gBACjBC,QAAQ,EAAE,SAAS;gBACnBb,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,GACH,SAGC,eACA1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,yCAAyC;gBACnDM,OAAO,EAAE;kBACPW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACvBd,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBN,KAAK,EAAE;gBACT,CAAE;gBACFU,KAAK,EAAE;kBAAEM,QAAQ,EAAE;gBAAQ,CAAE;gBAAAxB,QAAA,EAC9B;cAED;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGdtD,OAAA,CAACjB,MAAM,CAACoD,IAAI;cACVV,SAAS,EAAC,MAAM;cAChBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCX,OAAO,EAAE;gBACPF,OAAO,EAAE,CAAC;gBACVa,KAAK,EAAE;cACT,CAAE;cACFV,UAAU,EAAE;gBACVC,QAAQ,EAAE,CAAC;gBACXC,KAAK,EAAE;cACT,CAAE;cACFO,UAAU,EAAE;gBACVC,KAAK,EAAE,IAAI;gBACXC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrBX,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI;cAC9B,CAAE;cACFW,KAAK,EAAE;gBACLK,UAAU,EAAE,KAAK;gBACjB1B,KAAK,EAAE,SAAS;gBAChBc,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,GAEDtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI,EAAC,GAEZ,eACAvD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,0BAA0B;gBACpCM,OAAO,EAAE;kBACPY,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;kBAChBD,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;kBACtBb,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFI,KAAK,EAAE;kBAAEM,QAAQ,EAAE;gBAAQ,CAAE;gBAAAxB,QAAA,EAC9B;cAED;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGbtD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;YACTF,SAAS,EAAC,4CAA4C;YACtDG,OAAO,EAAE;cAAE4B,KAAK,EAAE,CAAC;cAAE3B,OAAO,EAAE;YAAE,CAAE;YAClCE,OAAO,EAAE;cACPyB,KAAK,EAAE,MAAM;cACb3B,OAAO,EAAE,CAAC;cACVgB,UAAU,EAAE,CACV,mDAAmD,EACnD,mDAAmD,EACnD,mDAAmD,EACnD,mDAAmD;YAEvD,CAAE;YACFb,UAAU,EAAE;cACVC,QAAQ,EAAE,GAAG;cACbC,KAAK,EAAE,GAAG;cACVW,UAAU,EAAE;gBACVZ,QAAQ,EAAE,CAAC;gBACXK,MAAM,EAAEC,QAAQ;gBAChBC,IAAI,EAAE;cACR;YACF,CAAE;YACFI,KAAK,EAAE;cACLC,UAAU,EAAE,mDAAmD;cAC/DY,SAAS,EAAE;YACb;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtD,OAAA;UAAGyB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJtD,OAAA;UAAKyB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1B,OAAA,CAACL,MAAM;YAACiD,KAAK,EAAE;cAAErB,KAAK,EAAE,SAAS;cAAEmC,WAAW,EAAE;YAAS;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC1C,eAAe,CAACN,YAAY,CAAC,EAAC,IAChC,eAAAN,OAAA,CAACL,MAAM;YAACiD,KAAK,EAAE;cAAErB,KAAK,EAAE,SAAS;cAAEoC,UAAU,EAAE;YAAS;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DtD,OAAA;YAAK4C,KAAK,EAAE;cAAEM,QAAQ,EAAE,UAAU;cAAE3B,KAAK,EAAE,SAAS;cAAEqC,SAAS,EAAE;YAAS,CAAE;YAAAlC,QAAA,EAAC;UAE7E;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbtD,OAAA;QAAKyB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC1B,OAAA;UAAKyB,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAAC2C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACzC,IAAI;YAC/B,oBACErB,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE6B,KAAK,GAAG;cAAI,CAAE;cAClDtC,SAAS,EAAG,kBAAiBqC,IAAI,CAACtC,UAAW,IAAGsC,IAAI,CAACvC,KAAM,EAAE;cAC7D0C,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC2D,IAAI,CAACxC,IAAI,CAAE;cACnC4C,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtCnE,QAAQ,CAAC2D,IAAI,CAACxC,IAAI,CAAC;gBACrB;cACF,CAAE;cAAAI,QAAA,gBAEF1B,OAAA;gBAAKyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1B,OAAA,CAACgE,aAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENtD,OAAA;gBAAIyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BoC,IAAI,CAAC3C;cAAK;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELtD,OAAA;gBAAGyB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCoC,IAAI,CAAC1C;cAAW;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GAxBCQ,IAAI,CAAC3C,KAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CT,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjC1B,OAAA;YAAKyB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1B,OAAA,CAACP,eAAe;cAACgC,SAAS,EAAC;YAAuC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEtD,OAAA;cAAA0B,QAAA,EAAM;YAAkC;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CtD,OAAA,CAACJ,QAAQ;cAAC6B,SAAS,EAAC;YAAuC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CApYID,GAAG;EAAA,QACUpB,WAAW,EACXC,WAAW;AAAA;AAAAyF,EAAA,GAFxBtE,GAAG;AAsYT,eAAeA,GAAG;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}