{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport AdminNavigation from \"./AdminNavigation\";\nimport { TbHome } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      getUserData();\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: [(user === null || user === void 0 ? void 0 : user.isAdmin) && /*#__PURE__*/_jsxDEV(AdminNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex-1 flex flex-col min-h-screen ${user !== null && user !== void 0 && user.isAdmin ? 'lg:ml-72' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: `nav-modern ${location.pathname.includes('/quiz') || location.pathname.includes('/write-exam') ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98' : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'} backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  localStorage.removeItem(\"token\");\n                  navigate(\"/login\");\n                },\n                className: \"force-blue-logout\",\n                title: \"Click to Logout\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '16px'\n                  },\n                  children: \"\\uD83D\\uDEAA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"relative group flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                  style: {\n                    width: '32px',\n                    height: '24px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://flagcdn.com/w40/tz.png\",\n                    alt: \"Tanzania Flag\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative brainwave-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                    style: {\n                      fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                      letterSpacing: '-0.02em'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      className: \"relative inline-block\",\n                      initial: {\n                        opacity: 0,\n                        x: -30,\n                        scale: 0.8\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      },\n                      whileHover: {\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: {\n                          duration: 0.3\n                        }\n                      },\n                      style: {\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      },\n                      children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                        animate: {\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        },\n                        transition: {\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        },\n                        style: {\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      className: \"relative inline-block\",\n                      initial: {\n                        opacity: 0,\n                        x: 30,\n                        scale: 0.8\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      },\n                      whileHover: {\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: {\n                          duration: 0.3\n                        }\n                      },\n                      style: {\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      },\n                      children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                        animate: {\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        },\n                        transition: {\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        },\n                        style: {\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                    initial: {\n                      width: 0,\n                      opacity: 0\n                    },\n                    animate: {\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                    },\n                    transition: {\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    style: {\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: '32px',\n                    height: '32px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/favicon.png\",\n                    alt: \"Brainwave Logo\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                    style: {\n                      display: 'none',\n                      fontSize: '12px'\n                    },\n                    children: \"\\uD83E\\uDDE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.8\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                className: \"flex items-center space-x-2 group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-2 border-white/20 relative cursor-pointer\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: '32px',\n                    height: '32px'\n                  },\n                  children: user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profileImage,\n                    alt: user.name,\n                    className: \"object-cover rounded-full w-full h-full\",\n                    style: {\n                      objectFit: 'cover'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                    style: {\n                      background: '#25D366',\n                      color: '#FFFFFF',\n                      fontSize: '12px'\n                    },\n                    children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden sm:block text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                    children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `flex-1 overflow-auto ${user !== null && user !== void 0 && user.isAdmin ? 'bg-gray-100' : 'bg-gradient-to-br from-gray-50 to-blue-50'} ${user !== null && user !== void 0 && user.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"cwn50ZKVcb7yRGicmnB70bd2a+Y=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "TbHome", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "_user$name", "_user$name$charAt", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "getUserData", "response", "success", "data", "error", "token", "localStorage", "getItem", "includes", "verifyPaymentStatus", "console", "log", "paymentStatus", "current", "clearInterval", "paymentRequired", "isAdmin", "setInterval", "getButtonClass", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "initial", "y", "opacity", "animate", "onClick", "removeItem", "style", "fontSize", "div", "scale", "transition", "duration", "delay", "width", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "profileImage", "name", "char<PERSON>t", "toUpperCase", "class", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\nimport { TbHome } from \"react-icons/tb\";\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* Admin Navigation for admin users */}\r\n      {user?.isAdmin && <AdminNavigation />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className={`flex-1 flex flex-col min-h-screen ${user?.isAdmin ? 'lg:ml-72' : ''}`}>\r\n        {/* Modern Responsive Header - Show for all users */}\r\n        {(\r\n          <motion.header\r\n            initial={{ y: -20, opacity: 0 }}\r\n            animate={{ y: 0, opacity: 1 }}\r\n            className={`nav-modern ${\r\n              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n          >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left section - Logout button and Hub button */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {/* BLUE LOGOUT BUTTON WITH CSS CLASS */}\r\n                <button\r\n                  onClick={() => {\r\n                    localStorage.removeItem(\"token\");\r\n                    navigate(\"/login\");\r\n                  }}\r\n                  className=\"force-blue-logout\"\r\n                  title=\"Click to Logout\"\r\n                >\r\n                  <span style={{ fontSize: '16px' }}>🚪</span>\r\n                  <span>Logout</span>\r\n                </button>\r\n\r\n\r\n              </div>\r\n\r\n              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n              <div className=\"flex-1 flex justify-center\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.9 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"relative group flex items-center space-x-3\"\r\n                >\r\n                  {/* Tanzania Flag - Using actual flag image */}\r\n                  <div\r\n                    className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '24px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        // Fallback to another flag source if first fails\r\n                        e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                        e.target.onerror = () => {\r\n                          // Final fallback - hide image and show text\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                        };\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amazing Animated Brainwave Text */}\r\n                  <div className=\"relative brainwave-container\">\r\n                    <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                        style={{\r\n                          fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                          letterSpacing: '-0.02em'\r\n                        }}>\r\n                      {/* Brain - with amazing effects */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                            \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.3,\r\n                          textShadow: {\r\n                            duration: 2,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, -2, 2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#1f2937',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                        }}\r\n                      >\r\n                        Brain\r\n\r\n                        {/* Electric spark */}\r\n                        <motion.div\r\n                          className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            scale: [0.5, 1.2, 0.5],\r\n                            backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                          }}\r\n                          transition={{\r\n                            duration: 1.5,\r\n                            repeat: Infinity,\r\n                            delay: 2\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#3b82f6',\r\n                            boxShadow: '0 0 10px #3b82f6'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n\r\n                      {/* Wave - with flowing effects (no space) */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          y: [0, -2, 0, 2, 0],\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                            \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.5,\r\n                          y: {\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          },\r\n                          textShadow: {\r\n                            duration: 2.5,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, 2, -2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#059669',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        }}\r\n                      >\r\n                        wave\r\n\r\n                        {/* Wave particle */}\r\n                        <motion.div\r\n                          className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            x: [0, 40, 80],\r\n                            y: [0, -5, 0, 5, 0],\r\n                            backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                          }}\r\n                          transition={{\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            delay: 1\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#10b981',\r\n                            boxShadow: '0 0 8px #10b981'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n                    </h1>\r\n\r\n                    {/* Glowing underline effect */}\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                      initial={{ width: 0, opacity: 0 }}\r\n                      animate={{\r\n                        width: '100%',\r\n                        opacity: 1,\r\n                        boxShadow: [\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                          '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1.5,\r\n                        delay: 1.2,\r\n                        boxShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Official Logo - Small like profile */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"/favicon.png\"\r\n                      alt=\"Brainwave Logo\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = 'none';\r\n                        e.target.nextSibling.style.display = 'flex';\r\n                      }}\r\n                    />\r\n                    <div\r\n                      className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                      style={{\r\n                        display: 'none',\r\n                        fontSize: '12px'\r\n                      }}\r\n                    >\r\n                      🧠\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Modern Glow Effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Right Section - User Profile (WhatsApp Style) */}\r\n              <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Small WhatsApp-style Profile Circle - Same as Ranking */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative cursor-pointer\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  >\r\n                    {user?.profileImage ? (\r\n                      <img\r\n                        src={user.profileImage}\r\n                        alt={user.name}\r\n                        className=\"object-cover rounded-full w-full h-full\"\r\n                        style={{ objectFit: 'cover' }}\r\n                      />\r\n                    ) : (\r\n                      <div\r\n                        className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\r\n                        style={{\r\n                          background: '#25D366',\r\n                          color: '#FFFFFF',\r\n                          fontSize: '12px'\r\n                        }}\r\n                      >\r\n                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxC,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM2B,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM;IAAE2B;EAAiB,CAAC,GAAGvB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACK,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGzB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACO,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAMrC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnC,WAAW,CAAC,CAAC;MACpC,IAAImC,QAAQ,CAACC,OAAO,EAAE;QACpBP,QAAQ,CAAC1B,OAAO,CAACgC,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACL3C,OAAO,CAAC4C,KAAK,CAACH,QAAQ,CAACzC,OAAO,CAAC;QAC/BoC,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdR,QAAQ,CAAC,QAAQ,CAAC;MAClBpC,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM2C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTL,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLJ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINlC,SAAS,CAAC,MAAM;IACd,IAAI0B,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACoB,QAAQ,CAACV,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACR,gBAAgB,EAAEU,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMa,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMN,IAAI,GAAG,MAAM7B,kBAAkB,CAAC,CAAC;MACvCoC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAER,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,KAAK,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,aAAa,MAAK,MAAM,EAAE;QACjD,IAAIrB,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACAc,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BM,QAAQ,CAACpB,eAAe,CAAC4B,IAAI,CAAC,CAAC;QAC/B,IAAIb,WAAW,CAACuB,OAAO,EAAE;UACvBC,aAAa,CAACxB,WAAW,CAACuB,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,KAAK,CAAC;MACpDT,QAAQ,CAACpB,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/Bc,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIlB,yBAAyB,EAAE;QAC7BiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CrB,WAAW,CAACuB,OAAO,GAAGI,WAAW,CAAC,MAAM;UACtCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCF,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTd,QAAQ,CAACnB,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACiB,yBAAyB,CAAC,CAAC;EAE/B/B,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,IAAI,EAAC7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAE;MAC3CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEY,WAAW,CAAC,CAAC;EAGvB,MAAMoB,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAACjC,IAAI,CAAC6B,eAAe,IAAII,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAA5B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqB,aAAa,MAAK,MAAM,IAAI1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACEnC,OAAA;IAAKwC,SAAS,EAAC,0CAA0C;IAAAtC,QAAA,GAEtD,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,OAAO,kBAAIpC,OAAA,CAACH,eAAe;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrC5C,OAAA;MAAKwC,SAAS,EAAG,qCAAoClC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,UAAU,GAAG,EAAG,EAAE;MAAAlC,QAAA,gBAGnFF,OAAA,CAACf,MAAM,CAAC4D,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BR,SAAS,EAAG,cACVvB,QAAQ,CAACE,QAAQ,CAACS,QAAQ,CAAC,OAAO,CAAC,IAAIX,QAAQ,CAACE,QAAQ,CAACS,QAAQ,CAAC,aAAa,CAAC,GAC5E,8EAA8E,GAC9E,2DACL,8FAA8F;QAAA1B,QAAA,eAEjGF,OAAA;UAAKwC,SAAS,EAAC,+CAA+C;UAAAtC,QAAA,eAC5DF,OAAA;YAAKwC,SAAS,EAAC,wEAAwE;YAAAtC,QAAA,gBAErFF,OAAA;cAAKwC,SAAS,EAAC,6BAA6B;cAAAtC,QAAA,eAE1CF,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM;kBACbxB,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;kBAChCnC,QAAQ,CAAC,QAAQ,CAAC;gBACpB,CAAE;gBACFwB,SAAS,EAAC,mBAAmB;gBAC7BD,KAAK,EAAC,iBAAiB;gBAAArC,QAAA,gBAEvBF,OAAA;kBAAMoD,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAnD,QAAA,EAAC;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5C5C,OAAA;kBAAAE,QAAA,EAAM;gBAAM;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC,eAGN5C,OAAA;cAAKwC,SAAS,EAAC,4BAA4B;cAAAtC,QAAA,eACzCF,OAAA,CAACf,MAAM,CAACqE,GAAG;gBACTR,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEO,KAAK,EAAE;gBAAI,CAAE;gBACpCN,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEO,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1ClB,SAAS,EAAC,4CAA4C;gBAAAtC,QAAA,gBAGtDF,OAAA;kBACEwC,SAAS,EAAC,wEAAwE;kBAClFY,KAAK,EAAE;oBACLO,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAA1D,QAAA,eAEFF,OAAA;oBACE6D,GAAG,EAAC,gCAAgC;oBACpCC,GAAG,EAAC,eAAe;oBACnBtB,SAAS,EAAC,4BAA4B;oBACtCY,KAAK,EAAE;sBAAEW,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,8GAA8G;sBAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;wBACvB;wBACAF,CAAC,CAACC,MAAM,CAACd,KAAK,CAACgB,OAAO,GAAG,MAAM;wBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG,+GAA+G;sBACpJ,CAAC;oBACH;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN5C,OAAA;kBAAKwC,SAAS,EAAC,8BAA8B;kBAAAtC,QAAA,gBAC3CF,OAAA;oBAAIwC,SAAS,EAAC,qFAAqF;oBAC/FY,KAAK,EAAE;sBACLmB,UAAU,EAAE,yDAAyD;sBACrEC,aAAa,EAAE;oBACjB,CAAE;oBAAAtE,QAAA,gBAEJF,OAAA,CAACf,MAAM,CAACwF,IAAI;sBACVjC,SAAS,EAAC,uBAAuB;sBACjCM,OAAO,EAAE;wBAAEE,OAAO,EAAE,CAAC;wBAAE0B,CAAC,EAAE,CAAC,EAAE;wBAAEnB,KAAK,EAAE;sBAAI,CAAE;sBAC5CN,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC;wBACV0B,CAAC,EAAE,CAAC;wBACJnB,KAAK,EAAE,CAAC;wBACRoB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;sBAEtC,CAAE;sBACFnB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,KAAK,EAAE,GAAG;wBACViB,UAAU,EAAE;0BACVlB,QAAQ,EAAE,CAAC;0BACXmB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR;sBACF,CAAE;sBACFC,UAAU,EAAE;wBACVxB,KAAK,EAAE,GAAG;wBACVyB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACrBxB,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAC9B,CAAE;sBACFL,KAAK,EAAE;wBACL6B,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBP,UAAU,EAAE;sBACd,CAAE;sBAAAzE,QAAA,GACH,OAGC,eACAF,OAAA,CAACf,MAAM,CAACqE,GAAG;wBACTd,SAAS,EAAC,+CAA+C;wBACzDS,OAAO,EAAE;0BACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BAClBO,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;0BACtB4B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;wBACnD,CAAE;wBACF3B,UAAU,EAAE;0BACVC,QAAQ,EAAE,GAAG;0BACbmB,MAAM,EAAEC,QAAQ;0BAChBnB,KAAK,EAAE;wBACT,CAAE;wBACFN,KAAK,EAAE;0BACL+B,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE;wBACb;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC,eAGd5C,OAAA,CAACf,MAAM,CAACwF,IAAI;sBACVjC,SAAS,EAAC,uBAAuB;sBACjCM,OAAO,EAAE;wBAAEE,OAAO,EAAE,CAAC;wBAAE0B,CAAC,EAAE,EAAE;wBAAEnB,KAAK,EAAE;sBAAI,CAAE;sBAC3CN,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC;wBACV0B,CAAC,EAAE,CAAC;wBACJnB,KAAK,EAAE,CAAC;wBACRR,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;sBAEtC,CAAE;sBACFnB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,KAAK,EAAE,GAAG;wBACVX,CAAC,EAAE;0BACDU,QAAQ,EAAE,CAAC;0BACXmB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR,CAAC;wBACDH,UAAU,EAAE;0BACVlB,QAAQ,EAAE,GAAG;0BACbmB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR;sBACF,CAAE;sBACFC,UAAU,EAAE;wBACVxB,KAAK,EAAE,GAAG;wBACVyB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;wBACrBxB,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAC9B,CAAE;sBACFL,KAAK,EAAE;wBACL6B,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBP,UAAU,EAAE;sBACd,CAAE;sBAAAzE,QAAA,GACH,MAGC,eACAF,OAAA,CAACf,MAAM,CAACqE,GAAG;wBACTd,SAAS,EAAC,gDAAgD;wBAC1DS,OAAO,EAAE;0BACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;0BACd3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BACnBoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;wBACnD,CAAE;wBACF3B,UAAU,EAAE;0BACVC,QAAQ,EAAE,CAAC;0BACXmB,MAAM,EAAEC,QAAQ;0BAChBnB,KAAK,EAAE;wBACT,CAAE;wBACFN,KAAK,EAAE;0BACL+B,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE;wBACb;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eAGL5C,OAAA,CAACf,MAAM,CAACqE,GAAG;oBACTd,SAAS,EAAC,4CAA4C;oBACtDM,OAAO,EAAE;sBAAEa,KAAK,EAAE,CAAC;sBAAEX,OAAO,EAAE;oBAAE,CAAE;oBAClCC,OAAO,EAAE;sBACPU,KAAK,EAAE,MAAM;sBACbX,OAAO,EAAE,CAAC;sBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACF5B,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbC,KAAK,EAAE,GAAG;sBACV0B,SAAS,EAAE;wBACT3B,QAAQ,EAAE,CAAC;wBACXmB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACF1B,KAAK,EAAE;sBACLiC,UAAU,EAAE,mDAAmD;sBAC/DD,SAAS,EAAE;oBACb;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN5C,OAAA;kBACEwC,SAAS,EAAC,gEAAgE;kBAC1EY,KAAK,EAAE;oBACLiC,UAAU,EAAE,SAAS;oBACrBD,SAAS,EAAE,4BAA4B;oBACvCzB,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAA1D,QAAA,gBAEFF,OAAA;oBACE6D,GAAG,EAAC,cAAc;oBAClBC,GAAG,EAAC,gBAAgB;oBACpBtB,SAAS,EAAC,4BAA4B;oBACtCY,KAAK,EAAE;sBAAEW,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACgB,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACoB,WAAW,CAAClC,KAAK,CAACgB,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF5C,OAAA;oBACEwC,SAAS,EAAC,gHAAgH;oBAC1HY,KAAK,EAAE;sBACLgB,OAAO,EAAE,MAAM;sBACff,QAAQ,EAAE;oBACZ,CAAE;oBAAAnD,QAAA,EACH;kBAED;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5C,OAAA;kBAAKwC,SAAS,EAAC;gBAAyK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGN5C,OAAA;cAAKwC,SAAS,EAAC,sDAAsD;cAAAtC,QAAA,eACnEF,OAAA,CAACf,MAAM,CAACqE,GAAG;gBACTR,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEO,KAAK,EAAE;gBAAI,CAAE;gBACpCN,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEO,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1ClB,SAAS,EAAC,mCAAmC;gBAAAtC,QAAA,gBAG7CF,OAAA;kBACEwC,SAAS,EAAC,+EAA+E;kBACzFY,KAAK,EAAE;oBACLiC,UAAU,EAAE,SAAS;oBACrBD,SAAS,EAAE,4BAA4B;oBACvCzB,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAA1D,QAAA,EAEDI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiF,YAAY,gBACjBvF,OAAA;oBACE6D,GAAG,EAAEvD,IAAI,CAACiF,YAAa;oBACvBzB,GAAG,EAAExD,IAAI,CAACkF,IAAK;oBACfhD,SAAS,EAAC,yCAAyC;oBACnDY,KAAK,EAAE;sBAAEW,SAAS,EAAE;oBAAQ;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,gBAEF5C,OAAA;oBACEwC,SAAS,EAAC,2EAA2E;oBACrFY,KAAK,EAAE;sBACLiC,UAAU,EAAE,SAAS;sBACrBJ,KAAK,EAAE,SAAS;sBAChB5B,QAAQ,EAAE;oBACZ,CAAE;oBAAAnD,QAAA,EAED,CAAAI,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEkF,IAAI,cAAApF,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYqF,MAAM,CAAC,CAAC,CAAC,cAAApF,iBAAA,uBAArBA,iBAAA,CAAuBqF,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGN5C,OAAA;kBAAKwC,SAAS,EAAC,4BAA4B;kBAAAtC,QAAA,gBACzCF,OAAA;oBAAKwC,SAAS,EAAC,wGAAwG;oBAAAtC,QAAA,EACpH,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,IAAI,KAAI;kBAAM;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACN5C,OAAA;oBAAKwC,SAAS,EAAC,iFAAiF;oBAAAtC,QAAA,GAAC,QACzF,EAACI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,KAAK;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAIhB5C,OAAA;QAAMwC,SAAS,EAAG,wBAChBlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GACT,aAAa,GACb,2CACL,IAAG9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,OAAO,GAAG,KAAK,GAAG,eAAgB,EAAE;QAAAlC,QAAA,eAC5CF,OAAA,CAACf,MAAM,CAACqE,GAAG;UACTR,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BS,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1ClB,SAAS,EAAC,QAAQ;UAAAtC,QAAA,EAEjBA;QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzC,EAAA,CA9bQF,cAAc;EAAA,QACJb,WAAW,EAGCA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAAqG,EAAA,GARrB3F,cAAc;AAgcvB,eAAeA,cAAc;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}