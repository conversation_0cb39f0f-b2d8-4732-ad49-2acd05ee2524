{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { TbMenu2, TbX, TbHome, TbBrain, TbBook, TbRobot, TbChartLine, TbTrophy, TbUser, TbMessageCircle, TbCreditCard, TbLogout, TbChevronRight } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n  const navigationItems = [{\n    title: 'Hub',\n    description: 'Main dashboard',\n    icon: TbHome,\n    path: '/user/hub',\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: TbBrain,\n    path: '/user/quiz',\n    color: 'from-purple-500 to-purple-600'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: TbBook,\n    path: '/user/study-material',\n    color: 'from-green-500 to-green-600'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help',\n    icon: TbRobot,\n    path: '/user/chat',\n    color: 'from-orange-500 to-orange-600'\n  }, {\n    title: 'Reports',\n    description: 'Track progress',\n    icon: TbChartLine,\n    path: '/user/reports',\n    color: 'from-red-500 to-red-600'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: TbTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600'\n  }, {\n    title: 'Profile',\n    description: 'Manage account',\n    icon: TbUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: TbMessageCircle,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade learning',\n    icon: TbCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600'\n  }, {\n    title: 'Logout',\n    description: 'Sign out of account',\n    icon: TbLogout,\n    path: 'logout',\n    color: 'from-red-500 to-red-600'\n  }];\n  const handleNavigation = path => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n  const isActivePath = path => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\",\n      title: isOpen ? \"Close Menu\" : \"Open Menu\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n        className: \"w-6 h-6 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n        className: \"w-6 h-6 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        onClick: () => setIsOpen(false),\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          x: -400,\n          opacity: 0\n        },\n        animate: {\n          x: 0,\n          opacity: 1\n        },\n        exit: {\n          x: -400,\n          opacity: 0\n        },\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200\n        },\n        className: \"fixed left-0 top-0 h-full w-80 sm:w-80 md:w-80 lg:w-80 max-w-[90vw] bg-white shadow-2xl z-50 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(false),\n            className: \"absolute top-4 right-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\",\n            title: \"Close Menu\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center pr-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl sm:text-2xl font-bold mb-2\",\n              children: \"Navigation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-200 text-xs sm:text-sm\",\n              children: \"Choose your destination\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            const isActive = item.path !== 'logout' && isActivePath(item.path);\n            const isLogout = item.path === 'logout';\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.3,\n                delay: index * 0.05\n              },\n              onClick: () => handleNavigation(item.path),\n              className: `w-full flex items-center justify-between p-3 sm:p-4 rounded-xl transition-all duration-200 ${isActive ? 'bg-blue-50 border-2 border-blue-200 shadow-md' : isLogout ? 'hover:bg-red-50 border-2 border-transparent' : 'hover:bg-gray-50 border-2 border-transparent'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`,\n                  children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `font-medium ${isActive ? 'text-blue-700' : isLogout ? 'text-red-700' : 'text-gray-900'}`,\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm ${isActive ? 'text-blue-600' : isLogout ? 'text-red-600' : 'text-gray-500'}`,\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n                className: `w-5 h-5 ${isActive ? 'text-blue-600' : isLogout ? 'text-red-400' : 'text-gray-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernSidebar, \"48y0qeclxm0j0IxiXymT1cAtcjo=\", false, function () {\n  return [useNavigate, useLocation, useSelector];\n});\n_c = ModernSidebar;\nexport default ModernSidebar;\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "motion", "AnimatePresence", "useSelector", "TbMenu2", "TbX", "TbHome", "TbBrain", "TbBook", "TbRobot", "TbChartLine", "TbTrophy", "TbUser", "TbMessageCircle", "TbCreditCard", "TbLogout", "TbChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "_s", "isOpen", "setIsOpen", "navigate", "location", "user", "state", "handleKeyDown", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "navigationItems", "title", "description", "icon", "path", "color", "handleNavigation", "handleLogout", "localStorage", "removeItem", "isActivePath", "pathname", "startsWith", "children", "onClick", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness", "map", "item", "index", "IconComponent", "isActive", "isLogout", "button", "duration", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight\n} from 'react-icons/tb';\n\nconst ModernSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const navigationItems = [\n    {\n      title: 'Hub',\n      description: 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: TbBrain,\n      path: '/user/quiz',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: TbBook,\n      path: '/user/study-material',\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help',\n      icon: TbRobot,\n      path: '/user/chat',\n      color: 'from-orange-500 to-orange-600'\n    },\n    {\n      title: 'Reports',\n      description: 'Track progress',\n      icon: TbChartLine,\n      path: '/user/reports',\n      color: 'from-red-500 to-red-600'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: TbTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: TbMessageCircle,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade learning',\n      icon: TbCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600'\n    },\n    {\n      title: 'Logout',\n      description: 'Sign out of account',\n      icon: TbLogout,\n      path: 'logout',\n      color: 'from-red-500 to-red-600'\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\"\n        title={isOpen ? \"Close Menu\" : \"Open Menu\"}\n      >\n        {isOpen ? (\n          <TbX className=\"w-6 h-6 text-gray-700\" />\n        ) : (\n          <TbMenu2 className=\"w-6 h-6 text-gray-700\" />\n        )}\n      </button>\n\n      {/* Backdrop */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setIsOpen(false)}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ x: -400, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -400, opacity: 0 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 200 }}\n            className=\"fixed left-0 top-0 h-full w-80 sm:w-80 md:w-80 lg:w-80 max-w-[90vw] bg-white shadow-2xl z-50 flex flex-col\"\n          >\n            {/* Header */}\n            <div className=\"p-4 sm:p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\">\n              {/* Close Button */}\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"absolute top-4 right-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\"\n                title=\"Close Menu\"\n              >\n                <TbX className=\"w-5 h-5 text-white\" />\n              </button>\n\n              <div className=\"text-center pr-12\">\n                <h1 className=\"text-xl sm:text-2xl font-bold mb-2\">Navigation</h1>\n                <p className=\"text-blue-200 text-xs sm:text-sm\">Choose your destination</p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = item.path !== 'logout' && isActivePath(item.path);\n                const isLogout = item.path === 'logout';\n\n                return (\n                  <motion.button\n                    key={item.path}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between p-3 sm:p-4 rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : isLogout\n                        ? 'hover:bg-red-50 border-2 border-transparent'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}>\n                        <IconComponent className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p className={`font-medium ${\n                          isActive\n                            ? 'text-blue-700'\n                            : isLogout\n                            ? 'text-red-700'\n                            : 'text-gray-900'\n                        }`}>\n                          {item.title}\n                        </p>\n                        <p className={`text-sm ${\n                          isActive\n                            ? 'text-blue-600'\n                            : isLogout\n                            ? 'text-red-600'\n                            : 'text-gray-500'\n                        }`}>\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight className={`w-5 h-5 ${\n                      isActive\n                        ? 'text-blue-600'\n                        : isLogout\n                        ? 'text-red-400'\n                        : 'text-gray-400'\n                    }`} />\n                  </motion.button>\n                );\n              })}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default ModernSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,cAAc,QACT,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM+B,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIR,MAAM,EAAE;QACpCC,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVS,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;MACnD;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;MACtDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EAEZ,MAAMe,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAEnC,MAAM;IACZoC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAElC,OAAO;IACbmC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAEjC,MAAM;IACZkC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAEhC,OAAO;IACbiC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAE/B,WAAW;IACjBgC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE9B,QAAQ;IACd+B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAE7B,MAAM;IACZ8B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE5B,eAAe;IACrB6B,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAE3B,YAAY;IAClB4B,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE1B,QAAQ;IACd2B,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAIF,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBG,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLpB,QAAQ,CAACiB,IAAI,CAAC;IAChB;IACAlB,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BtB,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMuB,YAAY,GAAIN,IAAI,IAAK;IAC7B,OAAOhB,QAAQ,CAACuB,QAAQ,KAAKP,IAAI,IAAIhB,QAAQ,CAACuB,QAAQ,CAACC,UAAU,CAACR,IAAI,CAAC;EACzE,CAAC;EAED,oBACExB,OAAA,CAAAE,SAAA;IAAA+B,QAAA,gBAEEjC,OAAA;MACEkC,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAAC,CAACD,MAAM,CAAE;MAClC8B,SAAS,EAAC,8IAA8I;MACxJd,KAAK,EAAEhB,MAAM,GAAG,YAAY,GAAG,WAAY;MAAA4B,QAAA,EAE1C5B,MAAM,gBACLL,OAAA,CAACb,GAAG;QAACgD,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEzCvC,OAAA,CAACd,OAAO;QAACiD,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC7C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAGTvC,OAAA,CAAChB,eAAe;MAAAiD,QAAA,EACb5B,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAACyD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBR,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAAC,KAAK,CAAE;QAChC6B,SAAS,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBvC,OAAA,CAAChB,eAAe;MAAAiD,QAAA,EACb5B,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAACyD,GAAG;QACTC,OAAO,EAAE;UAAEI,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACjCC,OAAO,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEC,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE;QAC5Dd,SAAS,EAAC,4GAA4G;QAAAF,QAAA,gBAGtHjC,OAAA;UAAKmC,SAAS,EAAC,2EAA2E;UAAAF,QAAA,gBAExFjC,OAAA;YACEkC,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAAC,KAAK,CAAE;YAChC6B,SAAS,EAAC,oGAAoG;YAC9Gd,KAAK,EAAC,YAAY;YAAAY,QAAA,eAElBjC,OAAA,CAACb,GAAG;cAACgD,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAETvC,OAAA;YAAKmC,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAChCjC,OAAA;cAAImC,SAAS,EAAC,oCAAoC;cAAAF,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEvC,OAAA;cAAGmC,SAAS,EAAC,kCAAkC;cAAAF,QAAA,EAAC;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvC,OAAA;UAAKmC,SAAS,EAAC,sCAAsC;UAAAF,QAAA,EAClDb,eAAe,CAAC8B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC5B,IAAI;YAC/B,MAAM+B,QAAQ,GAAGH,IAAI,CAAC3B,IAAI,KAAK,QAAQ,IAAIM,YAAY,CAACqB,IAAI,CAAC3B,IAAI,CAAC;YAClE,MAAM+B,QAAQ,GAAGJ,IAAI,CAAC3B,IAAI,KAAK,QAAQ;YAEvC,oBACExB,OAAA,CAACjB,MAAM,CAACyE,MAAM;cAEZf,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE;cAAE,CAAE;cAC9BC,UAAU,EAAE;gBAAEW,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEN,KAAK,GAAG;cAAK,CAAE;cACnDlB,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAACyB,IAAI,CAAC3B,IAAI,CAAE;cAC3CW,SAAS,EAAG,8FACVmB,QAAQ,GACJ,+CAA+C,GAC/CC,QAAQ,GACR,6CAA6C,GAC7C,8CACL,EAAE;cAAAtB,QAAA,gBAEHjC,OAAA;gBAAKmC,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1CjC,OAAA;kBAAKmC,SAAS,EAAG,yCAAwCgB,IAAI,CAAC1B,KAAM,mCAAmC;kBAAAQ,QAAA,eACrGjC,OAAA,CAACqD,aAAa;oBAAClB,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNvC,OAAA;kBAAKmC,SAAS,EAAC,WAAW;kBAAAF,QAAA,gBACxBjC,OAAA;oBAAGmC,SAAS,EAAG,eACbmB,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;oBAAAtB,QAAA,EACAkB,IAAI,CAAC9B;kBAAK;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACJvC,OAAA;oBAAGmC,SAAS,EAAG,WACbmB,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;oBAAAtB,QAAA,EACAkB,IAAI,CAAC7B;kBAAW;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvC,OAAA,CAACF,cAAc;gBAACqC,SAAS,EAAG,WAC1BmB,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GA5CDY,IAAI,CAAC3B,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6CD,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACnC,EAAA,CA9OID,aAAa;EAAA,QAEAtB,WAAW,EACXC,WAAW,EACXG,WAAW;AAAA;AAAA0E,EAAA,GAJxBxD,aAAa;AAgPnB,eAAeA,aAAa;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}