{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Dashboard\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Card, Row, Col, Statistic, Progress } from 'antd';\nimport { TbUsers, TbBook, TbFileText, TbChartBar, TbTrendingUp, TbTarget, TbAward, TbClock } from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      dispatch(ShowLoading());\n\n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({\n        examName: '',\n        userName: '',\n        page: 1,\n        limit: 1000\n      });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n\n      // Calculate average score from reports\n      const averageScore = reports.length > 0 ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length : 0;\n\n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? totalReports / totalUsers * 100 : 0;\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      dispatch(HideLoading());\n    }\n  };\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      y: 20,\n      opacity: 0\n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  const quickActions = [{\n    title: 'Manage Users',\n    description: 'View and manage student accounts',\n    icon: TbUsers,\n    path: '/admin/users',\n    color: 'bg-blue-500'\n  }, {\n    title: 'Create Exam',\n    description: 'Add new exams and questions',\n    icon: TbFileText,\n    path: '/admin/exams/add',\n    color: 'bg-green-500'\n  }, {\n    title: 'Study Materials',\n    description: 'Upload learning resources',\n    icon: TbBook,\n    path: '/admin/study-materials',\n    color: 'bg-purple-500'\n  }, {\n    title: 'View Reports',\n    description: 'Analyze student performance',\n    icon: TbChartBar,\n    path: '/admin/reports',\n    color: 'bg-orange-500'\n  }];\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    variants: containerVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    className: \"p-6 max-w-7xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      variants: itemVariants,\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900 mb-2\",\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Here's what's happening with your educational platform today.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: itemVariants,\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"h-full\",\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Students\",\n              value: stats.totalUsers,\n              prefix: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-500 text-sm\",\n                children: [stats.activeUsers, \" active\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Exams\",\n              value: stats.totalExams,\n              prefix: /*#__PURE__*/_jsxDEV(TbFileText, {\n                className: \"text-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Exam Attempts\",\n              value: stats.totalReports,\n              prefix: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"text-orange-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Average Score\",\n              value: stats.averageScore,\n              suffix: \"%\",\n              prefix: /*#__PURE__*/_jsxDEV(TbAward, {\n                className: \"text-purple-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 25\n              }, this),\n              valueStyle: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: itemVariants,\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Student Engagement\",\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Active Students\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [Math.round(stats.activeUsers / stats.totalUsers * 100) || 0, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                  percent: Math.round(stats.activeUsers / stats.totalUsers * 100) || 0,\n                  strokeColor: \"#52c41a\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Exam Completion Rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [stats.completionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                  percent: stats.completionRate,\n                  strokeColor: \"#1890ff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Quick Actions\",\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: quickActions.map((action, index) => {\n                const IconComponent = action.icon;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all\",\n                  onClick: () => window.location.href = action.path,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 ${action.color} rounded-lg flex items-center justify-center mb-2`,\n                    children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-sm mb-1\",\n                    children: action.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: action.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)]\n                }, action.title, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: itemVariants,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        title: \"System Status\",\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                className: \"w-6 h-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium\",\n              children: \"System Health\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 text-sm\",\n              children: \"All systems operational\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium\",\n              children: \"Last Updated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Just now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n                className: \"w-6 h-6 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium\",\n              children: \"Data Sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-purple-600 text-sm\",\n              children: \"Synchronized\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"P6BTRuCszYLLtfMybLT3d27dPGs=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useDispatch", "useSelector", "Card", "Row", "Col", "Statistic", "Progress", "TbUsers", "TbBook", "TbFileText", "TbChartBar", "TbTrendingUp", "TbTarget", "TbAward", "TbClock", "getAllUsers", "getAllExams", "getAllReports", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "dispatch", "user", "state", "stats", "setStats", "totalUsers", "activeUsers", "totalExams", "totalReports", "averageScore", "completionRate", "fetchDashboardData", "usersResponse", "users", "success", "examsResponse", "exams", "data", "reportsResponse", "examName", "userName", "page", "limit", "reports", "length", "filter", "u", "isBlocked", "reduce", "sum", "report", "percentage", "Math", "round", "error", "console", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "quickActions", "title", "description", "icon", "path", "color", "div", "variants", "initial", "animate", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "sm", "lg", "value", "prefix", "valueStyle", "suffix", "percent", "strokeColor", "map", "action", "index", "IconComponent", "whileHover", "scale", "whileTap", "onClick", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Dashboard/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Card, Row, Col, Statistic, Progress } from 'antd';\nimport {\n  TbUsers,\n  TbBook,\n  TbFileText,\n  TbChartBar,\n  TbTrendingUp,\n  TbTarget,\n  TbAward,\n  TbClock\n} from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\n\nconst AdminDashboard = () => {\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      dispatch(ShowLoading());\n\n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n      \n      // Calculate average score from reports\n      const averageScore = reports.length > 0 \n        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length\n        : 0;\n      \n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;\n\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      dispatch(HideLoading());\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  const quickActions = [\n    {\n      title: 'Manage Users',\n      description: 'View and manage student accounts',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Create Exam',\n      description: 'Add new exams and questions',\n      icon: TbFileText,\n      path: '/admin/exams/add',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Upload learning resources',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'bg-purple-500'\n    },\n    {\n      title: 'View Reports',\n      description: 'Analyze student performance',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'bg-orange-500'\n    }\n  ];\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className=\"p-6 max-w-7xl mx-auto\"\n    >\n      {/* Welcome Header */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Welcome back, {user?.name}! 👋\n        </h1>\n        <p className=\"text-gray-600\">\n          Here's what's happening with your educational platform today.\n        </p>\n      </motion.div>\n\n      {/* Statistics Cards */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Total Students\"\n                value={stats.totalUsers}\n                prefix={<TbUsers className=\"text-blue-500\" />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-2\">\n                <span className=\"text-green-500 text-sm\">\n                  {stats.activeUsers} active\n                </span>\n              </div>\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Total Exams\"\n                value={stats.totalExams}\n                prefix={<TbFileText className=\"text-green-500\" />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Exam Attempts\"\n                value={stats.totalReports}\n                prefix={<TbTarget className=\"text-orange-500\" />}\n                valueStyle={{ color: '#fa8c16' }}\n              />\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Average Score\"\n                value={stats.averageScore}\n                suffix=\"%\"\n                prefix={<TbAward className=\"text-purple-500\" />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </motion.div>\n\n      {/* Performance Overview */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <Row gutter={[16, 16]}>\n          <Col xs={24} lg={12}>\n            <Card title=\"Student Engagement\" className=\"h-full\">\n              <div className=\"space-y-4\">\n                <div>\n                  <div className=\"flex justify-between mb-2\">\n                    <span>Active Students</span>\n                    <span>{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</span>\n                  </div>\n                  <Progress \n                    percent={Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0} \n                    strokeColor=\"#52c41a\"\n                  />\n                </div>\n                \n                <div>\n                  <div className=\"flex justify-between mb-2\">\n                    <span>Exam Completion Rate</span>\n                    <span>{stats.completionRate}%</span>\n                  </div>\n                  <Progress \n                    percent={stats.completionRate} \n                    strokeColor=\"#1890ff\"\n                  />\n                </div>\n              </div>\n            </Card>\n          </Col>\n          \n          <Col xs={24} lg={12}>\n            <Card title=\"Quick Actions\" className=\"h-full\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                {quickActions.map((action, index) => {\n                  const IconComponent = action.icon;\n                  return (\n                    <motion.div\n                      key={action.title}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all\"\n                      onClick={() => window.location.href = action.path}\n                    >\n                      <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center mb-2`}>\n                        <IconComponent className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <h4 className=\"font-medium text-sm mb-1\">{action.title}</h4>\n                      <p className=\"text-xs text-gray-500\">{action.description}</p>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </Card>\n          </Col>\n        </Row>\n      </motion.div>\n\n      {/* Recent Activity */}\n      <motion.div variants={itemVariants}>\n        <Card title=\"System Status\" className=\"mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbTrendingUp className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h4 className=\"font-medium\">System Health</h4>\n              <p className=\"text-green-600 text-sm\">All systems operational</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbClock className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h4 className=\"font-medium\">Last Updated</h4>\n              <p className=\"text-gray-600 text-sm\">Just now</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbChartBar className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h4 className=\"font-medium\">Data Sync</h4>\n              <p className=\"text-purple-600 text-sm\">Synchronized</p>\n            </div>\n          </div>\n        </Card>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AAC1D,SACEC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,OAAO,EACPC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC;IACjCgC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEFpC,SAAS,CAAC,MAAM;IACdqC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFX,QAAQ,CAACL,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMiB,aAAa,GAAG,MAAMrB,WAAW,CAAC,CAAC;MACzC,MAAMsB,KAAK,GAAGD,aAAa,CAACE,OAAO,GAAGF,aAAa,CAACC,KAAK,GAAG,EAAE;;MAE9D;MACA,MAAME,aAAa,GAAG,MAAMvB,WAAW,CAAC,CAAC;MACzC,MAAMwB,KAAK,GAAGD,aAAa,CAACD,OAAO,GAAGC,aAAa,CAACE,IAAI,GAAG,EAAE;;MAE7D;MACA,MAAMC,eAAe,GAAG,MAAMzB,aAAa,CAAC;QAAE0B,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACjG,MAAMC,OAAO,GAAGL,eAAe,CAACJ,OAAO,GAAGI,eAAe,CAACD,IAAI,GAAG,EAAE;;MAEnE;MACA,MAAMZ,UAAU,GAAGQ,KAAK,CAACW,MAAM;MAC/B,MAAMlB,WAAW,GAAGO,KAAK,CAACY,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,SAAS,CAAC,CAACH,MAAM;MAC1D,MAAMjB,UAAU,GAAGS,KAAK,CAACQ,MAAM;MAC/B,MAAMhB,YAAY,GAAGe,OAAO,CAACC,MAAM;;MAEnC;MACA,MAAMf,YAAY,GAAGc,OAAO,CAACC,MAAM,GAAG,CAAC,GACnCD,OAAO,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGR,OAAO,CAACC,MAAM,GACnF,CAAC;;MAEL;MACA,MAAMd,cAAc,GAAGL,UAAU,GAAG,CAAC,GAAIG,YAAY,GAAGH,UAAU,GAAI,GAAG,GAAG,CAAC;MAE7ED,QAAQ,CAAC;QACPC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,YAAY;QACZC,YAAY,EAAEuB,IAAI,CAACC,KAAK,CAACxB,YAAY,CAAC;QACtCC,cAAc,EAAEsB,IAAI,CAACC,KAAK,CAACvB,cAAc;MAC3C,CAAC,CAAC;MAEFV,QAAQ,CAACN,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDlC,QAAQ,CAACN,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM0C,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAE;MAAEM,CAAC,EAAE,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAC;IAC7BC,OAAO,EAAE;MACPI,CAAC,EAAE,CAAC;MACJL,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVI,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAEjE,OAAO;IACbkE,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAEhE,MAAM;IACZiE,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE9D,UAAU;IAChB+D,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACErD,OAAA,CAACtB,MAAM,CAAC4E,GAAG;IACTC,QAAQ,EAAEhB,iBAAkB;IAC5BiB,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IACjBC,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAGjC3D,OAAA,CAACtB,MAAM,CAAC4E,GAAG;MAACC,QAAQ,EAAEV,YAAa;MAACa,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAClD3D,OAAA;QAAI0D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,GAAC,gBACtC,EAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,EAAC,gBAC5B;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLhE,OAAA;QAAG0D,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGbhE,OAAA,CAACtB,MAAM,CAAC4E,GAAG;MAACC,QAAQ,EAAEV,YAAa;MAACa,SAAS,EAAC,MAAM;MAAAC,QAAA,eAClD3D,OAAA,CAAClB,GAAG;QAACmF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACpB3D,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACzB3D,OAAA,CAACnB,IAAI;YAAC6E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACtB3D,OAAA,CAAChB,SAAS;cACRiE,KAAK,EAAC,gBAAgB;cACtBoB,KAAK,EAAE/D,KAAK,CAACE,UAAW;cACxB8D,MAAM,eAAEtE,OAAA,CAACd,OAAO;gBAACwE,SAAS,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9CO,UAAU,EAAE;gBAAElB,KAAK,EAAE;cAAU;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACFhE,OAAA;cAAK0D,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB3D,OAAA;gBAAM0D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GACrCrD,KAAK,CAACG,WAAW,EAAC,SACrB;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENhE,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACzB3D,OAAA,CAACnB,IAAI;YAAC6E,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACtB3D,OAAA,CAAChB,SAAS;cACRiE,KAAK,EAAC,aAAa;cACnBoB,KAAK,EAAE/D,KAAK,CAACI,UAAW;cACxB4D,MAAM,eAAEtE,OAAA,CAACZ,UAAU;gBAACsE,SAAS,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAClDO,UAAU,EAAE;gBAAElB,KAAK,EAAE;cAAU;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENhE,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACzB3D,OAAA,CAACnB,IAAI;YAAC6E,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACtB3D,OAAA,CAAChB,SAAS;cACRiE,KAAK,EAAC,eAAe;cACrBoB,KAAK,EAAE/D,KAAK,CAACK,YAAa;cAC1B2D,MAAM,eAAEtE,OAAA,CAACT,QAAQ;gBAACmE,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACjDO,UAAU,EAAE;gBAAElB,KAAK,EAAE;cAAU;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENhE,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACzB3D,OAAA,CAACnB,IAAI;YAAC6E,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACtB3D,OAAA,CAAChB,SAAS;cACRiE,KAAK,EAAC,eAAe;cACrBoB,KAAK,EAAE/D,KAAK,CAACM,YAAa;cAC1B4D,MAAM,EAAC,GAAG;cACVF,MAAM,eAAEtE,OAAA,CAACR,OAAO;gBAACkE,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChDO,UAAU,EAAE;gBAAElB,KAAK,EAAE;cAAU;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbhE,OAAA,CAACtB,MAAM,CAAC4E,GAAG;MAACC,QAAQ,EAAEV,YAAa;MAACa,SAAS,EAAC,MAAM;MAAAC,QAAA,eAClD3D,OAAA,CAAClB,GAAG;QAACmF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACpB3D,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,EAAG;UAAAT,QAAA,eAClB3D,OAAA,CAACnB,IAAI;YAACoE,KAAK,EAAC,oBAAoB;YAACS,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACjD3D,OAAA;cAAK0D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3D,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAK0D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC3D,OAAA;oBAAA2D,QAAA,EAAM;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5BhE,OAAA;oBAAA2D,QAAA,GAAOxB,IAAI,CAACC,KAAK,CAAE9B,KAAK,CAACG,WAAW,GAAGH,KAAK,CAACE,UAAU,GAAI,GAAG,CAAC,IAAI,CAAC,EAAC,GAAC;kBAAA;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNhE,OAAA,CAACf,QAAQ;kBACPwF,OAAO,EAAEtC,IAAI,CAACC,KAAK,CAAE9B,KAAK,CAACG,WAAW,GAAGH,KAAK,CAACE,UAAU,GAAI,GAAG,CAAC,IAAI,CAAE;kBACvEkE,WAAW,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhE,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAK0D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC3D,OAAA;oBAAA2D,QAAA,EAAM;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjChE,OAAA;oBAAA2D,QAAA,GAAOrD,KAAK,CAACO,cAAc,EAAC,GAAC;kBAAA;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNhE,OAAA,CAACf,QAAQ;kBACPwF,OAAO,EAAEnE,KAAK,CAACO,cAAe;kBAC9B6D,WAAW,EAAC;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENhE,OAAA,CAACjB,GAAG;UAACmF,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,EAAG;UAAAT,QAAA,eAClB3D,OAAA,CAACnB,IAAI;YAACoE,KAAK,EAAC,eAAe;YAACS,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAC5C3D,OAAA;cAAK0D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCX,YAAY,CAAC2B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;gBACnC,MAAMC,aAAa,GAAGF,MAAM,CAACzB,IAAI;gBACjC,oBACEnD,OAAA,CAACtB,MAAM,CAAC4E,GAAG;kBAETyB,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BtB,SAAS,EAAC,qFAAqF;kBAC/FwB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGT,MAAM,CAACxB,IAAK;kBAAAO,QAAA,gBAElD3D,OAAA;oBAAK0D,SAAS,EAAG,WAAUkB,MAAM,CAACvB,KAAM,mDAAmD;oBAAAM,QAAA,eACzF3D,OAAA,CAAC8E,aAAa;sBAACpB,SAAS,EAAC;oBAAoB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACNhE,OAAA;oBAAI0D,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAEiB,MAAM,CAAC3B;kBAAK;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DhE,OAAA;oBAAG0D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEiB,MAAM,CAAC1B;kBAAW;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAVxDY,MAAM,CAAC3B,KAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWP,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbhE,OAAA,CAACtB,MAAM,CAAC4E,GAAG;MAACC,QAAQ,EAAEV,YAAa;MAAAc,QAAA,eACjC3D,OAAA,CAACnB,IAAI;QAACoE,KAAK,EAAC,eAAe;QAACS,SAAS,EAAC,MAAM;QAAAC,QAAA,eAC1C3D,OAAA;UAAK0D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD3D,OAAA;YAAK0D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3D,OAAA;cAAK0D,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChG3D,OAAA,CAACV,YAAY;gBAACoE,SAAS,EAAC;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNhE,OAAA;cAAI0D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ChE,OAAA;cAAG0D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENhE,OAAA;YAAK0D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3D,OAAA;cAAK0D,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F3D,OAAA,CAACP,OAAO;gBAACiE,SAAS,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNhE,OAAA;cAAI0D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7ChE,OAAA;cAAG0D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAENhE,OAAA;YAAK0D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3D,OAAA;cAAK0D,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjG3D,OAAA,CAACX,UAAU;gBAACqE,SAAS,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNhE,OAAA;cAAI0D,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ChE,OAAA;cAAG0D,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEjB,CAAC;AAAC9D,EAAA,CApRID,cAAc;EAAA,QACDtB,WAAW,EACXC,WAAW;AAAA;AAAA0G,EAAA,GAFxBrF,cAAc;AAsRpB,eAAeA,cAAc;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}