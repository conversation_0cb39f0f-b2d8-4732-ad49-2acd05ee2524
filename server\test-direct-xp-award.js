const mongoose = require('mongoose');
const User = require('./models/userModel');
const Exam = require('./models/examModel');
const Report = require('./models/reportModel');
require('dotenv').config();

// Import the exact function from the reports route
const enhancedXPService = require('./services/enhancedXPService');
const streakTrackingService = require('./services/streakTrackingService');

async function testDirectXPAward() {
  try {
    console.log('🔍 Testing Direct XP Award (Simulating Reports Route)...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');

    // Find a test user (non-admin)
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}\n`);

    // Find a test exam
    const testExam = await Exam.findOne().limit(1);
    if (!testExam) {
      console.log('❌ No test exam found');
      return;
    }

    console.log(`📝 Test Exam: ${testExam.name}`);
    console.log(`📚 Subject: ${testExam.subject || 'General'}\n`);

    // Create a mock quiz result
    const mockResult = {
      correctAnswers: [
        { name: 'Question 1', userAnswer: 'A' },
        { name: 'Question 2', userAnswer: 'B' },
        { name: 'Question 3', userAnswer: 'C' }
      ],
      wrongAnswers: [
        { name: 'Question 4', userAnswer: 'D' }
      ],
      verdict: 'Pass',
      score: 75,
      points: 30,
      totalQuestions: 4,
      timeSpent: 300
    };

    // Create a report first (simulating the reports route)
    console.log('📝 Creating report...');
    const newReport = new Report({
      user: testUser._id,
      exam: testExam._id,
      result: mockResult
    });
    await newReport.save();
    console.log('✅ Report saved successfully\n');

    // Now simulate the exact XP calculation from the reports route
    console.log('💰 Starting XP calculation (simulating reports route)...');

    // Get all reports for this user to calculate accurate stats
    const allReports = await Report.find({ user: testUser._id }).populate('exam');

    // Calculate total points from all reports (legacy system)
    const totalPoints = allReports.reduce((sum, report) => {
      return sum + (report.result.points || 0);
    }, 0);

    // Calculate average score
    const scores = allReports.map(report => report.result.score || 0);
    const averageScore = scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;

    // Calculate streak
    let currentStreak = 0;
    let bestStreak = testUser.bestStreak || 0;

    // Check recent reports for current streak (sorted by creation date)
    const recentReports = allReports.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    for (const report of recentReports) {
      if (report.result.verdict === 'Pass') {
        currentStreak++;
      } else {
        break;
      }
    }

    // Update best streak if current is higher
    bestStreak = Math.max(bestStreak, currentStreak);

    // Update user document with legacy stats
    await User.findByIdAndUpdate(testUser._id, {
      totalQuizzesTaken: allReports.length,
      totalPointsEarned: totalPoints,
      averageScore: Math.round(averageScore * 100) / 100,
      currentStreak: currentStreak,
      bestStreak: bestStreak
    });

    console.log('📊 Updated legacy stats:', {
      totalQuizzes: allReports.length,
      totalPoints,
      averageScore: Math.round(averageScore * 100) / 100,
      currentStreak,
      bestStreak
    });

    // ENHANCED XP SYSTEM: Update streaks and calculate XP

    // 1. Update login streak (if user hasn't logged in today)
    await streakTrackingService.updateLoginStreak(testUser._id);

    // 2. Update quiz streaks and performance tracking
    const streakResult = await streakTrackingService.updateQuizStreaks(testUser._id, mockResult, testExam);

    // 3. Determine if this is first attempt for this exam
    const previousAttempts = allReports.filter(r =>
      r.exam && r.exam._id.toString() === testExam._id.toString()
    );
    const isFirstAttempt = previousAttempts.length <= 1; // Current attempt is included

    // Get previous score for improvement calculation
    const previousScore = previousAttempts.length > 1 ?
      previousAttempts[previousAttempts.length - 2].result.score : null;

    console.log('🎯 XP calculation parameters:', {
      isFirstAttempt,
      previousScore,
      score: mockResult.score,
      verdict: mockResult.verdict
    });

    // 4. Calculate XP using enhanced system
    const xpResult = await enhancedXPService.calculateQuizXP({
      userId: testUser._id,
      examData: {
        _id: testExam._id,
        subject: testExam.subject || testExam.category || 'General',
        difficulty: testExam.difficulty || testExam.difficultyLevel || 'medium',
        duration: testExam.duration || 30,
        name: testExam.name
      },
      result: mockResult,
      timeSpent: mockResult.timeSpent || 0,
      isFirstAttempt: isFirstAttempt,
      previousScore: previousScore
    });

    console.log('💰 XP Calculation Result:', {
      xpAwarded: xpResult.xpAwarded,
      breakdown: xpResult.breakdown
    });

    // 5. Award XP to user
    const xpAwardResult = await enhancedXPService.awardXP({
      userId: testUser._id,
      xpAmount: xpResult.xpAwarded,
      transactionType: 'quiz_completion',
      sourceId: testExam._id,
      sourceModel: 'exams',
      breakdown: xpResult.breakdown,
      quizData: {
        examId: testExam._id,
        subject: testExam.subject || testExam.category || 'General',
        difficulty: testExam.difficulty || testExam.difficultyLevel || 'medium',
        questionsTotal: (mockResult.correctAnswers?.length || 0) + (mockResult.wrongAnswers?.length || 0),
        questionsCorrect: mockResult.correctAnswers?.length || 0,
        timeSpent: mockResult.timeSpent || 0,
        score: mockResult.score || 0,
        isFirstAttempt: isFirstAttempt,
      },
      metadata: {
        reportId: newReport._id,
        verdict: mockResult.verdict,
        streakInfo: streakResult,
        testRun: true
      }
    });

    console.log('\n🏆 XP Award Result:', {
      success: xpAwardResult.success,
      xpAwarded: xpAwardResult.xpAwarded,
      newTotalXP: xpAwardResult.newTotalXP,
      levelUp: xpAwardResult.levelUp,
      newLevel: xpAwardResult.newLevel
    });

    // Verify the user's XP was actually updated
    const updatedUser = await User.findById(testUser._id);
    console.log('\n✅ User verification:');
    console.log(`📊 Updated XP: ${updatedUser.totalXP}`);
    console.log(`🎯 Updated Level: ${updatedUser.currentLevel}`);
    console.log(`🔥 Updated Streak: ${updatedUser.currentStreak}`);

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the test
testDirectXPAward();
