{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n\n  // Also try to get user from localStorage as backup\n  const backupUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use Redux user first, then localStorage backup\n  const currentUser = user || backupUser;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    return currentUser && String(userId) === String(currentUser._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user._id)) return;\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite !important;\n          border: 4px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.4)) !important;\n          box-shadow: 0 0 30px rgba(255, 215, 0, 0.8) !important;\n          transform: scale(1.02) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1016,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 17\n                }, this), userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1075,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    console.log('🎯 DEBUGGING USER POSITION');\n                    console.log('🎯 Your user data:', currentUser);\n                    console.log('🎯 Your user ID:', currentUser === null || currentUser === void 0 ? void 0 : currentUser._id);\n                    console.log('🎯 Your user name:', currentUser === null || currentUser === void 0 ? void 0 : currentUser.name);\n                    console.log('🎯 Your XP:', currentUser === null || currentUser === void 0 ? void 0 : currentUser.totalXP);\n                    console.log('🎯 Total ranking data:', rankingData.length);\n\n                    // Check if user exists in ranking data with different matching\n                    const exactMatch = rankingData.find(u => u._id === (currentUser === null || currentUser === void 0 ? void 0 : currentUser._id));\n                    const stringMatch = rankingData.find(u => String(u._id) === String(currentUser === null || currentUser === void 0 ? void 0 : currentUser._id));\n                    const nameMatch = rankingData.find(u => u.name === (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name));\n                    console.log('🎯 Exact ID match:', exactMatch);\n                    console.log('🎯 String ID match:', stringMatch);\n                    console.log('🎯 Name match:', nameMatch);\n\n                    // Show first 10 users for comparison\n                    console.log('🎯 First 10 users in ranking:');\n                    rankingData.slice(0, 10).forEach((user, index) => {\n                      console.log(`  ${index + 1}. ${user.name} (ID: ${user._id}) - XP: ${user.totalXP}`);\n                    });\n                    setShowFindMe(true);\n\n                    // If we found the user by any method, scroll to them\n                    const foundUser = exactMatch || stringMatch || nameMatch;\n                    if (foundUser) {\n                      const userPosition = rankingData.findIndex(u => u._id === foundUser._id || String(u._id) === String(foundUser._id));\n                      console.log('🎯 Found user at position:', userPosition + 1);\n                      setTimeout(() => {\n                        const userCard = document.querySelector(`[data-user-id=\"${foundUser._id}\"]`);\n                        if (userCard) {\n                          userCard.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'center'\n                          });\n                          console.log('🎯 Scrolled to your card!');\n                        } else {\n                          console.log('🎯 Card not found in DOM, scrolling to approximate position');\n                          // Scroll to approximate position in the list\n                          const allCards = document.querySelectorAll('.ranking-card');\n                          if (allCards[userPosition]) {\n                            allCards[userPosition].scrollIntoView({\n                              behavior: 'smooth',\n                              block: 'center'\n                            });\n                          }\n                        }\n                      }, 500);\n                    } else {\n                      console.log('🎯 USER NOT FOUND IN RANKING DATA!');\n                      console.log('🎯 This means either:');\n                      console.log('  1. Your quiz results haven\\'t been processed');\n                      console.log('  2. Your account is filtered out (admin/blocked)');\n                      console.log('  3. There\\'s a data sync issue');\n                    }\n\n                    // Auto-hide after 5 seconds\n                    setTimeout(() => setShowFindMe(false), 5000);\n                  },\n                  className: \"flex items-center gap-3 px-8 py-4 bg-red-500 text-white rounded-xl font-black text-xl shadow-2xl border-4 border-yellow-400 animate-pulse\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FF0000, #FF6B00)',\n                    boxShadow: '0 0 30px rgba(255, 0, 0, 0.8)',\n                    zIndex: 9999\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-8 h-8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83C\\uDFAF FIND ME \\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1100,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1244,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1271,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1246,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1287,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1209,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1304,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1300,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1204,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1197,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1321,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1179,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1338,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1327,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1357,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1361,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1380,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1381,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1401,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1420,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1421,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1400,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1469,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1463,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1531,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1532,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1536,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1547,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1519,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1479,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1385,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1384,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1379,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1372,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1572,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1577,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1567,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${isCurrentUser(topPerformers[1]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[1]._id) && showFindMe ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))' : isCurrentUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1656,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1655,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1670,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1673,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1695,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1701,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1685,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1684,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1716,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1723,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1728,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1731,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1727,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1667,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1660,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1613,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${isCurrentUser(topPerformers[0]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '320px',\n                    transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[0]._id) && showFindMe ? 'brightness(1.8) saturate(1.6) drop-shadow(0 0 60px rgba(255, 215, 0, 1)) hue-rotate(15deg)' : isCurrentUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1787,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1786,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1796,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1791,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1813,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1816,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1839,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1845,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1829,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1865,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1858,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1828,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1872,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1883,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1871,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1912,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1898,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1917,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1928,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1929,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1927,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1933,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1926,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1937,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1938,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1936,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1942,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1935,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1925,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1916,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1807,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1800,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1742,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${isCurrentUser(topPerformers[2]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[2]._id) && showFindMe ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))' : isCurrentUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1996,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1995,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2010,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2013,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2035,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2041,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2025,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2024,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2056,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2063,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2068,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2071,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2067,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2007,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2000,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1953,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1610,\n                columnNumber: 19\n              }, this), currentUserLeague && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.3,\n                  duration: 0.8\n                },\n                className: \"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-white mb-2\",\n                    children: [\"Your League: \", currentUserLeague.league.leagueIcon, \" \", currentUserLeague.league.title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2090,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/80 text-sm\",\n                    children: [\"Rank #\", currentUserLeague.userRank, \" of \", currentUserLeague.totalInLeague, \" in your league\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2093,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2089,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold\",\n                      children: currentUserLeague.league.promotionXP > 0 ? `${currentUserLeague.league.promotionXP - ((user === null || user === void 0 ? void 0 : user.totalXP) || 0)} XP` : 'Max League'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2100,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"To Promotion\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2106,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2099,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold\",\n                      children: currentUserLeague.totalInLeague\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2109,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2110,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2108,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold\",\n                      children: [\"#\", currentUserLeague.userRank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2113,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Rank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2114,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2112,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2098,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2083,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1593,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2136,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2150,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2153,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2135,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: isCurrentUser && showFindMe ? 'scale(1.1) translateY(-5px)' : isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                        filter: isCurrentUser && showFindMe ? 'brightness(1.5) saturate(1.5) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : isCurrentUser ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: isCurrentUser && showFindMe ? '6px solid #FFD700' : isCurrentUser ? '4px solid #FFD700' : 'none',\n                        borderRadius: isCurrentUser ? '16px' : '0px',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2224,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2230,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2229,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full w-full h-full\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2255,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2261,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2245,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2282,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2275,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2244,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2227,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2293,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2304,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2292,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2322,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2290,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2289,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2331,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2351,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2352,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2344,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2361,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2362,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2354,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2343,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2329,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2217,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2211,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2171,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2165,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2164,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2128,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2387,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2401,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2386,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2427,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2438,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2448,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2437,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2426,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2453,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2425,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${isCurrentUser && showFindMe ? 'find-me-highlight ring-4 ring-yellow-400/80' : isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2493,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2496,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser && showFindMe ? 'ring-2 ring-yellow-400 ring-opacity-100' : isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                                  style: {\n                                    background: '#f0f0f0',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    width: '40px',\n                                    height: '40px'\n                                  },\n                                  children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: champion.profilePicture,\n                                    alt: champion.name,\n                                    className: \"object-cover rounded-full w-full h-full\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2525,\n                                    columnNumber: 47\n                                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                    style: {\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    },\n                                    children: champion.name.charAt(0).toUpperCase()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2531,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2515,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2551,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2544,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2508,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2563,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2557,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2567,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2572,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2575,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2571,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2490,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2484,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2470,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2464,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2589,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2588,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2414,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2407,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2378,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2621,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2624,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2620,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2627,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2630,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2626,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2633,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2636,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2632,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2619,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2639,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2613,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2607,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2655,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2660,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2665,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2654,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2648,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2688,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2684,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2690,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2695,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2703,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2683,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2677,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2721,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2727,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2716,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1589,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1014,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"7ka3d9M68krY94Rgboqa09JRbY0=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "backupUser", "userData", "localStorage", "getItem", "JSON", "parse", "currentUser", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "console", "log", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "totalQuizzesTaken", "transformedData", "map", "index", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "userInLeague", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "position", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "scrollToUser", "podiumSection", "userInCurrentView", "userElement", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "y", "x", "delay", "left", "top", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "foundUser", "userPosition", "userCard", "allCards", "querySelectorAll", "zIndex", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_leagueGroups$leagueK2", "isSelected", "userCount", "border", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "height", "borderRadius", "width", "src", "alt", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n\n  // Also try to get user from localStorage as backup\n  const backupUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use Redux user first, then localStorage backup\n  const currentUser = user || backupUser;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return currentUser && String(userId) === String(currentUser._id);\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!user?._id) return;\n\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite !important;\n          border: 4px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.4)) !important;\n          box-shadow: 0 0 30px rgba(255, 215, 0, 0.8) !important;\n          transform: scale(1.02) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\">\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n\n\n                {/* Find Me Button - Always Visible */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => {\n                    console.log('🎯 DEBUGGING USER POSITION');\n                    console.log('🎯 Your user data:', currentUser);\n                    console.log('🎯 Your user ID:', currentUser?._id);\n                    console.log('🎯 Your user name:', currentUser?.name);\n                    console.log('🎯 Your XP:', currentUser?.totalXP);\n                    console.log('🎯 Total ranking data:', rankingData.length);\n\n                    // Check if user exists in ranking data with different matching\n                    const exactMatch = rankingData.find(u => u._id === currentUser?._id);\n                    const stringMatch = rankingData.find(u => String(u._id) === String(currentUser?._id));\n                    const nameMatch = rankingData.find(u => u.name === currentUser?.name);\n\n                    console.log('🎯 Exact ID match:', exactMatch);\n                    console.log('🎯 String ID match:', stringMatch);\n                    console.log('🎯 Name match:', nameMatch);\n\n                    // Show first 10 users for comparison\n                    console.log('🎯 First 10 users in ranking:');\n                    rankingData.slice(0, 10).forEach((user, index) => {\n                      console.log(`  ${index + 1}. ${user.name} (ID: ${user._id}) - XP: ${user.totalXP}`);\n                    });\n\n                    setShowFindMe(true);\n\n                    // If we found the user by any method, scroll to them\n                    const foundUser = exactMatch || stringMatch || nameMatch;\n                    if (foundUser) {\n                      const userPosition = rankingData.findIndex(u =>\n                        u._id === foundUser._id || String(u._id) === String(foundUser._id)\n                      );\n                      console.log('🎯 Found user at position:', userPosition + 1);\n\n                      setTimeout(() => {\n                        const userCard = document.querySelector(`[data-user-id=\"${foundUser._id}\"]`);\n                        if (userCard) {\n                          userCard.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                          console.log('🎯 Scrolled to your card!');\n                        } else {\n                          console.log('🎯 Card not found in DOM, scrolling to approximate position');\n                          // Scroll to approximate position in the list\n                          const allCards = document.querySelectorAll('.ranking-card');\n                          if (allCards[userPosition]) {\n                            allCards[userPosition].scrollIntoView({ behavior: 'smooth', block: 'center' });\n                          }\n                        }\n                      }, 500);\n                    } else {\n                      console.log('🎯 USER NOT FOUND IN RANKING DATA!');\n                      console.log('🎯 This means either:');\n                      console.log('  1. Your quiz results haven\\'t been processed');\n                      console.log('  2. Your account is filtered out (admin/blocked)');\n                      console.log('  3. There\\'s a data sync issue');\n                    }\n\n                    // Auto-hide after 5 seconds\n                    setTimeout(() => setShowFindMe(false), 5000);\n                  }}\n                  className=\"flex items-center gap-3 px-8 py-4 bg-red-500 text-white rounded-xl font-black text-xl shadow-2xl border-4 border-yellow-400 animate-pulse\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FF0000, #FF6B00)',\n                    boxShadow: '0 0 30px rgba(255, 0, 0, 0.8)',\n                    zIndex: 9999\n                  }}\n                >\n                  <TbTarget className=\"w-8 h-8\" />\n                  <span>🎯 FIND ME 🎯</span>\n                </motion.button>\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          isCurrentUser(topPerformers[1]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[1]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[1]._id) && showFindMe\n                            ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))'\n                            : isCurrentUser(topPerformers[1]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          isCurrentUser(topPerformers[0]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[0]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '320px',\n                          transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[0]._id) && showFindMe\n                            ? 'brightness(1.8) saturate(1.6) drop-shadow(0 0 60px rgba(255, 215, 0, 1)) hue-rotate(15deg)'\n                            : isCurrentUser(topPerformers[0]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          isCurrentUser(topPerformers[2]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[2]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[2]._id) && showFindMe\n                            ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))'\n                            : isCurrentUser(topPerformers[2]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n                  {/* League Information Section */}\n                  {currentUserLeague && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 1.3, duration: 0.8 }}\n                      className=\"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\"\n                    >\n                      <div className=\"text-center mb-4\">\n                        <h3 className=\"text-xl font-bold text-white mb-2\">\n                          Your League: {currentUserLeague.league.leagueIcon} {currentUserLeague.league.title}\n                        </h3>\n                        <p className=\"text-white/80 text-sm\">\n                          Rank #{currentUserLeague.userRank} of {currentUserLeague.totalInLeague} in your league\n                        </p>\n                      </div>\n\n                      <div className=\"flex justify-center gap-4 text-sm\">\n                        <div className=\"bg-green-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-green-400 font-bold\">\n                            {currentUserLeague.league.promotionXP > 0 ?\n                              `${currentUserLeague.league.promotionXP - (user?.totalXP || 0)} XP` :\n                              'Max League'\n                            }\n                          </div>\n                          <div className=\"text-white/80 text-xs\">To Promotion</div>\n                        </div>\n                        <div className=\"bg-blue-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-blue-400 font-bold\">{currentUserLeague.totalInLeague}</div>\n                          <div className=\"text-white/80 text-xs\">League Members</div>\n                        </div>\n                        <div className=\"bg-purple-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-purple-400 font-bold\">#{currentUserLeague.userRank}</div>\n                          <div className=\"text-white/80 text-xs\">League Rank</div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                isCurrentUser && showFindMe\n                                  ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                                  : isCurrentUser\n                                    ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                    : ''\n                              }`}\n                              style={{\n                                transform: isCurrentUser && showFindMe\n                                  ? 'scale(1.1) translateY(-5px)'\n                                  : isCurrentUser\n                                    ? 'scale(1.05)'\n                                    : 'scale(1)',\n                                filter: isCurrentUser && showFindMe\n                                  ? 'brightness(1.5) saturate(1.5) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                                  : isCurrentUser\n                                    ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                    : 'none',\n                                transition: 'all 0.3s ease',\n                                border: isCurrentUser && showFindMe\n                                  ? '6px solid #FFD700'\n                                  : isCurrentUser\n                                    ? '4px solid #FFD700'\n                                    : 'none',\n                                borderRadius: isCurrentUser ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                        style={{\n                                          background: '#f0f0f0',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      >\n                                        {champion.profilePicture ? (\n                                          <img\n                                            src={champion.profilePicture}\n                                            alt={champion.name}\n                                            className=\"object-cover rounded-full w-full h-full\"\n                                          />\n                                        ) : (\n                                          <div\n                                            className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                            style={{\n                                              background: '#25D366',\n                                              color: '#FFFFFF',\n                                              fontSize: '12px'\n                                            }}\n                                          >\n                                            {champion.name.charAt(0).toUpperCase()}\n                                          </div>\n                                        )}\n                                      </div>\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      isCurrentUser && showFindMe\n                                        ? 'find-me-highlight ring-4 ring-yellow-400/80'\n                                        : isCurrentUser\n                                          ? 'ring-2 ring-yellow-400/60'\n                                          : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser && showFindMe\n                                            ? 'ring-2 ring-yellow-400 ring-opacity-100'\n                                            : isCurrentUser\n                                              ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                              : ''\n                                        }`}>\n                                          <div\n                                            className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                            style={{\n                                              background: '#f0f0f0',\n                                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          >\n                                            {champion.profilePicture ? (\n                                              <img\n                                                src={champion.profilePicture}\n                                                alt={champion.name}\n                                                className=\"object-cover rounded-full w-full h-full\"\n                                              />\n                                            ) : (\n                                              <div\n                                                className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                                style={{\n                                                  background: '#25D366',\n                                                  color: '#FFFFFF',\n                                                  fontSize: '16px'\n                                                }}\n                                              >\n                                                {champion.name.charAt(0).toUpperCase()}\n                                              </div>\n                                            )}\n                                          </div>\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;;EAEnC;EACA,MAAMC,UAAU,GAAG,CAAC,MAAM;IACxB,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMK,WAAW,GAAGP,IAAI,IAAIC,UAAU;EACtC,MAAMO,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM0E,UAAU,GAAGxE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMyE,SAAS,GAAGzE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM0E,cAAc,GAAG1E,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2E,aAAa,GAAG3E,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM4E,WAAW,GAAG5E,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM6E,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhF,OAAO;MACbiF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEtE,SAAS;MACfuE,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEhE,QAAQ;MACdiE,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEjE,OAAO;MACbkE,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEjF,QAAQ;MACdkF,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzE,OAAO;MACb0E,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/E,MAAM;MACZgF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEvE,QAAQ;MACdwE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIzE,KAAK,IAAK;IACpC,MAAM0E,OAAO,GAAG,CAAC,CAAC;IAElB1E,KAAK,CAAC2E,OAAO,CAAC1E,IAAI,IAAI;MACpB,MAAM2E,UAAU,GAAGT,aAAa,CAAClE,IAAI,CAAC4E,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClB5E,KAAK,EAAE;QACT,CAAC;MACH;MACA0E,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAACrE,KAAK,CAAC8E,IAAI,CAAC;QACpC,GAAG7E,IAAI;QACP8E,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAACjF,KAAK,CAACkF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAE9E,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMoE,UAAU,GAAGT,aAAa,CAAC3D,WAAW,CAACqE,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMjD,WAAW,GAAG0D,QAAQ,CAACC,MAAM,CAACtF,IAAI,IAAI;MAC1C,MAAMoE,MAAM,GAAGF,aAAa,CAAClE,IAAI,CAAC4E,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClB5E,KAAK,EAAE4B,WAAW;MAClB4D,QAAQ,EAAE5D,WAAW,CAAC6D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKnF,WAAW,CAACmF,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAEhE,WAAW,CAACiE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIb,SAAS,IAAK;IAAA,IAAAc,qBAAA;IACxCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEhB,SAAS,CAAC;;IAE7C;IACAhD,iBAAiB,CAACgD,SAAS,CAAC;IAC5BlD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAAkE,qBAAA,GAAA7D,YAAY,CAAC+C,SAAS,CAAC,cAAAc,qBAAA,uBAAvBA,qBAAA,CAAyB/F,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACAkG,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBpB,SAAU,IAAG,CAAC,IACvDmB,QAAQ,CAACE,cAAc,CAAE,UAASrB,SAAU,EAAC,CAAC,IAC9C7C,UAAU,CAACmE,OAAO,CAACtB,SAAS,CAAC;MAElD,IAAIkB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAAC1B,MAAM,CAAClB,MAAM,IAAInC,YAAY,CAACmC,MAAM,CAAC,IAAInC,YAAY,CAACmC,MAAM,CAAC,CAACrE,KAAK,CAAC6F,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAED;EACA,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFrG,UAAU,CAAC,IAAI,CAAC;MAChBmF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMkB,qBAAqB,GAAG,MAAM9H,gBAAgB,CAAC;UACnD+H,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAApH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqH,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMyB,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAAClC,MAAM,CAACpF,QAAQ,IAC5DA,QAAQ,CAAC0E,OAAO,IAAI1E,QAAQ,CAAC0E,OAAO,GAAG,CAAC,IACxC1E,QAAQ,CAACwH,iBAAiB,IAAIxH,QAAQ,CAACwH,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGF,YAAY,CAACG,GAAG,CAAC,CAAC1H,QAAQ,EAAE2H,KAAK,MAAM;YAC7DnC,GAAG,EAAExF,QAAQ,CAACwF,GAAG;YACjBoC,IAAI,EAAE5H,QAAQ,CAAC4H,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAE7H,QAAQ,CAAC6H,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE9H,QAAQ,CAAC8H,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEnH,QAAQ,CAACmH,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAE/H,QAAQ,CAACgI,YAAY,IAAI,EAAE;YAC3CtD,OAAO,EAAE1E,QAAQ,CAAC0E,OAAO,IAAI,CAAC;YAC9B8C,iBAAiB,EAAExH,QAAQ,CAACwH,iBAAiB,IAAI,CAAC;YAClDS,YAAY,EAAEjI,QAAQ,CAACiI,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElI,QAAQ,CAACkI,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEnI,QAAQ,CAACmI,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEpI,QAAQ,CAACoI,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEZ,aAAa,CAAChE,QAAQ,CAAC0E,OAAO,IAAI,CAAC,CAAC;YAC1C4D,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEvI,QAAQ,CAACuI,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAExI,QAAQ,CAACwI,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEzI,QAAQ,CAACyI,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAE1I,QAAQ,CAAC0I,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAE3I,QAAQ,CAAC2I,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAE5I,QAAQ,CAAC4I,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHrI,cAAc,CAACiH,eAAe,CAAC;;UAE/B;UACA,MAAMqB,aAAa,GAAGrB,eAAe,CAACnC,SAAS,CAACyD,IAAI,IAAIA,IAAI,CAACvD,GAAG,MAAK1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG,EAAC;UAC/E5E,kBAAkB,CAACkI,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAIhJ,IAAI,EAAE;YACR,MAAMkJ,cAAc,GAAG9D,wBAAwB,CAACuC,eAAe,EAAE3H,IAAI,CAAC;YACtE0B,oBAAoB,CAACwH,cAAc,CAAC;YACpCtH,cAAc,CAAC,CAAAsH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnJ,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAMoJ,OAAO,GAAG3E,kBAAkB,CAACmD,eAAe,CAAC;UACnDzF,eAAe,CAACiH,OAAO,CAAC;UAExBvI,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAOwI,OAAO,EAAE;QAChBrD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoD,OAAO,CAAC;MACpE;;MAEA;MACArD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqD,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqD,eAAe,GAAG,MAAMlK,uBAAuB,CAAC,CAAC;QACjD4G,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsD,aAAa,GAAG,MAAMhK,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOiK,KAAK,EAAE;QACdxD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuD,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMhK,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOkK,SAAS,EAAE;UAClBzD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwD,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAC/B,OAAO,IAAI+B,aAAa,CAAC9B,IAAI,EAAE;QAChEzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyD,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC9B,OAAO,IAAI8B,eAAe,CAAC7B,IAAI,EAAE;UACtE6B,eAAe,CAAC7B,IAAI,CAAC9C,OAAO,CAACuE,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAT,IAAI,CAACjJ,IAAI,cAAA0J,UAAA,uBAATA,UAAA,CAAWhE,GAAG,KAAIuD,IAAI,CAACU,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVF,cAAc,CAACE,MAAM,CAAC,GAAGV,IAAI,CAACW,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAjC,eAAe,GAAG2B,aAAa,CAAC9B,IAAI,CACjClC,MAAM,CAACpF,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACwF,GAAG,IAAIxF,QAAQ,CAAC2J,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EjC,GAAG,CAAC,CAAC1H,QAAQ,EAAE2H,KAAK,KAAK;UACxB;UACA,MAAMiC,WAAW,GAAGL,cAAc,CAACvJ,QAAQ,CAACwF,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIqE,YAAY,GAAGD,WAAW,CAAClE,MAAM,IAAI1F,QAAQ,CAACwH,iBAAiB,IAAI,CAAC;UACxE,IAAIsC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIjC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAG7J,QAAQ,CAACiI,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAAClE,MAAM,IAAI1F,QAAQ,CAACqK,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACxK,QAAQ,CAACqK,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC1H,GAAG,CAAC,EAAE,EAAE0H,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIvK,QAAQ,CAACqK,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BrC,YAAY,GAAGkC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACnC,YAAY,GAAG4B,YAAY,CAAC;YAEpDhE,OAAO,CAACC,GAAG,CAAE,0BAAyB9F,QAAQ,CAAC4H,IAAK,KAAI0C,gBAAiB,aAAYG,gBAAiB,cAAazK,QAAQ,CAACqK,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI3F,OAAO,GAAG1E,QAAQ,CAAC0E,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAI1E,QAAQ,CAACqK,WAAW,EAAE;cACxB;cACA3F,OAAO,GAAGyF,IAAI,CAACK,KAAK,CAClBxK,QAAQ,CAACqK,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAnF,OAAO,GAAGyF,IAAI,CAACK,KAAK,CACjBvC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGlI,QAAQ,CAACkI,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGnI,QAAQ,CAACmI,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAAClE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIgF,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACpF,OAAO,CAACyF,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZvC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACpC,UAAU,EAAEuC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFxC,aAAa,GAAGwC,UAAU;UAC5B,CAAC,MAAM,IAAI1K,QAAQ,CAACqK,WAAW,IAAI,CAACnC,aAAa,EAAE;YACjD;YACA,MAAMyC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAG7J,QAAQ,CAACqK,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBzC,aAAa,GAAGiC,IAAI,CAAC1H,GAAG,CAACoH,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxExC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACrC,aAAa,EAAEiC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLnF,GAAG,EAAExF,QAAQ,CAACwF,GAAG;YACjBoC,IAAI,EAAE5H,QAAQ,CAAC4H,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAE7H,QAAQ,CAAC6H,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE9H,QAAQ,CAAC8H,KAAK,IAAI,EAAE;YAC3BX,KAAK,EAAEnH,QAAQ,CAACmH,KAAK,IAAI,EAAE;YAC3BY,cAAc,EAAE/H,QAAQ,CAAC+H,cAAc,IAAI,EAAE;YAC7CrD,OAAO,EAAEA,OAAO;YAChB8C,iBAAiB,EAAEqC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEpI,QAAQ,CAACoI,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5B4D,UAAU,EAAE,IAAI;YAChB;YACAsC,cAAc,EAAE5K,QAAQ,CAACqK,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAAClE,MAAM,GAAG,CAAC;YAClCmD,UAAU,EAAEe,WAAW,CAAClE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG1F,QAAQ,CAACqK,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA5C,eAAe,CAAC1C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACA+C,eAAe,CAACjD,OAAO,CAAC,CAAC1E,IAAI,EAAE6H,KAAK,KAAK;UACvC7H,IAAI,CAACuI,IAAI,GAAGV,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFnH,cAAc,CAACiH,eAAe,CAAC;;QAE/B;QACA,IAAIpC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIvF,IAAI,EAAE;UACR;UACAuF,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACyD,IAAI,IAAIA,IAAI,CAACvD,GAAG,KAAK1F,IAAI,CAAC0F,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACyD,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACvD,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIvF,IAAI,CAAC8H,IAAI,EAAE;YAChCvC,QAAQ,GAAGoC,eAAe,CAACnC,SAAS,CAACyD,IAAI,IAAIA,IAAI,CAACnB,IAAI,KAAK9H,IAAI,CAAC8H,IAAI,CAAC;UACvE;QACF;QAEAhH,kBAAkB,CAACyE,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIvF,IAAI,EAAE;UACR,MAAMkJ,cAAc,GAAG9D,wBAAwB,CAACuC,eAAe,EAAE3H,IAAI,CAAC;UACtE0B,oBAAoB,CAACwH,cAAc,CAAC;UACpCtH,cAAc,CAAC,CAAAsH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnJ,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAMoJ,OAAO,GAAG3E,kBAAkB,CAACmD,eAAe,CAAC;QACnDzF,eAAe,CAACiH,OAAO,CAAC;;QAExB;QACApD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CzF,WAAW,EAAEP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H,IAAI;UACvB6B,MAAM,EAAE3J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG;UACjBuF,UAAU,EAAE,QAAOjL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG;UAC5BwF,OAAO,EAAE,CAAAlL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6J,IAAI,MAAK,OAAO,KAAI7J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkL,OAAO;UAChDC,MAAM,EAAEnL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,OAAO;UACrBoE,aAAa,EAAEzD,QAAQ;UACvB6F,gBAAgB,EAAE7F,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrD8F,gBAAgB,EAAE1D,eAAe,CAAC/B,MAAM;UACxC0F,eAAe,EAAE3D,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACnC,CAAC,KAAK;YAAE+F,EAAE,EAAE/F,CAAC,CAACC,GAAG;YAAE+F,IAAI,EAAE,OAAOhG,CAAC,CAACC,GAAG;YAAEoC,IAAI,EAAErC,CAAC,CAACqC;UAAK,CAAC,CAAC,CAAC;UACxG4D,UAAU,EAAE/D,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACvD,GAAG,MAAK1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG,EAAC;UAChEkG,WAAW,EAAEjE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACvD,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG,CAAC,CAAC;UACjFmG,SAAS,EAAElE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACnB,IAAI,MAAK9H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8H,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMgE,WAAW,GAAG;UAClBlC,OAAO,EAAEjC,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,SAAS,CAAC,CAACnD,MAAM;UACvEmG,aAAa,EAAEpE,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,eAAe,CAAC,CAACnD,MAAM;UACnFoG,SAAS,EAAErE,eAAe,CAACrC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,WAAW,CAAC,CAACnD;QACvE,CAAC;QAEDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2B,eAAe,CAAC/B,MAAM,EAAE,gBAAgB,CAAC;QACxFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8F,WAAW,CAAC;QAC5C/F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACnC,CAAC,KAAK;UACvEqC,IAAI,EAAErC,CAAC,CAACqC,IAAI;UACZ3D,EAAE,EAAEsB,CAAC,CAACb,OAAO;UACbqH,OAAO,EAAExG,CAAC,CAACiC,iBAAiB;UAC5BwE,GAAG,EAAEzG,CAAC,CAAC0C,YAAY;UACnBgE,MAAM,EAAE1G,CAAC,CAACsD;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLhD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCtF,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxB9C,OAAO,CAACoO,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDvL,OAAO,CAACuL,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR3I,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACduJ,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMoF,WAAW,GAAG7J,kBAAkB,CAAC6H,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG9J,kBAAkB,CAACoD,MAAM,CAAC,CAAC;IAC7FtE,oBAAoB,CAAC+K,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCpL,iBAAiB,CAACqL,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B3G,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DiB,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM0F,mBAAmB,GAAIC,KAAK,IAAK;MACrC7G,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE4G,KAAK,CAACC,MAAM,CAAC;MACnE;MACA5G,UAAU,CAAC,MAAM;QACfgB,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED6F,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,iBAAiB,CAAC;IACnDI,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXK,aAAa,CAACT,cAAc,CAAC;MAC7B;MACAO,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEP,iBAAiB,CAAC;MACtDI,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEN,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjP,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAIiC,YAAY,IAAIqC,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC2D,MAAM,GAAG,CAAC,IAAI,CAAC7D,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAACiD,SAAS,EAAEkI,UAAU,CAAC,IAAI5I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;QAClE,MAAMkL,YAAY,GAAGD,UAAU,CAACnN,KAAK,CAAC4L,IAAI,CAAClG,CAAC,IAAIuF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;QACnF,IAAIyH,YAAY,EAAE;UAChBpH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEhB,SAAS,CAAC;UACxDhD,iBAAiB,CAACgD,SAAS,CAAC;UAC5BlD,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAACsL,UAAU,CAACnN,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAEiC,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAMqL,aAAa,GAAG3M,WAAW,CAAC8K,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM8B,eAAe,GAAG5M,WAAW,CAAC8K,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACtN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0F,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAM6H,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAC/H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;IAC9F,IAAI6H,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAC5H,SAAS,CAACiI,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAC/H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACL+F,IAAI,EAAE,QAAQ;QACdkC,QAAQ,EAAED,cAAc;QACxBtJ,MAAM,EAAE,iBAAiB;QACzBY,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEkI,UAAU,CAAC,IAAI5I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;MAAA,IAAA2L,iBAAA;MAClE,MAAMT,YAAY,IAAAS,iBAAA,GAAGV,UAAU,CAACnN,KAAK,cAAA6N,iBAAA,uBAAhBA,iBAAA,CAAkBjC,IAAI,CAAClG,CAAC,IAAIuF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;MACpF,IAAIyH,YAAY,EAAE;QAChB,MAAMQ,QAAQ,GAAGT,UAAU,CAACnN,KAAK,CAACyF,SAAS,CAACC,CAAC,IAAIuF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACL+F,IAAI,EAAE,QAAQ;UACdkC,QAAQ,EAAEA,QAAQ;UAClBvJ,MAAM,EAAE8I,UAAU,CAAC/J,KAAK;UACxB6B,SAAS,EAAEA,SAAS;UACpB6I,UAAU,EAAEX,UAAU,CAACnN,KAAK,CAAC6F;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMkI,cAAc,GAAGR,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMS,aAAa,GAAIpE,MAAM,IAAK;IAChC,OAAOpJ,WAAW,IAAIyK,MAAM,CAACrB,MAAM,CAAC,KAAKqB,MAAM,CAACzK,WAAW,CAACmF,GAAG,CAAC;EAClE,CAAC;;EAED;EACAhI,SAAS,CAAC,MAAM;IACd,IAAI,EAACsC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0F,GAAG,GAAE;IAEhB,MAAMsI,YAAY,GAAGA,CAAA,KAAM;MACzB;MACA,MAAMT,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAC/H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;MAE9F,IAAI6H,UAAU,EAAE;QACd;QACA,MAAMU,aAAa,GAAG9H,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvE,IAAI6H,aAAa,EAAE;UACjBhI,UAAU,CAAC,MAAM;YACfgI,aAAa,CAAC1H,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM,IAAI/E,WAAW,CAACiE,MAAM,GAAG,CAAC,EAAE;QACjC;QACA,MAAMsI,iBAAiB,GAAGvM,WAAW,CAAC6L,IAAI,CAAC/H,CAAC,IAAIuF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,CAAC;QACnF,IAAIwI,iBAAiB,EAAE;UACrB;UACA,MAAMC,WAAW,GAAGhI,QAAQ,CAACC,aAAa,CAAE,kBAAiBpG,IAAI,CAAC0F,GAAI,IAAG,CAAC;UAC1E,IAAIyI,WAAW,EAAE;YACflI,UAAU,CAAC,MAAM;cACfkI,WAAW,CAAC5H,cAAc,CAAC;gBACzBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE,QAAQ;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM0H,KAAK,GAAGnI,UAAU,CAAC+H,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMK,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACpO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,GAAG,EAAE0H,aAAa,EAAEzL,WAAW,CAAC,CAAC;;EAE3C;EACA,MAAM2M,oBAAoB,GAAGA,CAAChG,kBAAkB,EAAEiG,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1ExI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCsC,kBAAkB;MAClBiG,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAIpG,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAACuG,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBnM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL0L,IAAI,EAAE,SAAS;UACfnM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL0L,IAAI,EAAE,SAAS;QACfnM,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI1C,OAAO,IAAIF,WAAW,CAACmF,MAAM,KAAK,CAAC,EAAE;IACvC,oBACEpG,OAAA;MAAKwP,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBzI,UAAU,EAAE;YAAE0I,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFtQ,OAAA;UAAGwP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEtQ,OAAA,CAAAE,SAAA;IAAAuP,QAAA,gBACEzP,OAAA;MAAAyP,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVtQ,OAAA;MAAKwP,SAAS,EAAC,kIAAkI;MAAAC,QAAA,gBAEjJzP,OAAA;QAAKwP,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CzP,OAAA;UAAKwP,SAAS,EAAC;QAA2H;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJtQ,OAAA;UAAKwP,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtKtQ,OAAA;UAAKwP,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnKtQ,OAAA;UAAKwP,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGNtQ,OAAA;QAAKwP,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGc,KAAK,CAAC,EAAE,CAAC,CAAC,CAACnI,GAAG,CAAC,CAACoI,CAAC,EAAEC,CAAC,kBACvBzQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAE9F,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnC8C,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFvI,UAAU,EAAE;YACV0I,QAAQ,EAAE,CAAC,GAAGlF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BkD,MAAM,EAAEC,QAAQ;YAChBW,KAAK,EAAE/F,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACF3F,KAAK,EAAE;YACL0J,IAAI,EAAG,GAAEhG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BgE,GAAG,EAAG,GAAEjG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfG2D,CAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtQ,OAAA;QAAKwP,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BrJ,UAAU,EAAE;YAAE0I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtEzP,OAAA;YAAKwP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCzP,OAAA;cAAKwP,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnIzP,OAAA;gBAAKwP,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGzP,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMnQ,QAAQ,CAAC,WAAW,CAAE;kBACrCwO,SAAS,EAAC,gNAAgN;kBAC1NrI,KAAK,EAAE;oBACLiK,QAAQ,EAAE9D,MAAM,CAAC+D,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFzP,OAAA,CAACjB,MAAM;oBAACyQ,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CtQ,OAAA;oBAAAyP,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGfhC,cAAc,iBACbtO,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BlB,SAAS,EAAC,mJAAmJ;kBAC7JrI,KAAK,EAAE;oBACLmK,UAAU,EAAEhD,cAAc,CAACrC,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/C7I,KAAK,EAAEkL,cAAc,CAACrC,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/D3E,SAAS,EAAE,oCAAoC;oBAC/C8J,QAAQ,EAAE9D,MAAM,CAAC+D,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAA5B,QAAA,gBAEFzP,OAAA,CAACvB,QAAQ;oBAAC+Q,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CtQ,OAAA;oBAAAyP,QAAA,EACGnB,cAAc,CAACrC,IAAI,KAAK,QAAQ,GAC5B,cAAaqC,cAAc,CAACH,QAAS,EAAC,GACtC,GAAEG,cAAc,CAAC1J,MAAO,KAAI0J,cAAc,CAACH,QAAS;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,eAKDtQ,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACb5K,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;oBACzCD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEzF,WAAW,CAAC;oBAC9CwF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEzF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmF,GAAG,CAAC;oBACjDK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEzF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuH,IAAI,CAAC;oBACpD/B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEzF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqE,OAAO,CAAC;oBAChDmB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEvF,WAAW,CAACmF,MAAM,CAAC;;oBAEzD;oBACA,MAAM8F,UAAU,GAAGjL,WAAW,CAACkL,IAAI,CAAClG,CAAC,IAAIA,CAAC,CAACC,GAAG,MAAKnF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmF,GAAG,EAAC;oBACpE,MAAMkG,WAAW,GAAGnL,WAAW,CAACkL,IAAI,CAAClG,CAAC,IAAIuF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAACzK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmF,GAAG,CAAC,CAAC;oBACrF,MAAMmG,SAAS,GAAGpL,WAAW,CAACkL,IAAI,CAAClG,CAAC,IAAIA,CAAC,CAACqC,IAAI,MAAKvH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuH,IAAI,EAAC;oBAErE/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0F,UAAU,CAAC;oBAC7C3F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4F,WAAW,CAAC;oBAC/C7F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6F,SAAS,CAAC;;oBAExC;oBACA9F,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5CvF,WAAW,CAAC8K,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC7G,OAAO,CAAC,CAAC1E,IAAI,EAAE6H,KAAK,KAAK;sBAChD9B,OAAO,CAACC,GAAG,CAAE,KAAI6B,KAAK,GAAG,CAAE,KAAI7H,IAAI,CAAC8H,IAAK,SAAQ9H,IAAI,CAAC0F,GAAI,WAAU1F,IAAI,CAAC4E,OAAQ,EAAC,CAAC;oBACrF,CAAC,CAAC;oBAEFpD,aAAa,CAAC,IAAI,CAAC;;oBAEnB;oBACA,MAAMuP,SAAS,GAAGrF,UAAU,IAAIE,WAAW,IAAIC,SAAS;oBACxD,IAAIkF,SAAS,EAAE;sBACb,MAAMC,YAAY,GAAGvQ,WAAW,CAAC+E,SAAS,CAACC,CAAC,IAC1CA,CAAC,CAACC,GAAG,KAAKqL,SAAS,CAACrL,GAAG,IAAIsF,MAAM,CAACvF,CAAC,CAACC,GAAG,CAAC,KAAKsF,MAAM,CAAC+F,SAAS,CAACrL,GAAG,CACnE,CAAC;sBACDK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgL,YAAY,GAAG,CAAC,CAAC;sBAE3D/K,UAAU,CAAC,MAAM;wBACf,MAAMgL,QAAQ,GAAG9K,QAAQ,CAACC,aAAa,CAAE,kBAAiB2K,SAAS,CAACrL,GAAI,IAAG,CAAC;wBAC5E,IAAIuL,QAAQ,EAAE;0BACZA,QAAQ,CAAC1K,cAAc,CAAC;4BAAEC,QAAQ,EAAE,QAAQ;4BAAEC,KAAK,EAAE;0BAAS,CAAC,CAAC;0BAChEV,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;wBAC1C,CAAC,MAAM;0BACLD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;0BAC1E;0BACA,MAAMkL,QAAQ,GAAG/K,QAAQ,CAACgL,gBAAgB,CAAC,eAAe,CAAC;0BAC3D,IAAID,QAAQ,CAACF,YAAY,CAAC,EAAE;4BAC1BE,QAAQ,CAACF,YAAY,CAAC,CAACzK,cAAc,CAAC;8BAAEC,QAAQ,EAAE,QAAQ;8BAAEC,KAAK,EAAE;4BAAS,CAAC,CAAC;0BAChF;wBACF;sBACF,CAAC,EAAE,GAAG,CAAC;oBACT,CAAC,MAAM;sBACLV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;sBACjDD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;sBACpCD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;sBAC7DD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;sBAChED,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;oBAChD;;oBAEA;oBACAC,UAAU,CAAC,MAAMzE,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;kBAC9C,CAAE;kBACFwN,SAAS,EAAC,2IAA2I;kBACrJrI,KAAK,EAAE;oBACLmK,UAAU,EAAE,0CAA0C;oBACtDhK,SAAS,EAAE,+BAA+B;oBAC1CsK,MAAM,EAAE;kBACV,CAAE;kBAAAnC,QAAA,gBAEFzP,OAAA,CAACnB,QAAQ;oBAAC2Q,SAAS,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCtQ,OAAA;oBAAAyP,QAAA,EAAM;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eAShBtQ,OAAA;kBAAKwP,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpIzP,OAAA,CAAC5B,MAAM,CAACyT,EAAE;oBACRrC,SAAS,EAAC,sCAAsC;oBAChDrI,KAAK,EAAE;sBACLmK,UAAU,EAAE,mDAAmD;sBAC/DQ,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCC,UAAU,EAAE,6BAA6B;sBACzClM,MAAM,EAAE;oBACV,CAAE;oBACF+J,OAAO,EAAE;sBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjC5J,UAAU,EAAE;sBAAE0I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAR,QAAA,EAC/C;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZtQ,OAAA;oBAAKwP,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvElI,iBAAiB,CAAC,CAAC,CAACa,GAAG,CAAE5C,SAAS,IAAK;sBAAA,IAAAyM,sBAAA;sBACtC,MAAMrN,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAM0M,UAAU,GAAG3P,cAAc,KAAKiD,SAAS;sBAC/C,MAAM2M,SAAS,GAAG,EAAAF,sBAAA,GAAAxP,YAAY,CAAC+C,SAAS,CAAC,cAAAyM,sBAAA,uBAAvBA,sBAAA,CAAyB1R,KAAK,CAAC6F,MAAM,KAAI,CAAC;sBAE5D,oBACEpG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;wBAETF,SAAS,EAAC,kCAAkC;wBAC5CwB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAAxB,QAAA,gBAE5BzP,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCQ,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BE,OAAO,EAAEA,CAAA,KAAM9K,kBAAkB,CAACb,SAAS,CAAE;0BAC7CgK,SAAS,EAAG,+GACV0C,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACH/K,KAAK,EAAE;4BACLmK,UAAU,EAAEY,UAAU,GACjB,2BAA0BtN,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7E8O,MAAM,EAAG,aAAYF,UAAU,GAAG,SAAS,GAAGtN,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzEyD,SAAS,EAAE4K,UAAU,GAChB,YAAWtN,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxC4D,SAAS,EAAE8K,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjDpM,MAAM,EAAEoM,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACFrC,OAAO,EAAEqC,UAAU,GAAG;4BACpB5K,SAAS,EAAE,CACR,YAAW1C,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACDyN,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACP5J,UAAU,EAAE;4BACV0I,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAEkC,UAAU,GAAGjC,QAAQ,GAAG,CAAC;4BACjCC,IAAI,EAAE;0BACR,CAAE;0BACFvM,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAWwO,SAAU,SAAS;0BAAA1C,QAAA,gBAEnEzP,OAAA;4BAAMwP,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE7K,MAAM,CAACb;0BAAU;4BAAAoM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChE4B,UAAU,iBACTlS,OAAA,CAAC5B,MAAM,CAACsR,GAAG;4BACTC,OAAO,EAAE;8BAAEsB,KAAK,EAAE,CAAC;8BAAEnB,MAAM,EAAE,CAAC,GAAG;8BAAEF,OAAO,EAAE;4BAAE,CAAE;4BAChDC,OAAO,EAAE;8BACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClBnB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBF,OAAO,EAAE,CAAC;8BACVtI,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACV4J,KAAK,EAAE;gCAAElB,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BAC3DJ,MAAM,EAAE;gCAAEC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAS,CAAC;8BACzD5I,SAAS,EAAE;gCAAEyI,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BACjEN,OAAO,EAAE;gCAAEG,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFP,SAAS,EAAC,8KAA8K;4BACxLrI,KAAK,EAAE;8BACLmK,UAAU,EAAE,mDAAmD;8BAC/Dc,MAAM,EAAE,iBAAiB;8BACzBR,MAAM,EAAE;4BACV,CAAE;4BAAAnC,QAAA,eAEFzP,OAAA,CAAC5B,MAAM,CAACiU,IAAI;8BACV7C,SAAS,EAAC,kCAAkC;8BAC5CK,OAAO,EAAE;gCACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClBnB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACFzI,UAAU,EAAE;gCACV0I,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChBC,IAAI,EAAE;8BACR,CAAE;8BAAAT,QAAA,EACH;4BAED;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACDtQ,OAAA;4BACEwP,SAAS,EAAC,2HAA2H;4BACrIrI,KAAK,EAAE;8BACLmK,UAAU,EAAE1M,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChBgO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED0C;0BAAS;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhBtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;0BACTF,SAAS,EAAC,aAAa;0BACvBwB,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAAxB,QAAA,eAE5BzP,OAAA;4BACEwP,SAAS,EAAC,mDAAmD;4BAC7DrI,KAAK,EAAE;8BACL/D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByO,UAAU,EAAG,eAAcpN,MAAM,CAACpB,WAAY,EAAC;8BAC/C8N,UAAU,EAAG,GAAE1M,MAAM,CAACf,WAAY,IAAG;8BACrCuO,MAAM,EAAG,aAAYxN,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAA4L,QAAA,EAED7K,MAAM,CAACjB;0BAAK;4BAAAwM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GR9K,SAAS;wBAAA2K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENtQ,OAAA;oBAAGwP,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGNtQ,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEnB,MAAM,EAAE;kBAAI,CAAE;kBACzCoB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE1J,gBAAiB;kBAC1B6K,QAAQ,EAAEnR,OAAQ;kBAClBqO,SAAS,EAAC,qNAAqN;kBAC/NrI,KAAK,EAAE;oBACLiK,QAAQ,EAAE9D,MAAM,CAAC+D,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFzP,OAAA,CAAChB,SAAS;oBAACwQ,SAAS,EAAG,yBAAwBrO,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAgP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFtQ,OAAA;oBAAAyP,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAA9P,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6J,IAAI,MAAK,OAAO,KAAI7J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkL,OAAO,mBACvC1L,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BrJ,UAAU,EAAE;YAAE0I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CzP,OAAA;YAAKwP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCzP,OAAA;cAAKwP,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HzP,OAAA;gBAAKwP,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCzP,OAAA;kBAAKwP,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFzP,OAAA;oBAAMwP,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNtQ,OAAA;kBAAAyP,QAAA,gBACEzP,OAAA;oBAAIwP,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDtQ,OAAA;oBAAGwP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BrJ,UAAU,EAAE;YAAE0I,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzCzP,OAAA;YAAKwP,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GzP,OAAA;cAAKwP,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGtQ,OAAA;cAAKwP,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGtQ,OAAA;cAAKwP,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFzP,OAAA;gBAAKwP,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBACTG,OAAO,EAAE;oBACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFlL,UAAU,EAAE;oBACV0I,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBzP,OAAA;oBAAIwP,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GzP,OAAA,CAAC5B,MAAM,CAACiU,IAAI;sBACVxC,OAAO,EAAE;wBACP2C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFnL,UAAU,EAAE;wBACV0I,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIrI,KAAK,EAAE;wBACLsL,cAAc,EAAE,WAAW;wBAC3BX,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCjM,MAAM,EAAE;sBACV,CAAE;sBAAA2J,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdtQ,OAAA;sBAAAmQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtQ,OAAA,CAAC5B,MAAM,CAACiU,IAAI;sBACVxC,OAAO,EAAE;wBACPmC,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF3K,UAAU,EAAE;wBACV0I,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACF/I,KAAK,EAAE;wBACL/D,KAAK,EAAE,SAAS;wBAChBsP,UAAU,EAAE,KAAK;wBACjBV,UAAU,EAAE;sBACd,CAAE;sBAAAvC,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbtQ,OAAA,CAAC5B,MAAM,CAACuU,CAAC;kBACPhD,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BrJ,UAAU,EAAE;oBAAEuJ,KAAK,EAAE,GAAG;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHrI,KAAK,EAAE;oBACL/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCV,UAAU,EAAE,0CAA0C;oBACtDQ,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAtC,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAI,CAAE;kBACpCpB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBAClC5J,UAAU,EAAE;oBAAEuJ,KAAK,EAAE,GAAG;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBzP,OAAA;oBAAGwP,SAAS,EAAC,6JAA6J;oBACvKrI,KAAK,EAAE;sBACL6K,UAAU,EAAE,6BAA6B;sBACzCY,SAAS,EAAE;oBACb,CAAE;oBAAAnD,QAAA,EACF5N;kBAAiB;oBAAAsO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BrJ,UAAU,EAAE;oBAAEuJ,KAAK,EAAE,CAAC;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACE/L,IAAI,EAAEnE,OAAO;oBACbsT,KAAK,EAAE5R,WAAW,CAACmF,MAAM;oBACzB0M,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBnP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEjF,QAAQ;oBACdoU,KAAK,EAAEjF,aAAa,CAACxH,MAAM;oBAC3B0M,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBnP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE9E,OAAO;oBACbiU,KAAK,EAAE5R,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC2C,aAAa,GAAG,CAAC,CAAC,CAACxC,MAAM;oBAC1D0M,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBnP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE/E,MAAM;oBACZkU,KAAK,EAAE5R,WAAW,CAACwJ,MAAM,CAAC,CAACC,GAAG,EAAEzE,CAAC,KAAKyE,GAAG,IAAIzE,CAAC,CAACb,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC6N,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBnP,WAAW,EAAE;kBACf,CAAC,CACF,CAACuE,GAAG,CAAC,CAAC8K,IAAI,EAAE7K,KAAK,kBAChBrI,OAAA,CAAC5B,MAAM,CAACsR,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAI,CAAE;oBACpCpB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAE,CAAE;oBAClC5J,UAAU,EAAE;sBAAEuJ,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,GAAG;sBAAE0H,QAAQ,EAAE;oBAAI,CAAE;oBACxDiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnClB,SAAS,EAAG,qBAAoB0D,IAAI,CAACH,UAAW,8EAA8E;oBAC9H5L,KAAK,EAAE;sBACLiL,MAAM,EAAG,aAAYc,IAAI,CAACrP,WAAY,IAAG;sBACzCyD,SAAS,EAAG,cAAa4L,IAAI,CAACrP,WAAY;oBAC5C,CAAE;oBAAA4L,QAAA,gBAEFzP,OAAA;sBAAKwP,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFtQ,OAAA,CAACkT,IAAI,CAACxP,IAAI;sBACR8L,SAAS,EAAC,kDAAkD;sBAC5DrI,KAAK,EAAE;wBAAE/D,KAAK,EAAE8P,IAAI,CAACF,SAAS;wBAAElN,MAAM,EAAE;sBAAyC;oBAAE;sBAAAqK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFtQ,OAAA;sBACEwP,SAAS,EAAC,0EAA0E;sBACpFrI,KAAK,EAAE;wBACL/D,KAAK,EAAE8P,IAAI,CAACF,SAAS;wBACrBhB,UAAU,EAAG,6BAA4B;wBACzClM,MAAM,EAAE,oCAAoC;wBAC5CsL,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDyD,IAAI,CAACL;oBAAK;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNtQ,OAAA;sBACEwP,SAAS,EAAC,4CAA4C;sBACtDrI,KAAK,EAAE;wBACL/D,KAAK,EAAE,SAAS;wBAChB4O,UAAU,EAAE,6BAA6B;wBACzCZ,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDyD,IAAI,CAACJ;oBAAK;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDjI,KAAK;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZnP,OAAO,iBACNnB,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBzI,UAAU,EAAE;cAAE0I,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFtQ,OAAA;YAAGwP,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACnP,OAAO,iBACPnB,OAAA,CAAC5B,MAAM,CAACsR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAG,CAAE;UAC/Bb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BrJ,UAAU,EAAE;YAAEuJ,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjEzP,OAAA;YAAKwP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/B7B,aAAa,CAACxH,MAAM,GAAG,CAAC,iBACvBpG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5J,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBzP,OAAA;gBAAIwP,SAAS,EAAC,gGAAgG;gBAACrI,KAAK,EAAE;kBACpHmK,UAAU,EAAE,mDAAmD;kBAC/DQ,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzClM,MAAM,EAAE;gBACV,CAAE;gBAAA2J,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLtQ,OAAA;gBAAKwP,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/G7B,aAAa,CAAC,CAAC,CAAC,iBACf5N,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBAETyD,GAAG,EAAE3S,IAAI,IAAIgL,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,GAAGpD,aAAa,GAAG,IAAK;kBACtF,gBAAc8K,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCb,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVe,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFlL,UAAU,EAAE;oBACVuJ,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DqC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,oBACVjB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GAC7C,2DAA2D,GAC3DwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLiM,MAAM,EAAE,OAAO;oBACfhM,SAAS,EAAEmH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAEyI,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GACrD,0EAA0E,GAC1EwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3B+K,MAAM,EAAE7D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EmN,YAAY,EAAE9E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEoL,UAAU,EAAE/C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAuJ,QAAA,gBAGFzP,OAAA;oBAAKwP,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnKzP,OAAA;sBAAMwP,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNtQ,OAAA;oBACEwP,SAAS,EAAG,8BAA6B5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAClC,KAAM,mBAAkBwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpI0D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAasG,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,IAAG;sBAC9D8P,KAAK,EAAE;oBACT,CAAE;oBAAA7D,QAAA,eAEFzP,OAAA;sBACEwP,SAAS,EAAG,GAAE5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAoM,QAAA,gBAEnHzP,OAAA;wBAAKwP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFtQ,OAAA;wBACEwP,SAAS,EAAC,oMAAoM;wBAC9MrI,KAAK,EAAE;0BACL/D,KAAK,EAAE,SAAS;0BAChBgP,MAAM,EAAE;wBACV,CAAE;wBAAA3C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNtQ,OAAA;wBAAKwP,SAAS,EAAG,yBAAwBhP,IAAI,IAAIoN,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,KAAK1F,IAAI,CAAC0F,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,eACnIzP,OAAA;0BACEwP,SAAS,EAAC,wEAAwE;0BAClFrI,KAAK,EAAE;4BACLmK,UAAU,EAAE,SAAS;4BACrBhK,SAAS,EAAE,4BAA4B;4BACvCgM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA3D,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BzI,OAAA;4BACEuT,GAAG,EAAE3F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrC+K,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BkH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFtQ,OAAA;4BACEwP,SAAS,EAAC,2EAA2E;4BACrFrI,KAAK,EAAE;8BACLmK,UAAU,EAAE,SAAS;8BACrBlO,KAAK,EAAE,SAAS;8BAChBgO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACmL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNtQ,OAAA;wBACEwP,SAAS,EAAC,iCAAiC;wBAC3CrI,KAAK,EAAE;0BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAkM,QAAA,EAEjD7B,aAAa,CAAC,CAAC,CAAC,CAACtF;sBAAI;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELtQ,OAAA;wBAAKwP,SAAS,EAAC,yBAAyB;wBAACrI,KAAK,EAAE;0BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;wBAAU,CAAE;wBAAAmM,QAAA,GACxF7B,aAAa,CAAC,CAAC,CAAC,CAACxI,OAAO,CAAC6N,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENtQ,OAAA;wBAAKwP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDzP,OAAA;0BAAMmH,KAAK,EAAE;4BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;0BAAU,CAAE;0BAAAmM,QAAA,GAAC,eACpD,EAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC1F,iBAAiB;wBAAA;0BAAAiI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPtQ,OAAA;0BAAMmH,KAAK,EAAE;4BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;0BAAU,CAAE;0BAAAmM,QAAA,GAAC,eACpD,EAAC7B,aAAa,CAAC,CAAC,CAAC,CAAChF,aAAa;wBAAA;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA1HA,UAAS1C,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2H3B,CACb,EAGA1C,aAAa,CAAC,CAAC,CAAC,iBACf5N,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBAETyD,GAAG,EAAE3S,IAAI,IAAIgL,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,GAAGpD,aAAa,GAAG,IAAK;kBACtF,gBAAc8K,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE,CAAC,GAAG;oBAAEO,KAAK,EAAE;kBAAI,CAAE;kBAC7CpB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVc,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC;oBACRsB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxB7B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFrJ,UAAU,EAAE;oBACVuJ,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbwC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DQ,CAAC,EAAE;sBAAEX,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,yBACVjB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GAC7C,2DAA2D,GAC3DwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLiM,MAAM,EAAE,OAAO;oBACfhM,SAAS,EAAEmH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAEyI,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GACrD,4FAA4F,GAC5FwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3B+K,MAAM,EAAE7D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EmN,YAAY,EAAE9E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEoL,UAAU,EAAE/C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAAuJ,QAAA,gBAIrBzP,OAAA;oBAAKwP,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzKzP,OAAA;sBAAMwP,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGNtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDrJ,UAAU,EAAE;sBAAE0I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErEzP,OAAA,CAACtB,OAAO;sBAAC8Q,SAAS,EAAC;oBAA0C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGbtQ,OAAA;oBACEwP,SAAS,EAAG,8BAA6B5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAClC,KAAM,sBAAqBwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5J0D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAasG,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/F8P,KAAK,EAAE;oBACT,CAAE;oBAAA7D,QAAA,eAEFzP,OAAA;sBACEwP,SAAS,EAAG,GAAE5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH8D,KAAK,EAAE;wBACLmK,UAAU,EAAG,GAAE1D,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAoM,QAAA,gBAEFzP,OAAA;wBAAKwP,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlGtQ,OAAA;wBACEwP,SAAS,EAAC,wMAAwM;wBAClNrI,KAAK,EAAE;0BACL/D,KAAK,EAAE,SAAS;0BAChB4O,UAAU,EAAE,6BAA6B;0BACzCI,MAAM,EAAE;wBACV,CAAE;wBAAA3C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNtQ,OAAA;wBAAKwP,SAAS,EAAG,yBAAwBhP,IAAI,IAAIoN,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,KAAK1F,IAAI,CAAC0F,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,gBACnIzP,OAAA;0BACEwP,SAAS,EAAC,wEAAwE;0BAClFrI,KAAK,EAAE;4BACLmK,UAAU,EAAE,SAAS;4BACrBhK,SAAS,EAAE,4BAA4B;4BACvCgM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA3D,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BzI,OAAA;4BACEuT,GAAG,EAAE3F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrC+K,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BkH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFtQ,OAAA;4BACEwP,SAAS,EAAC,2EAA2E;4BACrFrI,KAAK,EAAE;8BACLmK,UAAU,EAAE,SAAS;8BACrBlO,KAAK,EAAE,SAAS;8BAChBgO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACmL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL9P,IAAI,IAAIoN,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,KAAK1F,IAAI,CAAC0F,GAAG,iBACxClG,OAAA;0BACEwP,SAAS,EAAC,4DAA4D;0BACtErI,KAAK,EAAE;4BACLmK,UAAU,EAAE,0CAA0C;4BACtDhK,SAAS,EAAE;0BACb,CAAE;0BAAAmI,QAAA,eAEFzP,OAAA,CAACrB,MAAM;4BAAC6Q,SAAS,EAAC;0BAAuB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNtQ,OAAA;wBAAKwP,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1DzP,OAAA;0BACEwP,SAAS,EAAC,6BAA6B;0BACvCrI,KAAK,EAAE;4BACL/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC/B,SAAS;4BACtCyO,UAAU,EAAG,eAAcpE,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,EAAC;4BAC9DsC,MAAM,EAAE;0BACV,CAAE;0BAAA2J,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACtF;wBAAI;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJ/B,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,iBAClClG,OAAA;0BACEwP,SAAS,EAAC,yDAAyD;0BACnErI,KAAK,EAAE;4BACLmK,UAAU,EAAE,0CAA0C;4BACtDlO,KAAK,EAAE,SAAS;4BAChBkE,SAAS,EAAE,+BAA+B;4BAC1C8K,MAAM,EAAE,mBAAmB;4BAC3BhB,QAAQ,EAAE;0BACZ,CAAE;0BAAA3B,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENtQ,OAAA;wBACEwP,SAAS,EAAG,6DAA4D5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ+D,KAAK,EAAE;0BACLmK,UAAU,EAAG,2BAA0B1D,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACzB,WAAY,KAAI+J,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChB4O,UAAU,EAAE,6BAA6B;0BACzC1K,SAAS,EAAG,cAAasG,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,IAAG;0BAC9D4O,MAAM,EAAE;wBACV,CAAE;wBAAA3C,QAAA,GAED7B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC5B,IAAI,iBAAI1F,KAAK,CAAC2V,aAAa,CAAC/F,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC5B,IAAI,EAAE;0BAC7E8L,SAAS,EAAE,SAAS;0BACpBrI,KAAK,EAAE;4BAAE/D,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACFpD,OAAA;0BAAMmH,KAAK,EAAE;4BAAE/D,KAAK,EAAE;0BAAU,CAAE;0BAAAqM,QAAA,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC3B;wBAAK;0BAAAwM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGNtQ,OAAA;wBAAKwP,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCzP,OAAA;0BAAKwP,SAAS,EAAC,oBAAoB;0BAACrI,KAAK,EAAE;4BACzC/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC/B,SAAS;4BACtCyO,UAAU,EAAG,eAAcpE,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,EAAC;4BAC9DsC,MAAM,EAAE;0BACV,CAAE;0BAAA2J,QAAA,GACC7B,aAAa,CAAC,CAAC,CAAC,CAACxI,OAAO,CAAC6N,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAENtQ,OAAA;0BAAKwP,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDzP,OAAA;4BAAKwP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BzP,OAAA;8BAAKwP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDzP,OAAA,CAAClB,OAAO;gCAAC0Q,SAAS,EAAC,SAAS;gCAACrI,KAAK,EAAE;kCAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;gCAAU;8BAAE;gCAAA6M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClFtQ,OAAA;gCAAMwP,SAAS,EAAC,WAAW;gCAACrI,KAAK,EAAE;kCAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;gCAAU,CAAE;gCAAAmM,QAAA,EAC3E7B,aAAa,CAAC,CAAC,CAAC,CAAC1F;8BAAiB;gCAAAiI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNtQ,OAAA;8BAAKwP,SAAS,EAAC,oBAAoB;8BAACrI,KAAK,EAAE;gCAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;8BAAU,CAAE;8BAAAmM,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACNtQ,OAAA;4BAAKwP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BzP,OAAA;8BAAKwP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDzP,OAAA,CAACpB,OAAO;gCAAC4Q,SAAS,EAAC,SAAS;gCAACrI,KAAK,EAAE;kCAAE/D,KAAK,EAAE;gCAAU;8BAAE;gCAAA+M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5DtQ,OAAA;gCAAMwP,SAAS,EAAC,WAAW;gCAACrI,KAAK,EAAE;kCAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;gCAAU,CAAE;gCAAAmM,QAAA,EAC3E7B,aAAa,CAAC,CAAC,CAAC,CAAChF;8BAAa;gCAAAuH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNtQ,OAAA;8BAAKwP,SAAS,EAAC,oBAAoB;8BAACrI,KAAK,EAAE;gCAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;8BAAU,CAAE;8BAAAmM,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA5MA,SAAQ1C,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6M1B,CACb,EAGA1C,aAAa,CAAC,CAAC,CAAC,iBACf5N,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBAETyD,GAAG,EAAE3S,IAAI,IAAIgL,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC,GAAGpD,aAAa,GAAG,IAAK;kBACtF,gBAAc8K,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCb,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVe,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFlL,UAAU,EAAE;oBACVuJ,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DqC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,oBACVjB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GAC7C,2DAA2D,GAC3DwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLiM,MAAM,EAAE,OAAO;oBACfhM,SAAS,EAAEmH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAEyI,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,IAAInE,UAAU,GACrD,0EAA0E,GAC1EwM,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3B+K,MAAM,EAAE7D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EmN,YAAY,EAAE9E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEoL,UAAU,EAAE/C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAAuJ,QAAA,gBAGFzP,OAAA;oBAAKwP,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtKzP,OAAA;sBAAMwP,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNtQ,OAAA;oBACEwP,SAAS,EAAG,8BAA6B5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAClC,KAAM,mBAAkBwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpI0D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAasG,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC9B,WAAY,IAAG;sBAC9D8P,KAAK,EAAE;oBACT,CAAE;oBAAA7D,QAAA,eAEFzP,OAAA;sBACEwP,SAAS,EAAG,GAAE5B,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAoM,QAAA,gBAEnHzP,OAAA;wBAAKwP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFtQ,OAAA;wBACEwP,SAAS,EAAC,sMAAsM;wBAChNrI,KAAK,EAAE;0BACL/D,KAAK,EAAE,SAAS;0BAChBgP,MAAM,EAAE;wBACV,CAAE;wBAAA3C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNtQ,OAAA;wBAAKwP,SAAS,EAAG,yBAAwBhP,IAAI,IAAIoN,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAG,KAAK1F,IAAI,CAAC0F,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,eACnIzP,OAAA;0BACEwP,SAAS,EAAC,wEAAwE;0BAClFrI,KAAK,EAAE;4BACLmK,UAAU,EAAE,SAAS;4BACrBhK,SAAS,EAAE,4BAA4B;4BACvCgM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA3D,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BzI,OAAA;4BACEuT,GAAG,EAAE3F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrC+K,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BkH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFtQ,OAAA;4BACEwP,SAAS,EAAC,2EAA2E;4BACrFrI,KAAK,EAAE;8BACLmK,UAAU,EAAE,SAAS;8BACrBlO,KAAK,EAAE,SAAS;8BAChBgO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED7B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACmL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNtQ,OAAA;wBACEwP,SAAS,EAAC,iCAAiC;wBAC3CrI,KAAK,EAAE;0BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAkM,QAAA,EAEjD7B,aAAa,CAAC,CAAC,CAAC,CAACtF;sBAAI;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELtQ,OAAA;wBAAKwP,SAAS,EAAC,yBAAyB;wBAACrI,KAAK,EAAE;0BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;wBAAU,CAAE;wBAAAmM,QAAA,GACxF7B,aAAa,CAAC,CAAC,CAAC,CAACxI,OAAO,CAAC6N,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENtQ,OAAA;wBAAKwP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDzP,OAAA;0BAAMmH,KAAK,EAAE;4BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;0BAAU,CAAE;0BAAAmM,QAAA,GAAC,eACpD,EAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC1F,iBAAiB;wBAAA;0BAAAiI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPtQ,OAAA;0BAAMmH,KAAK,EAAE;4BAAE/D,KAAK,EAAEwK,aAAa,CAAC,CAAC,CAAC,CAACtI,IAAI,CAAChC;0BAAU,CAAE;0BAAAmM,QAAA,GAAC,eACpD,EAAC7B,aAAa,CAAC,CAAC,CAAC,CAAChF,aAAa;wBAAA;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA1HA,SAAQ1C,aAAa,CAAC,CAAC,CAAC,CAAC1H,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2H1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLrO,iBAAiB,iBAChBjC,OAAA,CAAC5B,MAAM,CAACsR,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAG,CAAE;gBAC/Bb,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAE,CAAE;gBAC9BrJ,UAAU,EAAE;kBAAEuJ,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CP,SAAS,EAAC,wJAAwJ;gBAAAC,QAAA,gBAElKzP,OAAA;kBAAKwP,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzP,OAAA;oBAAIwP,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,eACnC,EAACxN,iBAAiB,CAAC2C,MAAM,CAACb,UAAU,EAAC,GAAC,EAAC9B,iBAAiB,CAAC2C,MAAM,CAACjB,KAAK;kBAAA;oBAAAwM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACLtQ,OAAA;oBAAGwP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAC7B,EAACxN,iBAAiB,CAAC8D,QAAQ,EAAC,MAAI,EAAC9D,iBAAiB,CAACkE,aAAa,EAAC,iBACzE;kBAAA;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENtQ,OAAA;kBAAKwP,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDzP,OAAA;oBAAKwP,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDzP,OAAA;sBAAKwP,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACtCxN,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,GAAG,CAAC,GACtC,GAAE/B,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,IAAI,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,OAAO,KAAI,CAAC,CAAE,KAAI,GACnE;oBAAY;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CAAC,eACNtQ,OAAA;sBAAKwP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNtQ,OAAA;oBAAKwP,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACxDzP,OAAA;sBAAKwP,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAExN,iBAAiB,CAACkE;oBAAa;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFtQ,OAAA;sBAAKwP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNtQ,OAAA;oBAAKwP,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1DzP,OAAA;sBAAKwP,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GAAC,GAAC,EAACxN,iBAAiB,CAAC8D,QAAQ;oBAAA;sBAAAoK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9EtQ,OAAA;sBAAKwP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGS,CACb,EAGA/N,cAAc,GACb;YACAJ,WAAW,CAACiE,MAAM,GAAG,CAAC,iBACpBpG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BrJ,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtCzP,OAAA;gBAAKwP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCzP,OAAA,CAAC5B,MAAM,CAACwV,EAAE;kBACRpE,SAAS,EAAC,kDAAkD;kBAC5DrI,KAAK,EAAE;oBACLmK,UAAU,EAAG,0BAAyBrO,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY,KAAIZ,YAAY,CAACV,cAAc,CAAC,CAACe,SAAU,GAAE;oBAC5HwO,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzClM,MAAM,EAAG,wBAAuB7C,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY;kBAC3E,CAAE;kBACFgM,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5J,UAAU,EAAE;oBAAE0I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,GAE7CxM,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU,EAAC,GAAC,EAACd,YAAY,CAACV,cAAc,CAAC,CAACoB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU;gBAAA;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZtQ,OAAA;kBAAGwP,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1DtN,WAAW,CAACiE,MAAM,EAAC,2BACtB;gBAAA;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJtQ,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM3O,iBAAiB,CAAC,IAAI,CAAE;kBACvCgN,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGNtQ,OAAA;gBAAKwP,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCzP,OAAA;kBAAKwP,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjCtN,WAAW,CAACiG,GAAG,CAAC,CAACyL,QAAQ,EAAExL,KAAK,KAAK;oBACpC,MAAMyL,UAAU,GAAGzL,KAAK,GAAG,CAAC;oBAC5B,MAAMkG,aAAa,GAAG/N,IAAI,IAAIgL,MAAM,CAACqI,QAAQ,CAAC3N,GAAG,CAAC,KAAKsF,MAAM,CAAChL,IAAI,CAAC0F,GAAG,CAAC;oBAEvE,oBACElG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;sBAETyD,GAAG,EAAE5E,aAAa,GAAGxL,WAAW,GAAG,IAAK;sBACxC,gBAAc8Q,QAAQ,CAAC3N,GAAI;sBAC3B,kBAAgB4N,UAAW;sBAC3BnE,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEc,CAAC,EAAE;sBAAG,CAAE;sBAC/Bb,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEc,CAAC,EAAE;sBAAE,CAAE;sBAC9BrJ,UAAU,EAAE;wBAAEuJ,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,IAAI;wBAAE0H,QAAQ,EAAE;sBAAI,CAAE;sBACzDiB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEP,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnClB,SAAS,EAAG,+BACVjB,aAAa,IAAIxM,UAAU,GACvB,2DAA2D,GAC3DwM,aAAa,GACX,yCAAyC,GACzC,EACP,EAAE;sBACHpH,KAAK,EAAE;wBACLC,SAAS,EAAEmH,aAAa,IAAIxM,UAAU,GAClC,6BAA6B,GAC7BwM,aAAa,GACX,aAAa,GACb,UAAU;wBAChBzI,MAAM,EAAEyI,aAAa,IAAIxM,UAAU,GAC/B,0EAA0E,GAC1EwM,aAAa,GACX,2EAA2E,GAC3E,MAAM;wBACZlH,UAAU,EAAE,eAAe;wBAC3B+K,MAAM,EAAE7D,aAAa,IAAIxM,UAAU,GAC/B,mBAAmB,GACnBwM,aAAa,GACX,mBAAmB,GACnB,MAAM;wBACZ8E,YAAY,EAAE9E,aAAa,GAAG,MAAM,GAAG,KAAK;wBAC5C+C,UAAU,EAAE/C,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvHJ,QAAQ,EAAE,UAAU;wBACpByD,MAAM,EAAErD,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAkB,QAAA,eAGFzP,OAAA;wBACEwP,SAAS,EAAG,oBAAmBqE,QAAQ,CAACvO,IAAI,CAAClC,KAAM,sBAAqByQ,QAAQ,CAACvO,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJ0D,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAauM,QAAQ,CAACvO,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAiM,QAAA,eAEFzP,OAAA;0BACEwP,SAAS,EAAG,GAAEqE,QAAQ,CAACvO,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH8D,KAAK,EAAE;4BACLiL,MAAM,EAAG,aAAYyB,QAAQ,CAACvO,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAA4L,QAAA,gBAGFzP,OAAA;4BAAKwP,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjGtQ,OAAA;4BAAKwP,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDzP,OAAA;8BAAKwP,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvBzP,OAAA;gCACEwP,SAAS,EAAC,kJAAkJ;gCAC5JrI,KAAK,EAAE;kCACL/D,KAAK,EAAE,SAAS;kCAChB4O,UAAU,EAAE,6BAA6B;kCACzCI,MAAM,EAAE,iCAAiC;kCACzC9K,SAAS,EAAE;gCACb,CAAE;gCAAAmI,QAAA,GACH,GACE,EAACqE,UAAU;8BAAA;gCAAA3D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGNtQ,OAAA;8BAAKwP,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBzP,OAAA;gCACEwP,SAAS,EAAC,gEAAgE;gCAC1ErI,KAAK,EAAE;kCACLmK,UAAU,EAAE,SAAS;kCACrBhK,SAAS,EAAE,4BAA4B;kCACvCgM,KAAK,EAAE,MAAM;kCACbF,MAAM,EAAE;gCACV,CAAE;gCAAA3D,QAAA,EAEDoE,QAAQ,CAACpL,cAAc,gBACtBzI,OAAA;kCACEuT,GAAG,EAAEM,QAAQ,CAACpL,cAAe;kCAC7B+K,GAAG,EAAEK,QAAQ,CAACvL,IAAK;kCACnBkH,SAAS,EAAC;gCAAyC;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACpD,CAAC,gBAEFtQ,OAAA;kCACEwP,SAAS,EAAC,2EAA2E;kCACrFrI,KAAK,EAAE;oCACLmK,UAAU,EAAE,SAAS;oCACrBlO,KAAK,EAAE,SAAS;oCAChBgO,QAAQ,EAAE;kCACZ,CAAE;kCAAA3B,QAAA,EAEDoE,QAAQ,CAACvL,IAAI,CAACmL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAAvD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAEL/B,aAAa,iBACZvO,OAAA;gCACEwP,SAAS,EAAC,8FAA8F;gCACxGrI,KAAK,EAAE;kCACLmK,UAAU,EAAE,0CAA0C;kCACtDhK,SAAS,EAAE;gCACb,CAAE;gCAAAmI,QAAA,eAEFzP,OAAA,CAACrB,MAAM;kCAAC6Q,SAAS,EAAC;gCAA2B;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNtQ,OAAA;4BAAKwP,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCzP,OAAA;8BAAKwP,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBzP,OAAA;gCAAKwP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3CzP,OAAA;kCACEwP,SAAS,EAAC,yCAAyC;kCACnDrI,KAAK,EAAE;oCACL/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAC/B,SAAS;oCAC9ByO,UAAU,EAAG,eAAc6B,QAAQ,CAACvO,IAAI,CAAC9B,WAAY,EAAC;oCACtDsC,MAAM,EAAE;kCACV,CAAE;kCAAA2J,QAAA,EAEDoE,QAAQ,CAACvL;gCAAI;kCAAA6H,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJ/B,aAAa,iBACZvO,OAAA;kCACEwP,SAAS,EAAC,yDAAyD;kCACnErI,KAAK,EAAE;oCACLmK,UAAU,EAAE,mDAAmD;oCAC/DlO,KAAK,EAAE,SAAS;oCAChBkE,SAAS,EAAE,8DAA8D;oCACzE8K,MAAM,EAAE,mBAAmB;oCAC3BJ,UAAU,EAAE,6BAA6B;oCACzCZ,QAAQ,EAAE,MAAM;oCAChBsB,UAAU,EAAE;kCACd,CAAE;kCAAAjD,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNtQ,OAAA;gCAAKwP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1CoE,QAAQ,CAAChM,KAAK,EAAC,gBAAS,EAACgM,QAAQ,CAACrL,KAAK;8BAAA;gCAAA2H,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNtQ,OAAA;4BAAKwP,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DzP,OAAA;8BACEwP,SAAS,EAAC,oCAAoC;8BAC9CrI,KAAK,EAAE;gCACL/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAC/B,SAAS;gCAC9ByO,UAAU,EAAG,eAAc6B,QAAQ,CAACvO,IAAI,CAAC9B,WAAY,EAAC;gCACtDsC,MAAM,EAAE;8BACV,CAAE;8BAAA2J,QAAA,GAEDoE,QAAQ,CAACzO,OAAO,CAAC6N,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGNtQ,OAAA;8BAAKwP,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9CzP,OAAA;gCACEwP,SAAS,EAAC,8CAA8C;gCACxDrI,KAAK,EAAE;kCACL4M,eAAe,EAAG,GAAEF,QAAQ,CAACvO,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAChC;gCACvB,CAAE;gCAAAmM,QAAA,gBAEFzP,OAAA,CAAClB,OAAO;kCAAC0Q,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BtQ,OAAA;kCAAMwP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEoE,QAAQ,CAAC3L;gCAAiB;kCAAAiI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACNtQ,OAAA;gCACEwP,SAAS,EAAC,8CAA8C;gCACxDrI,KAAK,EAAE;kCACL4M,eAAe,EAAE,WAAW;kCAC5B3Q,KAAK,EAAE;gCACT,CAAE;gCAAAqM,QAAA,gBAEFzP,OAAA,CAACpB,OAAO;kCAAC4Q,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BtQ,OAAA;kCAAMwP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEoE,QAAQ,CAACjL;gCAAa;kCAAAuH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAnMDuD,QAAQ,CAAC3N,GAAG;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAoMP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACAxL,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC2D,MAAM,GAAG,CAAC,iBAClCpG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BrJ,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cACtCxD,EAAE,EAAC,yBAAyB;cAAAyD,QAAA,gBAG5BzP,OAAA;gBAAKwP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCzP,OAAA,CAAC5B,MAAM,CAACwV,EAAE;kBACRpE,SAAS,EAAC,kDAAkD;kBAC5DrI,KAAK,EAAE;oBACLmK,UAAU,EAAE,mDAAmD;oBAC/DQ,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzClM,MAAM,EAAE;kBACV,CAAE;kBACF+J,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5J,UAAU,EAAE;oBAAE0I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZtQ,OAAA;kBAAGwP,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNtQ,OAAA;gBAAKwP,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9ClI,iBAAiB,CAAC,CAAC,CAACa,GAAG,CAAE5C,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMkI,UAAU,GAAGjL,YAAY,CAAC+C,SAAS,CAAC;kBAC1C,MAAMwO,QAAQ,GAAGtG,UAAU,CAACnN,KAAK,CAACwL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACE/L,OAAA,CAAC5B,MAAM,CAACsR,GAAG;oBAETyD,GAAG,EAAGc,EAAE,IAAMtR,UAAU,CAACmE,OAAO,CAACtB,SAAS,CAAC,GAAGyO,EAAI;oBAClDtE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEc,CAAC,EAAE;oBAAG,CAAE;oBAC/Bb,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEc,CAAC,EAAE;oBAAE,CAAE;oBAC9BrJ,UAAU,EAAE;sBAAEuJ,KAAK,EAAE,GAAG;sBAAEb,QAAQ,EAAE;oBAAI,CAAE;oBAC1CP,SAAS,EAAC,mGAAmG;oBAC7GxD,EAAE,EAAG,UAASxG,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAiK,QAAA,gBAGvBzP,OAAA;sBAAKwP,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDzP,OAAA;wBAAKwP,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCzP,OAAA;0BACEwP,SAAS,EAAC,gEAAgE;0BAC1ErI,KAAK,EAAE;4BACLmK,UAAU,EAAG,2BAA0B1M,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrF8O,MAAM,EAAG,aAAYxN,MAAM,CAACf,WAAY,IAAG;4BAC3CyD,SAAS,EAAG,cAAa1C,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAiM,QAAA,EAED7K,MAAM,CAACb;wBAAU;0BAAAoM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACNtQ,OAAA;0BAAAyP,QAAA,gBACEzP,OAAA;4BACEwP,SAAS,EAAC,0BAA0B;4BACpCrI,KAAK,EAAE;8BACL/D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByO,UAAU,EAAG,eAAcpN,MAAM,CAACpB,WAAY,EAAC;8BAC/CsC,MAAM,EAAE;4BACV,CAAE;4BAAA2J,QAAA,GAED7K,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAAwM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLtQ,OAAA;4BAAGwP,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjC/B,UAAU,CAACnN,KAAK,CAAC6F,MAAM,EAAC,oBAAa,EAACxB,MAAM,CAAChB,WAAW;0BAAA;4BAAAuM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNtQ,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BE,OAAO,EAAEA,CAAA,KAAM9K,kBAAkB,CAACb,SAAS,CAAE;wBAC7CgK,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAAC/B,UAAU,CAACnN,KAAK,CAAC6F,MAAM,EAAC,GACrC;sBAAA;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGNtQ,OAAA;sBAAKwP,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClEuE,QAAQ,CAAC5L,GAAG,CAAC,CAACyL,QAAQ,EAAExL,KAAK,KAAK;wBACjC,MAAMkG,aAAa,GAAG/N,IAAI,IAAIqT,QAAQ,CAAC3N,GAAG,KAAK1F,IAAI,CAAC0F,GAAG;wBACvD,MAAMgO,UAAU,GAAG7L,KAAK,GAAG,CAAC;wBAE5B,oBACErI,OAAA,CAAC5B,MAAM,CAACsR,GAAG;0BAETC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAI,CAAE;0BACpCpB,OAAO,EAAE;4BAAED,OAAO,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAE,CAAE;0BAClC5J,UAAU,EAAE;4BAAEuJ,KAAK,EAAE,GAAG,GAAGvI,KAAK,GAAG,GAAG;4BAAE0H,QAAQ,EAAE;0BAAI,CAAE;0BACxDiB,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnClB,SAAS,EAAG,YACVjB,aAAa,IAAIxM,UAAU,GACvB,6CAA6C,GAC7CwM,aAAa,GACX,2BAA2B,GAC3B,EACP,EAAE;0BAAAkB,QAAA,eAEHzP,OAAA;4BACEwP,SAAS,EAAG,qBAAoBqE,QAAQ,CAACvO,IAAI,CAAClC,KAAM,qBAAoByQ,QAAQ,CAACvO,IAAI,CAAC7B,IAAK,YAAY;4BACvG0D,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAauM,QAAQ,CAACvO,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAAiM,QAAA,eAEFzP,OAAA;8BACEwP,SAAS,EAAG,GAAEqE,QAAQ,CAACvO,IAAI,CAACjC,OAAQ,uEAAuE;8BAAAoM,QAAA,gBAE3GzP,OAAA;gCAAKwP,SAAS,EAAC;8BAAgE;gCAAAW,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtFtQ,OAAA;gCACEwP,SAAS,EAAC,mGAAmG;gCAC7GrI,KAAK,EAAE;kCACLmK,UAAU,EAAE1M,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChBgP,MAAM,EAAE;gCACV,CAAE;gCAAA3C,QAAA,GACH,GACE,EAACyE,UAAU;8BAAA;gCAAA/D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGNtQ,OAAA;gCAAKwP,SAAS,EAAG,yBACfjB,aAAa,IAAIxM,UAAU,GACvB,yCAAyC,GACzCwM,aAAa,GACX,wCAAwC,GACxC,EACP,EAAE;gCAAAkB,QAAA,gBACDzP,OAAA;kCACEwP,SAAS,EAAC,wEAAwE;kCAClFrI,KAAK,EAAE;oCACLmK,UAAU,EAAE,SAAS;oCACrBhK,SAAS,EAAE,4BAA4B;oCACvCgM,KAAK,EAAE,MAAM;oCACbF,MAAM,EAAE;kCACV,CAAE;kCAAA3D,QAAA,EAEDoE,QAAQ,CAACpL,cAAc,gBACtBzI,OAAA;oCACEuT,GAAG,EAAEM,QAAQ,CAACpL,cAAe;oCAC7B+K,GAAG,EAAEK,QAAQ,CAACvL,IAAK;oCACnBkH,SAAS,EAAC;kCAAyC;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpD,CAAC,gBAEFtQ,OAAA;oCACEwP,SAAS,EAAC,2EAA2E;oCACrFrI,KAAK,EAAE;sCACLmK,UAAU,EAAE,SAAS;sCACrBlO,KAAK,EAAE,SAAS;sCAChBgO,QAAQ,EAAE;oCACZ,CAAE;oCAAA3B,QAAA,EAEDoE,QAAQ,CAACvL,IAAI,CAACmL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kCAAC;oCAAAvD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACnC;gCACN;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,EACL/B,aAAa,iBACZvO,OAAA;kCACEwP,SAAS,EAAC,iGAAiG;kCAC3GrI,KAAK,EAAE;oCACLmK,UAAU,EAAE,0CAA0C;oCACtDhK,SAAS,EAAE;kCACb,CAAE;kCAAAmI,QAAA,eAEFzP,OAAA,CAACrB,MAAM;oCAAC6Q,SAAS,EAAC;kCAA2B;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNtQ,OAAA;gCACEwP,SAAS,EAAC,iCAAiC;gCAC3CrI,KAAK,EAAE;kCAAE/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAC/B;gCAAU,CAAE;gCAAAkM,QAAA,GAEzCoE,QAAQ,CAACvL,IAAI,EACbiG,aAAa,iBACZvO,OAAA;kCAAMwP,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAELtQ,OAAA;gCAAKwP,SAAS,EAAC,yBAAyB;gCAACrI,KAAK,EAAE;kCAAE/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAChC;gCAAU,CAAE;gCAAAmM,QAAA,GAChFoE,QAAQ,CAACzO,OAAO,CAAC6N,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAA9C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAENtQ,OAAA;gCAAKwP,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChDzP,OAAA;kCAAMmH,KAAK,EAAE;oCAAE/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAChC;kCAAU,CAAE;kCAAAmM,QAAA,GAAC,eAC5C,EAACoE,QAAQ,CAAC3L,iBAAiB;gCAAA;kCAAAiI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACPtQ,OAAA;kCAAMmH,KAAK,EAAE;oCAAE/D,KAAK,EAAEyQ,QAAQ,CAACvO,IAAI,CAAChC;kCAAU,CAAE;kCAAAmM,QAAA,GAAC,eAC5C,EAACoE,QAAQ,CAACjL,aAAa;gCAAA;kCAAAuH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GA7GDuD,QAAQ,CAAC3N,GAAG;0BAAAiK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA8GP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGL5C,UAAU,CAACnN,KAAK,CAAC6F,MAAM,GAAG,CAAC,iBAC1BpG,OAAA;sBAAKwP,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BzP,OAAA;wBAAGwP,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC/B,UAAU,CAACnN,KAAK,CAAC6F,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GAlLI9K,SAAS;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmLJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMArP,WAAW,CAACmF,MAAM,GAAG,CAAC,iBACrBpG,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BrJ,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJzP,OAAA;gBAAKwP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzP,OAAA;kBAAIwP,SAAS,EAAC,wBAAwB;kBAACrI,KAAK,EAAE;oBAC5C/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCtQ,OAAA;kBAAKwP,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DzP,OAAA;oBAAKwP,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CzP,OAAA;sBAAKwP,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,SAAS,CAAC,CAACnD;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNtQ,OAAA;sBAAKwP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNtQ,OAAA;oBAAKwP,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CzP,OAAA;sBAAKwP,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,eAAe,CAAC,CAACnD;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNtQ,OAAA;sBAAKwP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNtQ,OAAA;oBAAKwP,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CzP,OAAA;sBAAKwP,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACsD,UAAU,KAAK,WAAW,CAAC,CAACnD;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNtQ,OAAA;sBAAKwP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtQ,OAAA;kBAAGwP,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAjP,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCrB,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5J,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJzP,OAAA;gBAAKwP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzP,OAAA;kBAAIwP,SAAS,EAAC,yBAAyB;kBAACrI,KAAK,EAAE;oBAC7C/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BtQ,OAAA;kBAAKwP,SAAS,EAAC,0BAA0B;kBAACrI,KAAK,EAAE;oBAC/C/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,GAAC,GAAC,EAACpO,eAAe;gBAAA;kBAAA8O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BtQ,OAAA;kBAAGwP,SAAS,EAAC,SAAS;kBAACrI,KAAK,EAAE;oBAC5B/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDtQ,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BrJ,UAAU,EAAE;gBAAEuJ,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BzP,OAAA;gBAAKwP,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IzP,OAAA,CAAC5B,MAAM,CAACsR,GAAG;kBACTG,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5J,UAAU,EAAE;oBAAE0I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9CzP,OAAA,CAACb,QAAQ;oBAACqQ,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACbtQ,OAAA;kBAAIwP,SAAS,EAAC,yBAAyB;kBAACrI,KAAK,EAAE;oBAC7C/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BtQ,OAAA;kBAAGwP,SAAS,EAAC,gCAAgC;kBAACrI,KAAK,EAAE;oBACnD/D,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCU,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJtQ,OAAA,CAAC5B,MAAM,CAAC2S,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BzB,SAAS,EAAC,sJAAsJ;kBAChK2B,OAAO,EAAEA,CAAA,KAAM7D,MAAM,CAAC6G,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAA3E,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZrP,WAAW,CAACmF,MAAM,KAAK,CAAC,IAAI,CAACjF,OAAO,iBACnCnB,OAAA,CAAC5B,MAAM,CAACsR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClCzB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BzP,OAAA,CAACvB,QAAQ;gBAAC+Q,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DtQ,OAAA;gBAAIwP,SAAS,EAAC,yBAAyB;gBAACrI,KAAK,EAAE;kBAC7C/D,KAAK,EAAE,SAAS;kBAChB4O,UAAU,EAAE,6BAA6B;kBACzCU,UAAU,EAAE;gBACd,CAAE;gBAAAjD,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBtQ,OAAA;gBAAGwP,SAAS,EAAC,SAAS;gBAACrI,KAAK,EAAE;kBAC5B/D,KAAK,EAAE,SAAS;kBAChB4O,UAAU,EAAE,6BAA6B;kBACzCU,UAAU,EAAE;gBACd,CAAE;gBAAAjD,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAClQ,EAAA,CA1pFID,kBAAkB;EAAA,QACJ7B,WAAW,EAeZC,WAAW;AAAA;AAAA8V,EAAA,GAhBxBlU,kBAAkB;AA4pFxB,eAAeA,kBAAkB;AAAC,IAAAkU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}