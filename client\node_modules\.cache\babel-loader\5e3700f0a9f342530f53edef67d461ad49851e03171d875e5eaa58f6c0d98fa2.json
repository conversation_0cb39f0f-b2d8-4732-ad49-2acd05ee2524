{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto, sendOTP } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userDetails$name, _userDetails$name$cha;\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const sendOTPRequest = async email => {\n    dispatch(ShowLoading());\n    try {\n      const response = await sendOTP({\n        email\n      });\n      if (response.success) {\n        message.success(\"Please verify new email!\");\n        setEdit(false);\n        setServerGeneratedOTP(response.data);\n      } else {\n        message.error(response.message);\n        discardChanges();\n      }\n    } catch (error) {\n      message.error(error.message);\n      discardChanges();\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    if (!formData.name) return message.error(\"Please enter your name.\");\n    if (!formData.class_) return message.error(\"Please select a class.\");\n    if (!skipOTP && formData.email !== userDetails.email) {\n      return sendOTPRequest(formData.email);\n    }\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserInfo({\n        ...formData,\n        userId: userDetails._id\n      });\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        setServerGeneratedOTP(null);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = async e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        message.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        message.error('Image size should be less than 5MB');\n        return;\n      }\n      setProfileImage(file);\n\n      // Show preview\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n\n      // Auto-upload the image\n      const data = new FormData();\n      data.append(\"profileImage\", file);\n      dispatch(ShowLoading());\n      try {\n        const response = await updateUserPhoto(data);\n        dispatch(HideLoading());\n        if (response.success) {\n          message.success(\"Profile picture updated successfully!\");\n          getUserData(); // Refresh user data to show new image\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message || \"Failed to update profile picture\");\n      }\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const verifyUser = async values => {\n    if (values.otp === serverGeneratedOTP) {\n      handleUpdate({\n        skipOTP: true\n      });\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    getUserData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-gray-900 mb-2\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account settings and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-4 border-blue-200 relative cursor-pointer hover:border-blue-400 transition-colors duration-200\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                    width: '80px',\n                    height: '80px'\n                  },\n                  onClick: () => document.getElementById('profileImageInput').click(),\n                  children: [userDetails !== null && userDetails !== void 0 && userDetails.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: userDetails.profileImage,\n                    alt: \"Profile\",\n                    className: \"object-cover rounded-full w-full h-full\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                    style: {\n                      background: '#25D366',\n                      color: '#FFFFFF',\n                      fontSize: '24px'\n                    },\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$name = userDetails.name) === null || _userDetails$name === void 0 ? void 0 : (_userDetails$name$cha = _userDetails$name.charAt(0)) === null || _userDetails$name$cha === void 0 ? void 0 : _userDetails$name$cha.toUpperCase()) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\",\n                  onClick: () => document.getElementById('profileImageInput').click(),\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 text-white\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 13a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"profileImageInput\",\n                  type: \"file\",\n                  accept: \"image/*\",\n                  className: \"hidden\",\n                  onChange: handleImageChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-6 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg px-4 py-2 border border-blue-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-600 font-medium\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg px-4 py-2 border border-green-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-green-600 font-medium\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || '<EMAIL>'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded-lg px-4 py-2 border border-purple-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-purple-600 font-medium\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"xJjWNSR1RCzlUK3trf/QbFJJOcI=\", false, function () {\n  return [useDispatch];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "sendOTP", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsForRanking", "getUserRanking", "getXPLeaderboard", "jsxDEV", "_jsxDEV", "Profile", "_s", "_userDetails$name", "_userDetails$name$cha", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "serverGeneratedOTP", "setServerGeneratedOTP", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "length", "prev", "discardChanges", "sendOTPRequest", "handleUpdate", "skipOTP", "levelChanged", "setTimeout", "window", "location", "reload", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "append", "handleImageUpload", "verifyUser", "values", "otp", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "boxShadow", "width", "height", "onClick", "document", "getElementById", "click", "src", "alt", "onError", "display", "nextS<PERSON>ling", "color", "fontSize", "char<PERSON>t", "toUpperCase", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "id", "accept", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    if (!formData.name) return message.error(\"Please enter your name.\");\r\n    if (!formData.class_) return message.error(\"Please select a class.\");\r\n\r\n    if (\r\n      !skipOTP &&\r\n      formData.email !== userDetails.email\r\n    ) {\r\n      return sendOTPRequest(formData.email);\r\n    }\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      });\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* Profile Picture - Clickable for Change */}\r\n                <div className=\"relative mb-4\">\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-4 border-blue-200 relative cursor-pointer hover:border-blue-400 transition-colors duration-200\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\r\n                      width: '80px',\r\n                      height: '80px'\r\n                    }}\r\n                    onClick={() => document.getElementById('profileImageInput').click()}\r\n                  >\r\n                    {userDetails?.profileImage ? (\r\n                      <img\r\n                        src={userDetails.profileImage}\r\n                        alt=\"Profile\"\r\n                        className=\"object-cover rounded-full w-full h-full\"\r\n                        onError={(e) => {\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                    ) : null}\r\n                    <div\r\n                      className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\r\n                      style={{\r\n                        background: '#25D366',\r\n                        color: '#FFFFFF',\r\n                        fontSize: '24px'\r\n                      }}\r\n                    >\r\n                      {userDetails?.name?.charAt(0)?.toUpperCase() || 'U'}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Camera Icon Overlay */}\r\n                  <div className=\"absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                       onClick={() => document.getElementById('profileImageInput').click()}>\r\n                    <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                    </svg>\r\n                  </div>\r\n\r\n                  {/* Hidden File Input */}\r\n                  <input\r\n                    id=\"profileImageInput\"\r\n                    type=\"file\"\r\n                    accept=\"image/*\"\r\n                    className=\"hidden\"\r\n                    onChange={handleImageChange}\r\n                  />\r\n                </div>\r\n\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-6 text-center\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-2 border border-blue-200\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-2 border border-green-200\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Email</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.email || '<EMAIL>'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-2 border border-purple-200\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.name || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.email || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.school || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.level || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.class || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.phoneNumber || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center\">\r\n                <button className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\">\r\n                  Edit Profile\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,EACfC,OAAO,QACF,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtG,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,qBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6B,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC6C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAMiD,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9B,MAAMuC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrC,uBAAuB,CAAC,CAAC;MAChD,IAAIqC,QAAQ,CAACC,OAAO,EAAE;QACpB1B,cAAc,CAACyB,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL9C,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;MACjC;MACA0C,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;MAC5B0C,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM2C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAG/B,WAAW,CACxBgC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAACzC,WAAW,CAAC0C,GAAG,CAAC,CAAC;IAC/DrC,cAAc,CAAC4B,OAAO,CAAC;EACzB,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACd,IAAI0B,WAAW,IAAIF,WAAW,EAAE;MAC9BgC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC9B,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM2C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BjB,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMjD,WAAW,CAAC,CAAC;MACpC,IAAIiD,QAAQ,CAACC,OAAO,EAAE;QACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC;QAC7BnB,WAAW,CAAC;UACVC,IAAI,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEe,QAAQ,CAACE,IAAI,CAACjB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAAChB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEY,QAAQ,CAACE,IAAI,CAACc,KAAK,IAAI,EAAE;UACjC7B,KAAK,EAAEa,QAAQ,CAACE,IAAI,CAACf,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEW,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACZ,YAAY,EAAE;UAC9BC,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACZ,YAAY,CAAC;QAC7C;QACAS,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACL3C,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR0C,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAIqE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEpC,IAAI;MAAEqC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAItC,IAAI,KAAK,aAAa,IAAIqC,KAAK,CAACE,MAAM,GAAG,EAAE,EAAE;IACjD,IAAIvC,IAAI,KAAK,OAAO,IAAIqC,KAAK,MAAKjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAIkC,KAAK,KAAK,EAAE,EAAE;MACpExB,qBAAqB,CAACwB,KAAK,CAAC;MAC5B1B,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAEyC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACxC,IAAI,GAAGqC,KAAK;MACb,IAAIrC,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B1C,WAAW,CAAC;MACVC,IAAI,EAAEZ,WAAW,CAACY,IAAI;MACtBC,KAAK,EAAEb,WAAW,CAACa,KAAK;MACxBC,MAAM,EAAEd,WAAW,CAACc,MAAM;MAC1BE,MAAM,EAAEhB,WAAW,CAAC4C,KAAK;MACzB7B,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBE,WAAW,EAAEjB,WAAW,CAACiB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAM+C,cAAc,GAAG,MAAOzC,KAAK,IAAK;IACtCa,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAM9C,OAAO,CAAC;QAAE+B;MAAM,CAAC,CAAC;MACzC,IAAIe,QAAQ,CAACC,OAAO,EAAE;QACpB7C,OAAO,CAAC6C,OAAO,CAAC,0BAA0B,CAAC;QAC3CtB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAACO,QAAQ,CAACE,IAAI,CAAC;MACtC,CAAC,MAAM;QACL9C,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;QAC/BqE,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;MAC5BqE,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACR3B,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMkE,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/C,IAAI,CAAC9C,QAAQ,CAACE,IAAI,EAAE,OAAO5B,OAAO,CAAC+C,KAAK,CAAC,yBAAyB,CAAC;IACnE,IAAI,CAACrB,QAAQ,CAACM,MAAM,EAAE,OAAOhC,OAAO,CAAC+C,KAAK,CAAC,wBAAwB,CAAC;IAEpE,IACE,CAACyB,OAAO,IACR9C,QAAQ,CAACG,KAAK,KAAKb,WAAW,CAACa,KAAK,EACpC;MACA,OAAOyC,cAAc,CAAC5C,QAAQ,CAACG,KAAK,CAAC;IACvC;IAEAa,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMhD,cAAc,CAAC;QACpC,GAAG8B,QAAQ;QACX8B,MAAM,EAAExC,WAAW,CAAC0C;MACtB,CAAC,CAAC;MACF,IAAId,QAAQ,CAACC,OAAO,EAAE;QACpB7C,OAAO,CAAC6C,OAAO,CAACD,QAAQ,CAAC5C,OAAO,CAAC;QACjCuB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAAC,IAAI,CAAC;QAC3BsB,WAAW,CAAC,CAAC;QACb,IAAIf,QAAQ,CAAC6B,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACL7E,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR0C,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMyE,wBAAwB,GAAGA,CAAA,KAAM;IACrCnD,WAAW,CAAEyC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPrC,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsC,uBAAuB,GAAGA,CAAA,KAAM;IACpCxC,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuC,iBAAiB,GAAG,MAAOhB,CAAC,IAAK;IACrC,MAAMiB,IAAI,GAAGjB,CAAC,CAACE,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCpF,OAAO,CAAC+C,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACF;;MAEA;MACA,IAAIkC,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BrF,OAAO,CAAC+C,KAAK,CAAC,oCAAoC,CAAC;QACnD;MACF;MAEAZ,eAAe,CAAC8C,IAAI,CAAC;;MAErB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM/D,eAAe,CAAC6D,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;MAE1B;MACA,MAAMnC,IAAI,GAAG,IAAI6C,QAAQ,CAAC,CAAC;MAC3B7C,IAAI,CAAC8C,MAAM,CAAC,cAAc,EAAEX,IAAI,CAAC;MACjCvC,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI;QACF,MAAMsC,QAAQ,GAAG,MAAM/C,eAAe,CAACiD,IAAI,CAAC;QAC5CJ,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;QACvB,IAAIuC,QAAQ,CAACC,OAAO,EAAE;UACpB7C,OAAO,CAAC6C,OAAO,CAAC,uCAAuC,CAAC;UACxDc,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL3D,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;QACdL,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;QACvBL,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,IAAI,kCAAkC,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAM6F,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAM/C,IAAI,GAAG,IAAI6C,QAAQ,CAAC,CAAC;IAC3B7C,IAAI,CAAC8C,MAAM,CAAC,cAAc,EAAE1D,YAAY,CAAC;IACzCQ,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAM/C,eAAe,CAACiD,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpB7C,OAAO,CAAC6C,OAAO,CAAC,6BAA6B,CAAC;QAC9Cc,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL3D,OAAO,CAAC+C,KAAK,CAACH,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR0C,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMyF,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACC,GAAG,KAAK5D,kBAAkB,EAAE;MACrCmC,YAAY,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLxE,OAAO,CAAC+C,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACdmE,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhD,OAAA;IAAKsF,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjFvF,OAAA;MAAKsF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CvF,OAAA;QAAKsF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhCvF,OAAA;UAAKsF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvF,OAAA;YAAIsF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE3F,OAAA;YAAGsF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAGN3F,OAAA;UAAKsF,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7DvF,OAAA;YAAKsF,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBvF,OAAA;cAAKsF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE9CvF,OAAA;gBAAKsF,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BvF,OAAA;kBACEsF,SAAS,EAAC,oIAAoI;kBAC9IM,KAAK,EAAE;oBACLC,UAAU,EAAE,SAAS;oBACrBC,SAAS,EAAE,6BAA6B;oBACxCC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBACFC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;kBAAAb,QAAA,GAEnElF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEkB,YAAY,gBACxBvB,OAAA;oBACEqG,GAAG,EAAEhG,WAAW,CAACkB,YAAa;oBAC9B+E,GAAG,EAAC,SAAS;oBACbhB,SAAS,EAAC,yCAAyC;oBACnDiB,OAAO,EAAGlD,CAAC,IAAK;sBACdA,CAAC,CAACE,MAAM,CAACqC,KAAK,CAACY,OAAO,GAAG,MAAM;sBAC/BnD,CAAC,CAACE,MAAM,CAACkD,WAAW,CAACb,KAAK,CAACY,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACA,IAAI,eACR3F,OAAA;oBACEsF,SAAS,EAAC,2EAA2E;oBACrFM,KAAK,EAAE;sBACLC,UAAU,EAAE,SAAS;sBACrBa,KAAK,EAAE,SAAS;sBAChBC,QAAQ,EAAE;oBACZ,CAAE;oBAAApB,QAAA,EAED,CAAAlF,WAAW,aAAXA,WAAW,wBAAAF,iBAAA,GAAXE,WAAW,CAAEY,IAAI,cAAAd,iBAAA,wBAAAC,qBAAA,GAAjBD,iBAAA,CAAmByG,MAAM,CAAC,CAAC,CAAC,cAAAxG,qBAAA,uBAA5BA,qBAAA,CAA8ByG,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3F,OAAA;kBAAKsF,SAAS,EAAC,kIAAkI;kBAC5IW,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;kBAAAb,QAAA,eACvEvF,OAAA;oBAAKsF,SAAS,EAAC,oBAAoB;oBAACwB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAzB,QAAA,gBACvFvF,OAAA;sBAAMiH,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkK;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1O3F,OAAA;sBAAMiH,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3F,OAAA;kBACEqH,EAAE,EAAC,mBAAmB;kBACtB7C,IAAI,EAAC,MAAM;kBACX8C,MAAM,EAAC,SAAS;kBAChBhC,SAAS,EAAC,QAAQ;kBAClBiC,QAAQ,EAAElD;gBAAkB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN3F,OAAA;gBAAKsF,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC9DvF,OAAA;kBAAKsF,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEvF,OAAA;oBAAGsF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzD3F,OAAA;oBAAGsF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,IAAI,KAAI;kBAAM;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN3F,OAAA;kBAAKsF,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvEvF,OAAA;oBAAGsF,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D3F,OAAA;oBAAGsF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,KAAK,KAAI;kBAAkB;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACN3F,OAAA;kBAAKsF,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,gBACzEvF,OAAA;oBAAGsF,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D3F,OAAA;oBAAGsF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,KAAK,KAAI;kBAAK;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3F,OAAA;cAAKsF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDvF,OAAA;gBAAKsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBvF,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5E3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,IAAI,KAAI;kBAAc;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3F,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,KAAK,KAAI;kBAAc;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3F,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9E3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,MAAM,KAAI;kBAAc;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3F,OAAA;gBAAKsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBvF,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI;kBAAc;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3F,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,KAAK,KAAI;kBAAc;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3F,OAAA;kBAAAuF,QAAA,gBACEvF,OAAA;oBAAOsF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpF3F,OAAA;oBAAKsF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAlF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,WAAW,KAAI;kBAAc;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3F,OAAA;cAAKsF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCvF,OAAA;gBAAQsF,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EAAC;cAE7H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CA9YID,OAAO;EAAA,QAkBMR,WAAW;AAAA;AAAA+H,EAAA,GAlBxBvH,OAAO;AAgZb,eAAeA,OAAO;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}