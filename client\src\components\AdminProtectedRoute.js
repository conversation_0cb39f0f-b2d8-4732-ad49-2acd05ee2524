import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';

const AdminProtectedRoute = ({ children }) => {
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is loaded and is not an admin
    if (user && !user.isAdmin) {
      message.error('Access denied. Admin privileges required.');
      navigate('/user/hub');
    }
  }, [user, navigate]);

  // If user is not loaded yet, show loading or return null
  if (!user) {
    return null;
  }

  // If user is not admin, return null (will redirect in useEffect)
  if (!user.isAdmin) {
    return null;
  }

  // If user is admin, render the children
  return children;
};

export default AdminProtectedRoute;
