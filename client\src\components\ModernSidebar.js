import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector } from 'react-redux';
import {
  TbMenu2,
  TbX,
  TbHome,
  TbBrain,
  TbBook,
  TbRobot,
  TbChartLine,
  TbTrophy,
  TbUser,
  TbMessageCircle,
  TbCreditCard,
  TbLogout,
  TbChevronRight
} from 'react-icons/tb';

const ModernSidebar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state) => state.user);

  const navigationItems = [
    {
      title: 'Hub',
      description: 'Main dashboard',
      icon: TbHome,
      path: '/user/hub',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: TbBrain,
      path: '/user/quiz',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: TbBook,
      path: '/user/study-material',
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'Ask AI',
      description: 'Get instant help',
      icon: TbRobot,
      path: '/user/chat',
      color: 'from-orange-500 to-orange-600'
    },
    {
      title: 'Reports',
      description: 'Track progress',
      icon: TbChartLine,
      path: '/user/reports',
      color: 'from-red-500 to-red-600'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: TbTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: 'Profile',
      description: 'Manage account',
      icon: TbUser,
      path: '/profile',
      color: 'from-indigo-500 to-indigo-600'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: TbMessageCircle,
      path: '/forum',
      color: 'from-pink-500 to-pink-600'
    },
    {
      title: 'Plans',
      description: 'Upgrade learning',
      icon: TbCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setIsOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    navigate("/login");
  };

  const isActivePath = (path) => {
    return location.pathname === path || location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105"
        title={isOpen ? "Close Menu" : "Open Menu"}
      >
        {isOpen ? (
          <TbX className="w-6 h-6 text-gray-700" />
        ) : (
          <TbMenu2 className="w-6 h-6 text-gray-700" />
        )}
      </button>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: -400, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -400, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-50 overflow-y-auto"
          >
            {/* Header */}
            <div className="p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                  <span className="text-2xl">🧠</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold">BrainWave</h1>
                  <p className="text-blue-200 text-sm">Study Smarter</p>
                </div>
              </div>
              
              {/* User Profile */}
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <TbUser className="w-6 h-6" />
                  </div>
                  <div>
                    <p className="font-medium">{user?.name}</p>
                    <p className="text-blue-200 text-sm">Class {user?.class}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="p-4 space-y-2">
              {navigationItems.map((item, index) => {
                const IconComponent = item.icon;
                const isActive = isActivePath(item.path);
                
                return (
                  <motion.button
                    key={item.path}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'
                        : 'hover:bg-gray-50 border-2 border-transparent'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}>
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <div className="text-left">
                        <p className={`font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`}>
                          {item.title}
                        </p>
                        <p className={`text-sm ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                          {item.description}
                        </p>
                      </div>
                    </div>
                    <TbChevronRight className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                  </motion.button>
                );
              })}
            </div>

            {/* Logout Button */}
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 p-4 rounded-xl bg-red-50 hover:bg-red-100 border-2 border-red-200 transition-all duration-200"
              >
                <div className="w-10 h-10 rounded-lg bg-red-500 flex items-center justify-center">
                  <TbLogout className="w-5 h-5 text-white" />
                </div>
                <div className="text-left">
                  <p className="font-medium text-red-700">Logout</p>
                  <p className="text-sm text-red-600">Sign out of account</p>
                </div>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ModernSidebar;
