{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const inspiringQuotes = [\"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\", \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\", \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\"];\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  const navigationItems = [{\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help from AI',\n    icon: FaRobot,\n    path: '/user/chat',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: 'Reports',\n    description: 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-orange-500 to-orange-600',\n    hoverColor: 'from-orange-600 to-orange-700'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Profile',\n    description: 'Manage your account',\n    icon: FaUser,\n    path: '/user/profile',\n    color: 'from-indigo-500 to-indigo-600',\n    hoverColor: 'from-indigo-600 to-indigo-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/user/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade your learning',\n    icon: FaCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600',\n    hoverColor: 'from-emerald-600 to-emerald-700'\n  }, {\n    title: 'About Us',\n    description: 'Learn about our mission',\n    icon: FaInfoCircle,\n    path: '/user/about-us',\n    color: 'from-cyan-500 to-cyan-600',\n    hoverColor: 'from-cyan-600 to-cyan-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-welcome relative overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"relative\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"absolute inset-0 rounded-2xl\",\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: [0.1, 0.3, 0.1],\n                scale: [0.8, 1.1, 0.8]\n              },\n              transition: {\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              style: {\n                background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.15), rgba(16, 185, 129, 0.1), transparent)',\n                filter: 'blur(20px)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative z-10 flex items-center justify-center\",\n              initial: {\n                y: 50,\n                opacity: 0\n              },\n              animate: {\n                y: 0,\n                opacity: 1\n              },\n              transition: {\n                duration: 1.2,\n                delay: 0.3,\n                ease: \"easeOut\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"relative mr-4\",\n                initial: {\n                  x: -100,\n                  opacity: 0,\n                  rotateX: -90\n                },\n                animate: {\n                  x: 0,\n                  opacity: 1,\n                  rotateX: 0\n                },\n                transition: {\n                  duration: 1.5,\n                  delay: 0.6,\n                  ease: \"easeOut\"\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateY: [0, 5, -5, 0],\n                  transition: {\n                    duration: 0.6,\n                    ease: \"easeInOut\"\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight\",\n                  style: {\n                    background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.05em',\n                    textShadow: '0 0 40px rgba(59, 130, 246, 0.3)'\n                  },\n                  animate: {\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                  },\n                  transition: {\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  children: \"Study\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -top-3 -right-3\",\n                  animate: {\n                    y: [-5, 5, -5],\n                    rotate: [0, 10, -10, 0],\n                    opacity: [0.7, 1, 0.7]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  style: {\n                    fontSize: '1.5rem',\n                    filter: 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.5))'\n                  },\n                  children: \"\\uD83D\\uDCD6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"relative\",\n                initial: {\n                  x: 100,\n                  opacity: 0,\n                  rotateX: 90\n                },\n                animate: {\n                  x: 0,\n                  opacity: 1,\n                  rotateX: 0\n                },\n                transition: {\n                  duration: 1.5,\n                  delay: 0.9,\n                  ease: \"easeOut\"\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateY: [0, -5, 5, 0],\n                  transition: {\n                    duration: 0.6,\n                    ease: \"easeInOut\"\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight\",\n                  style: {\n                    background: 'linear-gradient(135deg, #064e3b 0%, #059669 25%, #10b981 50%, #34d399 75%, #a7f3d0 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.05em',\n                    textShadow: '0 0 40px rgba(16, 185, 129, 0.3)'\n                  },\n                  animate: {\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                  },\n                  transition: {\n                    duration: 5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  },\n                  children: \"Smarter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -top-3 -right-3\",\n                  animate: {\n                    y: [5, -5, 5],\n                    rotate: [0, -15, 15, 0],\n                    scale: [1, 1.2, 1],\n                    opacity: [0.7, 1, 0.7]\n                  },\n                  transition: {\n                    duration: 2.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 0.5\n                  },\n                  style: {\n                    fontSize: '1.5rem',\n                    filter: 'drop-shadow(0 0 10px rgba(16, 185, 129, 0.5))'\n                  },\n                  children: \"\\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-center mt-6\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 1,\n                delay: 1.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"text-2xl sm:text-3xl font-bold\",\n                style: {\n                  background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  textShadow: '0 0 20px rgba(245, 158, 11, 0.3)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                whileHover: {\n                  scale: 1.1,\n                  transition: {\n                    duration: 0.3\n                  }\n                },\n                children: [user === null || user === void 0 ? void 0 : user.name, \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute top-0 right-0\",\n                animate: {\n                  rotate: [0, 360],\n                  scale: [0.8, 1.3, 0.8],\n                  opacity: [0.5, 1, 0.5]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                style: {\n                  fontSize: '1.2rem',\n                  filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.6))'\n                },\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n              initial: {\n                width: 0,\n                opacity: 0\n              },\n              animate: {\n                width: '80%',\n                opacity: 1\n              },\n              transition: {\n                duration: 2,\n                delay: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"h-1 rounded-full relative overflow-hidden\",\n                style: {\n                  background: 'linear-gradient(90deg, transparent, #3b82f6, #10b981, #f59e0b, transparent)',\n                  boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'\n                },\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0))',\n                    width: '30%'\n                  },\n                  animate: {\n                    x: ['-100%', '400%']\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 2.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: \"Ready to shine today? \\u2728 Choose your learning path below.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"yUKD1BuiW8zY+bONKPa0/Mswuw0=\", false, function () {\n  return [useNavigate, useSelector];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "currentQuote", "setCurrentQuote", "handleLogout", "localStorage", "removeItem", "success", "inspiringQuotes", "interval", "setInterval", "prev", "length", "clearInterval", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "repeat", "Infinity", "ease", "style", "background", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "x", "rotateX", "whileHover", "rotateY", "span", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "fontFamily", "letterSpacing", "textShadow", "backgroundPosition", "rotate", "fontSize", "name", "width", "boxShadow", "marginRight", "marginLeft", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - <PERSON>\",\n    \"The future belongs to those who believe in the beauty of their dreams. - <PERSON>\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help from AI',\n      icon: FaRobot,\n      path: '/user/chat',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-orange-500 to-orange-600',\n      hoverColor: 'from-orange-600 to-orange-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/user/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/user/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade your learning',\n      icon: FaCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600',\n      hoverColor: 'from-emerald-600 to-emerald-700'\n    },\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Professional Premium Study Smarter Animation */}\n          <div className=\"hub-welcome relative overflow-hidden\">\n            <motion.div\n              className=\"relative\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 1.5 }}\n            >\n              {/* Premium Background Glow */}\n              <motion.div\n                className=\"absolute inset-0 rounded-2xl\"\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{\n                  opacity: [0.1, 0.3, 0.1],\n                  scale: [0.8, 1.1, 0.8]\n                }}\n                transition={{\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                style={{\n                  background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.15), rgba(16, 185, 129, 0.1), transparent)',\n                  filter: 'blur(20px)'\n                }}\n              />\n\n              {/* Main Text Container */}\n              <motion.div\n                className=\"relative z-10 flex items-center justify-center\"\n                initial={{ y: 50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 1.2, delay: 0.3, ease: \"easeOut\" }}\n              >\n                {/* Study - Premium Design */}\n                <motion.div\n                  className=\"relative mr-4\"\n                  initial={{ x: -100, opacity: 0, rotateX: -90 }}\n                  animate={{\n                    x: 0,\n                    opacity: 1,\n                    rotateX: 0\n                  }}\n                  transition={{\n                    duration: 1.5,\n                    delay: 0.6,\n                    ease: \"easeOut\"\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateY: [0, 5, -5, 0],\n                    transition: { duration: 0.6, ease: \"easeInOut\" }\n                  }}\n                >\n                  <motion.span\n                    className=\"block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight\"\n                    style={{\n                      background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      backgroundClip: 'text',\n                      fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                      letterSpacing: '-0.05em',\n                      textShadow: '0 0 40px rgba(59, 130, 246, 0.3)'\n                    }}\n                    animate={{\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    }}\n                    transition={{\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }}\n                  >\n                    Study\n                  </motion.span>\n\n                  {/* Premium Floating Elements */}\n                  <motion.div\n                    className=\"absolute -top-3 -right-3\"\n                    animate={{\n                      y: [-5, 5, -5],\n                      rotate: [0, 10, -10, 0],\n                      opacity: [0.7, 1, 0.7]\n                    }}\n                    transition={{\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }}\n                    style={{\n                      fontSize: '1.5rem',\n                      filter: 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.5))'\n                    }}\n                  >\n                    📖\n                  </motion.div>\n                </motion.div>\n\n                {/* Smarter - Premium Design */}\n                <motion.div\n                  className=\"relative\"\n                  initial={{ x: 100, opacity: 0, rotateX: 90 }}\n                  animate={{\n                    x: 0,\n                    opacity: 1,\n                    rotateX: 0\n                  }}\n                  transition={{\n                    duration: 1.5,\n                    delay: 0.9,\n                    ease: \"easeOut\"\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateY: [0, -5, 5, 0],\n                    transition: { duration: 0.6, ease: \"easeInOut\" }\n                  }}\n                >\n                  <motion.span\n                    className=\"block text-4xl sm:text-5xl md:text-6xl font-black tracking-tight\"\n                    style={{\n                      background: 'linear-gradient(135deg, #064e3b 0%, #059669 25%, #10b981 50%, #34d399 75%, #a7f3d0 100%)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      backgroundClip: 'text',\n                      fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                      letterSpacing: '-0.05em',\n                      textShadow: '0 0 40px rgba(16, 185, 129, 0.3)'\n                    }}\n                    animate={{\n                      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                    }}\n                    transition={{\n                      duration: 5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 1\n                    }}\n                  >\n                    Smarter\n                  </motion.span>\n\n                  {/* Premium Floating Elements */}\n                  <motion.div\n                    className=\"absolute -top-3 -right-3\"\n                    animate={{\n                      y: [5, -5, 5],\n                      rotate: [0, -15, 15, 0],\n                      scale: [1, 1.2, 1],\n                      opacity: [0.7, 1, 0.7]\n                    }}\n                    transition={{\n                      duration: 2.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 0.5\n                    }}\n                    style={{\n                      fontSize: '1.5rem',\n                      filter: 'drop-shadow(0 0 10px rgba(16, 185, 129, 0.5))'\n                    }}\n                  >\n                    🎯\n                  </motion.div>\n                </motion.div>\n              </motion.div>\n\n              {/* User Name - Premium Style */}\n              <motion.div\n                className=\"text-center mt-6\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 1, delay: 1.5 }}\n              >\n                <motion.span\n                  className=\"text-2xl sm:text-3xl font-bold\"\n                  style={{\n                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    textShadow: '0 0 20px rgba(245, 158, 11, 0.3)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    transition: { duration: 0.3 }\n                  }}\n                >\n                  {user?.name}!\n                </motion.span>\n\n                {/* Premium Sparkle Effect */}\n                <motion.div\n                  className=\"absolute top-0 right-0\"\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [0.8, 1.3, 0.8],\n                    opacity: [0.5, 1, 0.5]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  style={{\n                    fontSize: '1.2rem',\n                    filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.6))'\n                  }}\n                >\n                  ⭐\n                </motion.div>\n              </motion.div>\n\n              {/* Premium Underline Animation */}\n              <motion.div\n                className=\"absolute -bottom-4 left-1/2 transform -translate-x-1/2\"\n                initial={{ width: 0, opacity: 0 }}\n                animate={{\n                  width: '80%',\n                  opacity: 1\n                }}\n                transition={{ duration: 2, delay: 2 }}\n              >\n                <motion.div\n                  className=\"h-1 rounded-full relative overflow-hidden\"\n                  style={{\n                    background: 'linear-gradient(90deg, transparent, #3b82f6, #10b981, #f59e0b, transparent)',\n                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'\n                  }}\n                >\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full\"\n                    style={{\n                      background: 'linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0))',\n                      width: '30%'\n                    }}\n                    animate={{\n                      x: ['-100%', '400%']\n                    }}\n                    transition={{\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 2.5\n                    }}\n                  />\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                >\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA1B,OAAO,CAAC2B,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAR,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMS,eAAe,GAAG,CACtB,+FAA+F,EAC/F,4FAA4F,EAC5F,4GAA4G,EAC5G,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,CAC5B;EAEDhC,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCP,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEnC,gBAAgB;IACtBoC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAElC,MAAM;IACZmC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAExB,OAAO;IACbyB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEjC,WAAW;IACjBkC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE3B,QAAQ;IACd4B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEhC,MAAM;IACZiC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE/B,UAAU;IAChBgC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE9B,YAAY;IAClB+B,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,iCAAiC;IACxCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAE7B,YAAY;IAClB8B,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1B,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAG1B1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAKtB1B,OAAA;UAAKyB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,eACnD1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;YACTF,SAAS,EAAC,UAAU;YACpBG,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,gBAG9B1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,8BAA8B;cACxCG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEK,KAAK,EAAE;cAAI,CAAE;cACpCH,OAAO,EAAE;gBACPF,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBACxBK,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;cACvB,CAAE;cACFF,UAAU,EAAE;gBACVC,QAAQ,EAAE,CAAC;gBACXE,MAAM,EAAEC,QAAQ;gBAChBC,IAAI,EAAE;cACR,CAAE;cACFC,KAAK,EAAE;gBACLC,UAAU,EAAE,oGAAoG;gBAChHC,MAAM,EAAE;cACV;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGF5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,gDAAgD;cAC1DG,OAAO,EAAE;gBAAEE,CAAC,EAAE,EAAE;gBAAED,OAAO,EAAE;cAAE,CAAE;cAC/BE,OAAO,EAAE;gBAAED,CAAC,EAAE,CAAC;gBAAED,OAAO,EAAE;cAAE,CAAE;cAC9BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEY,KAAK,EAAE,GAAG;gBAAER,IAAI,EAAE;cAAU,CAAE;cAAAX,QAAA,gBAG3D1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,eAAe;gBACzBG,OAAO,EAAE;kBAAEkB,CAAC,EAAE,CAAC,GAAG;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkB,OAAO,EAAE,CAAC;gBAAG,CAAE;gBAC/ChB,OAAO,EAAE;kBACPe,CAAC,EAAE,CAAC;kBACJjB,OAAO,EAAE,CAAC;kBACVkB,OAAO,EAAE;gBACX,CAAE;gBACFf,UAAU,EAAE;kBACVC,QAAQ,EAAE,GAAG;kBACbY,KAAK,EAAE,GAAG;kBACVR,IAAI,EAAE;gBACR,CAAE;gBACFW,UAAU,EAAE;kBACVd,KAAK,EAAE,IAAI;kBACXe,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACtBjB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEI,IAAI,EAAE;kBAAY;gBACjD,CAAE;gBAAAX,QAAA,gBAEF1B,OAAA,CAACjB,MAAM,CAACmE,IAAI;kBACVzB,SAAS,EAAC,kEAAkE;kBAC5Ea,KAAK,EAAE;oBACLC,UAAU,EAAE,0FAA0F;oBACtGY,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtBC,UAAU,EAAE,kDAAkD;oBAC9DC,aAAa,EAAE,SAAS;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBACFzB,OAAO,EAAE;oBACP0B,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;kBACrD,CAAE;kBACFzB,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXE,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBAAAX,QAAA,EACH;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAGd5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;kBACTF,SAAS,EAAC,0BAA0B;kBACpCM,OAAO,EAAE;oBACPD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACd4B,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACvB7B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;kBACvB,CAAE;kBACFG,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXE,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFC,KAAK,EAAE;oBACLqB,QAAQ,EAAE,QAAQ;oBAClBnB,MAAM,EAAE;kBACV,CAAE;kBAAAd,QAAA,EACH;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGb5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,UAAU;gBACpBG,OAAO,EAAE;kBAAEkB,CAAC,EAAE,GAAG;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkB,OAAO,EAAE;gBAAG,CAAE;gBAC7ChB,OAAO,EAAE;kBACPe,CAAC,EAAE,CAAC;kBACJjB,OAAO,EAAE,CAAC;kBACVkB,OAAO,EAAE;gBACX,CAAE;gBACFf,UAAU,EAAE;kBACVC,QAAQ,EAAE,GAAG;kBACbY,KAAK,EAAE,GAAG;kBACVR,IAAI,EAAE;gBACR,CAAE;gBACFW,UAAU,EAAE;kBACVd,KAAK,EAAE,IAAI;kBACXe,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACtBjB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEI,IAAI,EAAE;kBAAY;gBACjD,CAAE;gBAAAX,QAAA,gBAEF1B,OAAA,CAACjB,MAAM,CAACmE,IAAI;kBACVzB,SAAS,EAAC,kEAAkE;kBAC5Ea,KAAK,EAAE;oBACLC,UAAU,EAAE,0FAA0F;oBACtGY,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtBC,UAAU,EAAE,kDAAkD;oBAC9DC,aAAa,EAAE,SAAS;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBACFzB,OAAO,EAAE;oBACP0B,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;kBACrD,CAAE;kBACFzB,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXE,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE,WAAW;oBACjBQ,KAAK,EAAE;kBACT,CAAE;kBAAAnB,QAAA,EACH;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAGd5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;kBACTF,SAAS,EAAC,0BAA0B;kBACpCM,OAAO,EAAE;oBACPD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACb4B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBACvBxB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;oBAClBL,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;kBACvB,CAAE;kBACFG,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbE,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE,WAAW;oBACjBQ,KAAK,EAAE;kBACT,CAAE;kBACFP,KAAK,EAAE;oBACLqB,QAAQ,EAAE,QAAQ;oBAClBnB,MAAM,EAAE;kBACV,CAAE;kBAAAd,QAAA,EACH;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGb5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,kBAAkB;cAC5BG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAI,CAAE;cAAAnB,QAAA,gBAExC1B,OAAA,CAACjB,MAAM,CAACmE,IAAI;gBACVzB,SAAS,EAAC,gCAAgC;gBAC1Ca,KAAK,EAAE;kBACLC,UAAU,EAAE,mDAAmD;kBAC/DY,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBG,UAAU,EAAE;gBACd,CAAE;gBACFzB,OAAO,EAAE;kBACP0B,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;gBACrD,CAAE;gBACFzB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXE,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFW,UAAU,EAAE;kBACVd,KAAK,EAAE,GAAG;kBACVF,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,GAEDtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,EAAC,GACd;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAGd5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,wBAAwB;gBAClCM,OAAO,EAAE;kBACP2B,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;kBAChBxB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;kBACtBL,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXE,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFC,KAAK,EAAE;kBACLqB,QAAQ,EAAE,QAAQ;kBAClBnB,MAAM,EAAE;gBACV,CAAE;gBAAAd,QAAA,EACH;cAED;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGb5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,wDAAwD;cAClEG,OAAO,EAAE;gBAAEiC,KAAK,EAAE,CAAC;gBAAEhC,OAAO,EAAE;cAAE,CAAE;cAClCE,OAAO,EAAE;gBACP8B,KAAK,EAAE,KAAK;gBACZhC,OAAO,EAAE;cACX,CAAE;cACFG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAE,CAAE;cAAAnB,QAAA,eAEtC1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,2CAA2C;gBACrDa,KAAK,EAAE;kBACLC,UAAU,EAAE,6EAA6E;kBACzFuB,SAAS,EAAE;gBACb,CAAE;gBAAApC,QAAA,eAEF1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;kBACTF,SAAS,EAAC,+BAA+B;kBACzCa,KAAK,EAAE;oBACLC,UAAU,EAAE,yFAAyF;oBACrGsB,KAAK,EAAE;kBACT,CAAE;kBACF9B,OAAO,EAAE;oBACPe,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;kBACrB,CAAE;kBACFd,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXE,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE,WAAW;oBACjBQ,KAAK,EAAE;kBACT;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN5C,OAAA;UAAGyB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ5C,OAAA;UAAKyB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1B,OAAA,CAACL,MAAM;YAAC2C,KAAK,EAAE;cAAEf,KAAK,EAAE,SAAS;cAAEwC,WAAW,EAAE;YAAS;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAChC,eAAe,CAACN,YAAY,CAAC,EAAC,IAChC,eAAAN,OAAA,CAACL,MAAM;YAAC2C,KAAK,EAAE;cAAEf,KAAK,EAAE,SAAS;cAAEyC,UAAU,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D5C,OAAA;YAAKsC,KAAK,EAAE;cAAEqB,QAAQ,EAAE,UAAU;cAAEpC,KAAK,EAAE,SAAS;cAAE0C,SAAS,EAAE;YAAS,CAAE;YAAAvC,QAAA,EAAC;UAE7E;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEb5C,OAAA;QAAKyB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC1B,OAAA;UAAKyB,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAACgD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC9C,IAAI;YAC/B,oBACErB,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEY,KAAK,EAAEuB,KAAK,GAAG;cAAI,CAAE;cAClD3C,SAAS,EAAG,kBAAiB0C,IAAI,CAAC3C,UAAW,IAAG2C,IAAI,CAAC5C,KAAM,EAAE;cAC7D+C,OAAO,EAAEA,CAAA,KAAMnE,QAAQ,CAACgE,IAAI,CAAC7C,IAAI,CAAE;cACnCiD,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtCxE,QAAQ,CAACgE,IAAI,CAAC7C,IAAI,CAAC;gBACrB;cACF,CAAE;cAAAI,QAAA,gBAEF1B,OAAA;gBAAKyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1B,OAAA,CAACqE,aAAa;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAEN5C,OAAA;gBAAIyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3ByC,IAAI,CAAChD;cAAK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEL5C,OAAA;gBAAGyB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCyC,IAAI,CAAC/C;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GAxBCuB,IAAI,CAAChD,KAAK;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5C,OAAA,CAACjB,MAAM,CAAC4C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEY,KAAK,EAAE;UAAI,CAAE;UAC1CpB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjC1B,OAAA;YAAKyB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1B,OAAA,CAACP,eAAe;cAACgC,SAAS,EAAC;YAAuC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE5C,OAAA;cAAA0B,QAAA,EAAM;YAAkC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C5C,OAAA,CAACJ,QAAQ;cAAC6B,SAAS,EAAC;YAAuC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAhcID,GAAG;EAAA,QACUpB,WAAW,EACXC,WAAW;AAAA;AAAA8F,EAAA,GAFxB3E,GAAG;AAkcT,eAAeA,GAAG;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}