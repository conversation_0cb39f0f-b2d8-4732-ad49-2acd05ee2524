{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions, deleteQuestion, updateQuestion, updateReplyStatus } from \"../../../apicalls/forum\";\nimport image from \"../../../assets/person.png\";\nimport { FaPencilAlt } from \"react-icons/fa\";\nimport { MdDelete, MdMessage } from \"react-icons/md\";\nimport { FaCheck } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState(\"\");\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [editQuestion, setEditQuestion] = useState(null);\n  const [form] = Form.useForm();\n  const [form2] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [limit] = useState(10);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async page => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit\n      }); // Pass query params to API call\n      if (response.success) {\n        console.log(response.data);\n        setQuestions(response.data); // No need to reverse as backend will handle order\n        // setCurrentPage(page);\n        setTotalQuestions(response.totalQuestions);\n        setTotalPages(response.totalPages);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    fetchQuestions(currentPage);\n  }, [currentPage, limit]);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n          setUserData(response.data);\n          await fetchQuestions();\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  const handleEdit = question => {\n    setEditQuestion(question);\n  };\n  const handleDelete = async question => {\n    try {\n      const confirmDelete = window.confirm(\"Are you sure you want to delete this question?\");\n      if (!confirmDelete) {\n        return;\n      }\n      const response = await deleteQuestion(question._id);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleUpdateQuestion = async values => {\n    try {\n      const response = await updateQuestion(values, editQuestion._id);\n      if (response.success) {\n        message.success(response.message);\n        setEditQuestion(null);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleCancelUpdate = () => {\n    setEditQuestion(\"\");\n  };\n  const handleCancelAdd = () => {\n    setAskQuestionVisible(false);\n    form.resetFields();\n  };\n  useEffect(() => {\n    if (editQuestion) {\n      form2.setFieldsValue({\n        title: editQuestion.title,\n        body: editQuestion.body\n      });\n    } else {\n      form2.resetFields();\n    }\n  }, [editQuestion]);\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        replyId,\n        status\n      }, questionId);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-discuss-line text-2xl text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: [\"Community \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto mb-8\",\n          children: \"Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setAskQuestionVisible(true),\n          className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-add-line text-xl mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), \"Ask a Question\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-question-line text-white text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          onFinish: handleAskQuestion,\n          layout: \"vertical\",\n          className: \"modern-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Question Title\",\n            rules: [{\n              required: true,\n              message: \"Please enter a descriptive title\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"What would you like to know?\",\n              className: \"h-12 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"body\",\n            label: \"Question Details\",\n            rules: [{\n              required: true,\n              message: \"Please provide more details about your question\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 6,\n              placeholder: \"Describe your question in detail. The more information you provide, the better answers you'll receive.\",\n              className: \"text-base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            className: \"mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-send-plane-line mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), \"Post Question\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCancelAdd,\n                className: \"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this), questions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-loader-4-line text-2xl text-gray-400 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Loading discussions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: questions.map(question => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: question.user.profileImage || image,\n                    alt: \"profile\",\n                    size: 48,\n                    className: \"border-2 border-blue-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: question.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: new Date(question.createdAt).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: (userData._id === question.user._id || userData.isAdmin) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(question),\n                    className: \"flex items-center px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                    children: /*#__PURE__*/_jsxDEV(FaPencilAlt, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(question),\n                    className: \"flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\",\n                    children: /*#__PURE__*/_jsxDEV(MdDelete, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-3\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed mb-6\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between pt-4 border-t border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => toggleReplies(question._id),\n                  className: \"flex items-center px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"ri-eye-line mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this), expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleReply(question._id),\n                  className: \"flex items-center px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"ri-reply-line mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this), \"Reply\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center px-3 py-2 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(MdMessage, {\n                  className: \"w-4 h-4 text-gray-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: question.replies.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), editQuestion && editQuestion._id === question._id && /*#__PURE__*/_jsxDEV(Form, {\n            form: form2,\n            onFinish: handleUpdateQuestion,\n            layout: \"vertical\",\n            initialValues: {\n              title: editQuestion.title,\n              body: editQuestion.body\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Title\",\n              rules: [{\n                required: true,\n                message: \"Please enter the title\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                style: {\n                  padding: \"18px 12px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Body\",\n              rules: [{\n                required: true,\n                message: \"Please enter the body\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                children: \"Update Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCancelUpdate,\n                style: {\n                  marginLeft: 10\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 space-y-4 bg-gray-50 rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-chat-3-line mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), \"Replies (\", question.replies.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), question.replies.map((reply, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `bg-white rounded-lg p-4 shadow-sm border-l-4 ${reply.user.isAdmin ? \"border-purple-500 bg-purple-50\" : reply.isVerified ? \"border-green-500 bg-green-50\" : \"border-gray-300\"}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: reply.user.profileImage || image,\n                    alt: \"profile\",\n                    size: 40,\n                    className: \"ring-2 ring-white shadow-md\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: reply.user.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 29\n                      }, this), reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full\",\n                        children: \"Admin\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 477,\n                        columnNumber: 31\n                      }, this), reply.isVerified && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                          className: \"w-4 h-4 text-green-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 483,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\",\n                          children: \"Verified\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 484,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: new Date(reply.createdAt).toLocaleString(undefined, {\n                        minute: \"numeric\",\n                        hour: \"numeric\",\n                        day: \"numeric\",\n                        month: \"short\",\n                        year: \"numeric\"\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-700 leading-relaxed mb-3\",\n                    children: reply.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this), isAdmin && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleUpdateStatus(question._id, reply._id, !reply.isVerified),\n                      className: `px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${reply.isVerified ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-green-100 text-green-700 hover:bg-green-200\"}`,\n                      children: reply.isVerified ? \"Disapprove\" : \"Approve\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 21\n              }, this)\n            }, reply._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: \"Please enter your reply\"\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n        current: currentPage,\n        total: totalQuestions,\n        pageSize: limit,\n        onChange: handlePageChange,\n        style: {\n          marginTop: \"20px\",\n          textAlign: \"center\"\n        },\n        showSizeChanger: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n};\n_s(Forum, \"XBuKUcnCQkZV3E4NAnmvM9R/I3I=\", false, function () {\n  return [Form.useForm, Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Pagination", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "deleteQuestion", "updateQuestion", "updateReplyStatus", "image", "FaPencilAlt", "MdDelete", "MdMessage", "FaCheck", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "editQuestion", "setEditQuestion", "form", "useForm", "form2", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "limit", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "success", "console", "log", "data", "error", "handlePageChange", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "handleEdit", "question", "handleDelete", "confirmDelete", "window", "confirm", "_id", "handleUpdateQuestion", "handleCancelUpdate", "handleCancelAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "body", "handleUpdateStatus", "replyId", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "type", "htmlType", "length", "map", "src", "user", "profileImage", "alt", "size", "Date", "createdAt", "toLocaleDateString", "year", "month", "day", "hour", "minute", "replies", "initialValues", "style", "padding", "marginLeft", "reply", "index", "isVerified", "toLocaleString", "undefined", "ref", "total", "pageSize", "onChange", "marginTop", "textAlign", "showSizeChanger", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        // setCurrentPage(page);\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestions(currentPage);\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"Forum max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Modern Header Section */}\r\n        <div className=\"text-center mb-12\">\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\">\r\n            <i className=\"ri-discuss-line text-2xl text-white\"></i>\r\n          </div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Community <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Forum</span>\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto mb-8\">\r\n            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\r\n          </p>\r\n\r\n          {/* Ask Question Button */}\r\n          <button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\"\r\n          >\r\n            <i className=\"ri-add-line text-xl mr-2\"></i>\r\n            Ask a Question\r\n          </button>\r\n        </div>\r\n\r\n        {/* Modern Ask Question Form */}\r\n        {askQuestionVisible && (\r\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-4\">\r\n                <i className=\"ri-question-line text-white text-lg\"></i>\r\n              </div>\r\n              <h2 className=\"text-2xl font-bold text-gray-900\">Ask a Question</h2>\r\n            </div>\r\n\r\n            <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\" className=\"modern-form\">\r\n              <Form.Item\r\n                name=\"title\"\r\n                label=\"Question Title\"\r\n                rules={[{ required: true, message: \"Please enter a descriptive title\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"What would you like to know?\"\r\n                  className=\"h-12 text-lg\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"body\"\r\n                label=\"Question Details\"\r\n                rules={[{ required: true, message: \"Please provide more details about your question\" }]}\r\n              >\r\n                <Input.TextArea\r\n                  rows={6}\r\n                  placeholder=\"Describe your question in detail. The more information you provide, the better answers you'll receive.\"\r\n                  className=\"text-base\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item className=\"mb-0\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Button\r\n                    type=\"primary\"\r\n                    htmlType=\"submit\"\r\n                    className=\"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Post Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelAdd}\r\n                    className=\"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {questions.length === 0 && (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\">\r\n              <i className=\"ri-loader-4-line text-2xl text-gray-400 animate-spin\"></i>\r\n            </div>\r\n            <p className=\"text-gray-500\">Loading discussions...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Questions Grid */}\r\n        <div className=\"space-y-6\">\r\n          {questions.map((question) => (\r\n            <div key={question._id} className=\"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\">\r\n              {/* Question Header */}\r\n              <div className=\"p-6 border-b border-gray-100\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"relative\">\r\n                      <Avatar\r\n                        src={question.user.profileImage || image}\r\n                        alt=\"profile\"\r\n                        size={48}\r\n                        className=\"border-2 border-blue-100\"\r\n                      />\r\n                      <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"></div>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"font-semibold text-gray-900\">{question.user.name}</h4>\r\n                      <p className=\"text-sm text-gray-500\">\r\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric',\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {(userData._id === question.user._id || userData.isAdmin) && (\r\n                      <>\r\n                        <button\r\n                          onClick={() => handleEdit(question)}\r\n                          className=\"flex items-center px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <FaPencilAlt className=\"w-4 h-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(question)}\r\n                          className=\"flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <MdDelete className=\"w-4 h-4\" />\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Question Content */}\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{question.title}</h3>\r\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{question.body}</p>\r\n\r\n                {/* Action Bar */}\r\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <button\r\n                      onClick={() => toggleReplies(question._id)}\r\n                      className=\"flex items-center px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                    >\r\n                      <i className=\"ri-eye-line mr-2\"></i>\r\n                      {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleReply(question._id)}\r\n                      className=\"flex items-center px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200\"\r\n                    >\r\n                      <i className=\"ri-reply-line mr-2\"></i>\r\n                      Reply\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center px-3 py-2 bg-gray-50 rounded-lg\">\r\n                    <MdMessage className=\"w-4 h-4 text-gray-500 mr-2\" />\r\n                    <span className=\"text-sm font-medium text-gray-700\">{question.replies.length}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Edit Question Form */}\r\n              {editQuestion && editQuestion._id === question._id && (\r\n              <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n            )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"mt-6 space-y-4 bg-gray-50 rounded-xl p-4\">\r\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\r\n                  <i className=\"ri-chat-3-line mr-2 text-blue-600\"></i>\r\n                  Replies ({question.replies.length})\r\n                </h4>\r\n                {question.replies.map((reply, index) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`bg-white rounded-lg p-4 shadow-sm border-l-4 ${\r\n                      reply.user.isAdmin\r\n                        ? \"border-purple-500 bg-purple-50\"\r\n                        : reply.isVerified\r\n                        ? \"border-green-500 bg-green-50\"\r\n                        : \"border-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start space-x-3\">\r\n                      {/* Avatar */}\r\n                      <div className=\"flex-shrink-0\">\r\n                        <Avatar\r\n                          src={reply.user.profileImage || image}\r\n                          alt=\"profile\"\r\n                          size={40}\r\n                          className=\"ring-2 ring-white shadow-md\"\r\n                        />\r\n                      </div>\r\n\r\n                      {/* Reply Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        {/* Header */}\r\n                        <div className=\"flex items-center justify-between mb-2\">\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <h5 className=\"font-semibold text-gray-900\">{reply.user.name}</h5>\r\n                            {reply.user.isAdmin && (\r\n                              <span className=\"px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full\">\r\n                                Admin\r\n                              </span>\r\n                            )}\r\n                            {reply.isVerified && !reply.user.isAdmin && (\r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <FaCheck className=\"w-4 h-4 text-green-600\" />\r\n                                <span className=\"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\r\n                                  Verified\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <span className=\"text-sm text-gray-500\">\r\n                            {new Date(reply.createdAt).toLocaleString(undefined, {\r\n                              minute: \"numeric\",\r\n                              hour: \"numeric\",\r\n                              day: \"numeric\",\r\n                              month: \"short\",\r\n                              year: \"numeric\",\r\n                            })}\r\n                          </span>\r\n                        </div>\r\n\r\n                        {/* Reply Text */}\r\n                        <div className=\"text-gray-700 leading-relaxed mb-3\">\r\n                          {reply.text}\r\n                        </div>\r\n\r\n                        {/* Admin Actions */}\r\n                        {isAdmin && !reply.user.isAdmin && (\r\n                          <div className=\"flex justify-end\">\r\n                            <button\r\n                              onClick={() =>\r\n                                handleUpdateStatus(\r\n                                  question._id,\r\n                                  reply._id,\r\n                                  !reply.isVerified\r\n                                )\r\n                              }\r\n                              className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${\r\n                                reply.isVerified\r\n                                  ? \"bg-red-100 text-red-700 hover:bg-red-200\"\r\n                                  : \"bg-green-100 text-green-700 hover:bg-green-200\"\r\n                              }`}\r\n                            >\r\n                              {reply.isVerified ? \"Disapprove\" : \"Approve\"}\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]}>\r\n              {replyQuestionId === question._id && (\r\n                <Form\r\n                  form={form}\r\n                  onFinish={handleReplySubmit}\r\n                  layout=\"vertical\"\r\n                >\r\n                  <Form.Item\r\n                    name=\"text\"\r\n                    label=\"Your Reply\"\r\n                    rules={[\r\n                      { required: true, message: \"Please enter your reply\" },\r\n                    ]}\r\n                  >\r\n                    <Input.TextArea rows={4} />\r\n                  </Form.Item>\r\n                  <Form.Item>\r\n                    <Button type=\"primary\" htmlType=\"submit\">\r\n                      Submit Reply\r\n                    </Button>\r\n                    <Button\r\n                      onClick={() => setReplyQuestionId(null)}\r\n                      style={{ marginLeft: 10 }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </Form.Item>\r\n                </Form>\r\n              )}\r\n            </div>\r\n            </div>\r\n          ))}\r\n\r\n        </div>\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SACEC,WAAW,EACXC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,iBAAiB,QACZ,yBAAyB;AAChC,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6C,IAAI,CAAC,GAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,CAAC,GAAGxC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAC9B,MAAME,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuD,KAAK,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5B,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM0D,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACFX,QAAQ,CAACnC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+C,QAAQ,GAAG,MAAM5C,eAAe,CAAC;QAAE2C,IAAI;QAAEJ;MAAM,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIK,QAAQ,CAACC,OAAO,EAAE;QACpBC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;QAC1B5B,YAAY,CAACwB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;QAC7B;QACAP,iBAAiB,CAACG,QAAQ,CAACJ,cAAc,CAAC;QAC1CF,aAAa,CAACM,QAAQ,CAACP,UAAU,CAAC;MACpC,CAAC,MAAM;QACLjD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR4C,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDX,SAAS,CAAC,MAAM;IACdyD,cAAc,CAACP,WAAW,CAAC;EAC7B,CAAC,EAAE,CAACA,WAAW,EAAEI,KAAK,CAAC,CAAC;EAExB,MAAMW,gBAAgB,GAAIP,IAAI,IAAK;IACjCP,cAAc,CAACO,IAAI,CAAC;EACtB,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BnB,QAAQ,CAACnC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM+C,QAAQ,GAAG,MAAMzD,WAAW,CAAC,CAAC;MACpC,IAAIyD,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAID,QAAQ,CAACI,IAAI,CAACjC,OAAO,EAAE;UACzBC,UAAU,CAAC,IAAI,CAAC;UAChBE,WAAW,CAAC0B,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM;UACL1B,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAAC0B,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAImE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCF,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IACpCjC,kBAAkB,CAAEkC,mBAAmB,KAAM;MAC3C,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM9C,WAAW,CAAC4D,MAAM,CAAC;MAC1C,IAAId,QAAQ,CAACC,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAACD,QAAQ,CAACxD,OAAO,CAAC;QACjCoC,qBAAqB,CAAC,KAAK,CAAC;QAC5BK,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAClB,MAAMjB,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwE,WAAW,GAAIL,UAAU,IAAK;IAClC7B,kBAAkB,CAAC6B,UAAU,CAAC;EAChC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMI,OAAO,GAAG;QACdP,UAAU,EAAE9B,eAAe;QAC3BsC,IAAI,EAAEL,MAAM,CAACK;MACf,CAAC;MACD,MAAMnB,QAAQ,GAAG,MAAM7C,QAAQ,CAAC+D,OAAO,CAAC;MACxC,IAAIlB,QAAQ,CAACC,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAACD,QAAQ,CAACxD,OAAO,CAAC;QACjCsC,kBAAkB,CAAC,IAAI,CAAC;QACxBG,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAClB,MAAMjB,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAIwC,eAAe,IAAI,CAACQ,SAAS,CAACR,eAAe,CAAC,EAAE;MAClDS,YAAY,CAAE8B,QAAQ,KAAM;QAC1B,GAAGA,QAAQ;QACX,CAACvC,eAAe,gBAAG1C,KAAK,CAACkF,SAAS,CAAC;MACrC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACxC,eAAe,EAAEQ,SAAS,CAAC,CAAC;EAEhChD,SAAS,CAAC,MAAM;IACd,IAAIwC,eAAe,IAAIQ,SAAS,CAACR,eAAe,CAAC,EAAE;MACjDQ,SAAS,CAACR,eAAe,CAAC,CAACyC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAAC3C,eAAe,EAAEQ,SAAS,CAAC,CAAC;EAEhC,MAAMoC,UAAU,GAAIC,QAAQ,IAAK;IAC/B1C,eAAe,CAAC0C,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOD,QAAQ,IAAK;IACvC,IAAI;MACF,MAAME,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClC,gDACF,CAAC;MACD,IAAI,CAACF,aAAa,EAAE;QAClB;MACF;MACA,MAAM5B,QAAQ,GAAG,MAAM3C,cAAc,CAACqE,QAAQ,CAACK,GAAG,CAAC;MACnD,IAAI/B,QAAQ,CAACC,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAACD,QAAQ,CAACxD,OAAO,CAAC;QACjC,MAAMsD,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwF,oBAAoB,GAAG,MAAOlB,MAAM,IAAK;IAC7C,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM1C,cAAc,CAACwD,MAAM,EAAE/B,YAAY,CAACgD,GAAG,CAAC;MAC/D,IAAI/B,QAAQ,CAACC,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAACD,QAAQ,CAACxD,OAAO,CAAC;QACjCwC,eAAe,CAAC,IAAI,CAAC;QACrB,MAAMc,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyF,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjD,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5BtD,qBAAqB,CAAC,KAAK,CAAC;IAC5BK,IAAI,CAAC8B,WAAW,CAAC,CAAC;EACpB,CAAC;EAED1E,SAAS,CAAC,MAAM;IACd,IAAI0C,YAAY,EAAE;MAChBI,KAAK,CAACgD,cAAc,CAAC;QACnBC,KAAK,EAAErD,YAAY,CAACqD,KAAK;QACzBC,IAAI,EAAEtD,YAAY,CAACsD;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlD,KAAK,CAAC4B,WAAW,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAMuD,kBAAkB,GAAG,MAAAA,CAAO3B,UAAU,EAAE4B,OAAO,EAAEC,MAAM,KAAK;IAChE,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMzC,iBAAiB,CAAC;QAAEgF,OAAO;QAAEC;MAAO,CAAC,EAAE7B,UAAU,CAAC;MACzE,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAACD,QAAQ,CAACxD,OAAO,CAAC;QACjC,MAAMsD,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLtD,OAAO,CAAC6D,KAAK,CAACL,QAAQ,CAACxD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAACA,KAAK,CAAC7D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEsB,OAAA;IAAK2E,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjF5E,OAAA;MAAK2E,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAEhE5E,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5E,OAAA;UAAK2E,SAAS,EAAC,2HAA2H;UAAAC,QAAA,eACxI5E,OAAA;YAAG2E,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNhF,OAAA;UAAI2E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAAC,YAC1C,eAAA5E,OAAA;YAAM2E,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC,eACLhF,OAAA;UAAG2E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJhF,OAAA;UACEiF,OAAO,EAAEA,CAAA,KAAMnE,qBAAqB,CAAC,IAAI,CAAE;UAC3C6D,SAAS,EAAC,qMAAqM;UAAAC,QAAA,gBAE/M5E,OAAA;YAAG2E,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnE,kBAAkB,iBACjBb,OAAA;QAAK2E,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E5E,OAAA;UAAK2E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC5E,OAAA;YAAK2E,SAAS,EAAC,yGAAyG;YAAAC,QAAA,eACtH5E,OAAA;cAAG2E,SAAS,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNhF,OAAA;YAAI2E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENhF,OAAA,CAACnB,IAAI;UAACsC,IAAI,EAAEA,IAAK;UAAC+D,QAAQ,EAAEnC,iBAAkB;UAACoC,MAAM,EAAC,UAAU;UAACR,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtF5E,OAAA,CAACnB,IAAI,CAACuG,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9G,OAAO,EAAE;YAAmC,CAAC,CAAE;YAAAkG,QAAA,eAEzE5E,OAAA,CAACpB,KAAK;cACJ6G,WAAW,EAAC,8BAA8B;cAC1Cd,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZhF,OAAA,CAACnB,IAAI,CAACuG,IAAI;YACRC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,kBAAkB;YACxBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9G,OAAO,EAAE;YAAkD,CAAC,CAAE;YAAAkG,QAAA,eAExF5E,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC,wGAAwG;cACpHd,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZhF,OAAA,CAACnB,IAAI,CAACuG,IAAI;YAACT,SAAS,EAAC,MAAM;YAAAC,QAAA,eACzB5E,OAAA;cAAK2E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C5E,OAAA,CAACrB,MAAM;gBACLiH,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBlB,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,gBAEjH5E,OAAA;kBAAG2E,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,iBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA,CAACrB,MAAM;gBACLsG,OAAO,EAAEb,eAAgB;gBACzBO,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,EAC1F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGAvE,SAAS,CAACqF,MAAM,KAAK,CAAC,iBACrB9F,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5E,OAAA;UAAK2E,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9F5E,OAAA;YAAG2E,SAAS,EAAC;UAAsD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNhF,OAAA;UAAG2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,eAGDhF,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBnE,SAAS,CAACsF,GAAG,CAAEnC,QAAQ,iBACtB5D,OAAA;UAAwB2E,SAAS,EAAC,mHAAmH;UAAAC,QAAA,gBAEnJ5E,OAAA;YAAK2E,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C5E,OAAA;cAAK2E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C5E,OAAA;gBAAK2E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5E,OAAA;kBAAK2E,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvB5E,OAAA,CAAClB,MAAM;oBACLkH,GAAG,EAAEpC,QAAQ,CAACqC,IAAI,CAACC,YAAY,IAAIxG,KAAM;oBACzCyG,GAAG,EAAC,SAAS;oBACbC,IAAI,EAAE,EAAG;oBACTzB,SAAS,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACFhF,OAAA;oBAAK2E,SAAS,EAAC;kBAAqF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxG,CAAC,eACNhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEhB,QAAQ,CAACqC,IAAI,CAACZ;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEhF,OAAA;oBAAG2E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC,IAAIyB,IAAI,CAACzC,QAAQ,CAAC0C,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;sBACxDC,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE,MAAM;sBACbC,GAAG,EAAE,SAAS;sBACdC,IAAI,EAAE,SAAS;sBACfC,MAAM,EAAE;oBACV,CAAC;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhF,OAAA;gBAAK2E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC,CAACrE,QAAQ,CAAC0D,GAAG,KAAKL,QAAQ,CAACqC,IAAI,CAAChC,GAAG,IAAI1D,QAAQ,CAACF,OAAO,kBACtDL,OAAA,CAAAE,SAAA;kBAAA0E,QAAA,gBACE5E,OAAA;oBACEiF,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAACC,QAAQ,CAAE;oBACpCe,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,eAEhH5E,OAAA,CAACL,WAAW;sBAACgF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACThF,OAAA;oBACEiF,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACD,QAAQ,CAAE;oBACtCe,SAAS,EAAC,oGAAoG;oBAAAC,QAAA,eAE9G5E,OAAA,CAACJ,QAAQ;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAK2E,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5E,OAAA;cAAI2E,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAEhB,QAAQ,CAACU;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1EhF,OAAA;cAAG2E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEhB,QAAQ,CAACW;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGrEhF,OAAA;cAAK2E,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9E5E,OAAA;gBAAK2E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5E,OAAA;kBACEiF,OAAO,EAAEA,CAAA,KAAMrC,aAAa,CAACgB,QAAQ,CAACK,GAAG,CAAE;kBAC3CU,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,gBAEpI5E,OAAA;oBAAG2E,SAAS,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnCrE,eAAe,CAACiD,QAAQ,CAACK,GAAG,CAAC,GAAG,cAAc,GAAG,cAAc;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACThF,OAAA;kBACEiF,OAAO,EAAEA,CAAA,KAAM/B,WAAW,CAACU,QAAQ,CAACK,GAAG,CAAE;kBACzCU,SAAS,EAAC,4HAA4H;kBAAAC,QAAA,gBAEtI5E,OAAA;oBAAG2E,SAAS,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,SAExC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChE5E,OAAA,CAACH,SAAS;kBAAC8E,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDhF,OAAA;kBAAM2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEhB,QAAQ,CAACiD,OAAO,CAACf;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL/D,YAAY,IAAIA,YAAY,CAACgD,GAAG,KAAKL,QAAQ,CAACK,GAAG,iBAClDjE,OAAA,CAACnB,IAAI;YACHsC,IAAI,EAAEE,KAAM;YACZ6D,QAAQ,EAAEhB,oBAAqB;YAC/BiB,MAAM,EAAC,UAAU;YACjB2B,aAAa,EAAE;cACbxC,KAAK,EAAErD,YAAY,CAACqD,KAAK;cACzBC,IAAI,EAAEtD,YAAY,CAACsD;YACrB,CAAE;YAAAK,QAAA,gBAEF5E,OAAA,CAACnB,IAAI,CAACuG,IAAI;cACRC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9G,OAAO,EAAE;cAAyB,CAAC,CACrD;cAAAkG,QAAA,eAEF5E,OAAA,CAACpB,KAAK;gBAACmI,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAY;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACZhF,OAAA,CAACnB,IAAI,CAACuG,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,MAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9G,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAAkG,QAAA,eAE9D5E,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACZhF,OAAA,CAACnB,IAAI,CAACuG,IAAI;cAAAR,QAAA,gBACR5E,OAAA,CAACrB,MAAM;gBAACiH,IAAI,EAAC,SAAS;gBAACC,QAAQ,EAAC,QAAQ;gBAAAjB,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA,CAACrB,MAAM;gBACLsG,OAAO,EAAEd,kBAAmB;gBAC5B4C,KAAK,EAAE;kBAAEE,UAAU,EAAE;gBAAG,CAAE;gBAAArC,QAAA,EAC3B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP,EACArE,eAAe,CAACiD,QAAQ,CAACK,GAAG,CAAC,iBAC5BjE,OAAA;YAAK2E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvD5E,OAAA;cAAI2E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE5E,OAAA;gBAAG2E,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,aAC5C,EAACpB,QAAQ,CAACiD,OAAO,CAACf,MAAM,EAAC,GACpC;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJpB,QAAQ,CAACiD,OAAO,CAACd,GAAG,CAAC,CAACmB,KAAK,EAAEC,KAAK,kBACjCnH,OAAA;cAEE2E,SAAS,EAAG,gDACVuC,KAAK,CAACjB,IAAI,CAAC5F,OAAO,GACd,gCAAgC,GAChC6G,KAAK,CAACE,UAAU,GAChB,8BAA8B,GAC9B,iBACL,EAAE;cAAAxC,QAAA,eAEH5E,OAAA;gBAAK2E,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBAEzC5E,OAAA;kBAAK2E,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5B5E,OAAA,CAAClB,MAAM;oBACLkH,GAAG,EAAEkB,KAAK,CAACjB,IAAI,CAACC,YAAY,IAAIxG,KAAM;oBACtCyG,GAAG,EAAC,SAAS;oBACbC,IAAI,EAAE,EAAG;oBACTzB,SAAS,EAAC;kBAA6B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNhF,OAAA;kBAAK2E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAE7B5E,OAAA;oBAAK2E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD5E,OAAA;sBAAK2E,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1C5E,OAAA;wBAAI2E,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEsC,KAAK,CAACjB,IAAI,CAACZ;sBAAI;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACjEkC,KAAK,CAACjB,IAAI,CAAC5F,OAAO,iBACjBL,OAAA;wBAAM2E,SAAS,EAAC,0EAA0E;wBAAAC,QAAA,EAAC;sBAE3F;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP,EACAkC,KAAK,CAACE,UAAU,IAAI,CAACF,KAAK,CAACjB,IAAI,CAAC5F,OAAO,iBACtCL,OAAA;wBAAK2E,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1C5E,OAAA,CAACF,OAAO;0BAAC6E,SAAS,EAAC;wBAAwB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9ChF,OAAA;0BAAM2E,SAAS,EAAC,wEAAwE;0BAAAC,QAAA,EAAC;wBAEzF;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNhF,OAAA;sBAAM2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC,IAAIyB,IAAI,CAACa,KAAK,CAACZ,SAAS,CAAC,CAACe,cAAc,CAACC,SAAS,EAAE;wBACnDV,MAAM,EAAE,SAAS;wBACjBD,IAAI,EAAE,SAAS;wBACfD,GAAG,EAAE,SAAS;wBACdD,KAAK,EAAE,OAAO;wBACdD,IAAI,EAAE;sBACR,CAAC;oBAAC;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAGNhF,OAAA;oBAAK2E,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAChDsC,KAAK,CAAC7D;kBAAI;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EAGL3E,OAAO,IAAI,CAAC6G,KAAK,CAACjB,IAAI,CAAC5F,OAAO,iBAC7BL,OAAA;oBAAK2E,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/B5E,OAAA;sBACEiF,OAAO,EAAEA,CAAA,KACPT,kBAAkB,CAChBZ,QAAQ,CAACK,GAAG,EACZiD,KAAK,CAACjD,GAAG,EACT,CAACiD,KAAK,CAACE,UACT,CACD;sBACDzC,SAAS,EAAG,2EACVuC,KAAK,CAACE,UAAU,GACZ,0CAA0C,GAC1C,gDACL,EAAE;sBAAAxC,QAAA,EAEFsC,KAAK,CAACE,UAAU,GAAG,YAAY,GAAG;oBAAS;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA9EDkC,KAAK,CAACjD,GAAG;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+EX,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eACDhF,OAAA;YAAKuH,GAAG,EAAEhG,SAAS,CAACqC,QAAQ,CAACK,GAAG,CAAE;YAAAW,QAAA,EAC/B7D,eAAe,KAAK6C,QAAQ,CAACK,GAAG,iBAC/BjE,OAAA,CAACnB,IAAI;cACHsC,IAAI,EAAEA,IAAK;cACX+D,QAAQ,EAAE/B,iBAAkB;cAC5BgC,MAAM,EAAC,UAAU;cAAAP,QAAA,gBAEjB5E,OAAA,CAACnB,IAAI,CAACuG,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,YAAY;gBAClBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9G,OAAO,EAAE;gBAA0B,CAAC,CACtD;gBAAAkG,QAAA,eAEF5E,OAAA,CAACpB,KAAK,CAAC8G,QAAQ;kBAACC,IAAI,EAAE;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACZhF,OAAA,CAACnB,IAAI,CAACuG,IAAI;gBAAAR,QAAA,gBACR5E,OAAA,CAACrB,MAAM;kBAACiH,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAAjB,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThF,OAAA,CAACrB,MAAM;kBACLsG,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;kBACxC+F,KAAK,EAAE;oBAAEE,UAAU,EAAE;kBAAG,CAAE;kBAAArC,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAjPIpB,QAAQ,CAACK,GAAG;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkPjB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC,CAAC,eAENhF,OAAA,CAACjB,UAAU;QACTyE,OAAO,EAAE/B,WAAY;QACrB+F,KAAK,EAAE1F,cAAe;QACtB2F,QAAQ,EAAE5F,KAAM;QAChB6F,QAAQ,EAAElF,gBAAiB;QAC3BuE,KAAK,EAAE;UAAEY,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAClDC,eAAe,EAAE;MAAM;QAAAhD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA9iBID,KAAK;EAAA,QAQMtB,IAAI,CAACuC,OAAO,EACXvC,IAAI,CAACuC,OAAO,EACXnC,WAAW;AAAA;AAAA6I,EAAA,GAVxB3H,KAAK;AAgjBX,eAAeA,KAAK;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}