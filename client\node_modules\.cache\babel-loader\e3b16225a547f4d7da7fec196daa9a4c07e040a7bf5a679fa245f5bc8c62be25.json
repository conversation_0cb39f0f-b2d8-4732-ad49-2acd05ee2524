{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    const result = user && String(userId) === String(user._id);\n    if (showFindMe && result) {\n      console.log('🎯 isCurrentUser match found!', {\n        userId,\n        currentUserId: user._id,\n        result\n      });\n    }\n    return result;\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user._id)) return;\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite !important;\n          border: 4px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.4)) !important;\n          box-shadow: 0 0 30px rgba(255, 215, 0, 0.8) !important;\n          transform: scale(1.02) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 825,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1008,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1007,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 17\n                }, this), userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1080,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 19\n                }, this), showFindMe && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.8,\n                    y: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: [1, 1.05, 1],\n                    y: 0,\n                    boxShadow: ['0 0 20px rgba(255, 215, 0, 0.5)', '0 0 40px rgba(255, 215, 0, 0.8)', '0 0 20px rgba(255, 215, 0, 0.5)']\n                  },\n                  transition: {\n                    duration: 0.5,\n                    scale: {\n                      duration: 1,\n                      repeat: Infinity\n                    },\n                    boxShadow: {\n                      duration: 1.5,\n                      repeat: Infinity\n                    }\n                  },\n                  className: \"fixed top-4 right-4 z-50 bg-gradient-to-r from-yellow-400 to-orange-400 text-black px-6 py-3 rounded-xl font-bold shadow-2xl border-2 border-yellow-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                        className: \"w-5 h-5 animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1111,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83C\\uDFAF LOCATING YOU!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1112,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs\",\n                      children: user !== null && user !== void 0 && user._id ? `User: ${user.name || user._id}` : 'Testing Mode - Watch first card!'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1114,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    console.log('🎯 Find Me button clicked!');\n                    console.log('🎯 User object:', user);\n                    setShowFindMe(true);\n                    console.log('🎯 showFindMe state set to true');\n\n                    // Debug user information\n                    console.log('🎯 Current user ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('🎯 Top performers:', topPerformers.map(p => ({\n                      id: p._id,\n                      name: p.name\n                    })));\n                    console.log('🎯 League groups:', Object.keys(leagueGroups));\n                    console.log('🎯 Ranking data length:', rankingData.length);\n\n                    // If no user, just show the effect for testing - DON'T RETURN EARLY!\n                    if (!(user !== null && user !== void 0 && user._id)) {\n                      console.log('🎯 No user found - showing test effect on first card');\n                      // Still set the timeout to hide the effect, but don't return\n                      setTimeout(() => {\n                        console.log('🎯 Hiding Find Me effect (test mode)');\n                        setShowFindMe(false);\n                      }, 5000);\n                      // Don't return - let the highlighting logic run\n                    } else {\n                      // Check if user is in podium\n                      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n                      console.log('🎯 User in podium:', isInPodium);\n\n                      // Debug league information\n                      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                        var _leagueData$users2;\n                        const userInLeague = (_leagueData$users2 = leagueData.users) === null || _leagueData$users2 === void 0 ? void 0 : _leagueData$users2.find(u => String(u._id) === String(user._id));\n                        if (userInLeague) {\n                          console.log(`🎯 Found user in league: ${leagueKey}`, {\n                            leagueTitle: leagueData.title,\n                            userPosition: leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1,\n                            totalInLeague: leagueData.users.length\n                          });\n                        }\n                      }\n                      if (isInPodium) {\n                        // Scroll to podium section\n                        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n                        if (podiumSection) {\n                          setTimeout(() => {\n                            podiumSection.scrollIntoView({\n                              behavior: 'smooth',\n                              block: 'center',\n                              inline: 'nearest'\n                            });\n                          }, 100);\n                        }\n                      } else {\n                        // Find user's league and scroll to it\n                        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                          var _leagueData$users3;\n                          const userInLeague = (_leagueData$users3 = leagueData.users) === null || _leagueData$users3 === void 0 ? void 0 : _leagueData$users3.find(u => String(u._id) === String(user._id));\n                          if (userInLeague) {\n                            // First select the league\n                            setSelectedLeague(leagueKey);\n\n                            // Then scroll to the league section\n                            setTimeout(() => {\n                              const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`);\n                              if (leagueElement) {\n                                leagueElement.scrollIntoView({\n                                  behavior: 'smooth',\n                                  block: 'center',\n                                  inline: 'nearest'\n                                });\n\n                                // Add visual effect\n                                leagueElement.style.transform = 'scale(1.02)';\n                                leagueElement.style.transition = 'all 0.3s ease';\n                                leagueElement.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.8)';\n                                setTimeout(() => {\n                                  leagueElement.style.transform = 'scale(1)';\n                                  leagueElement.style.boxShadow = '';\n                                }, 1000);\n                              }\n\n                              // Then scroll to user's position within the league\n                              setTimeout(() => {\n                                const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n                                if (userElement) {\n                                  userElement.scrollIntoView({\n                                    behavior: 'smooth',\n                                    block: 'center',\n                                    inline: 'nearest'\n                                  });\n                                }\n                              }, 1000);\n                            }, 200);\n                            break;\n                          }\n                        }\n                      }\n\n                      // Hide the find me effect after 5 seconds (increased for better visibility)\n                      setTimeout(() => {\n                        console.log('🎯 Hiding Find Me effect');\n                        setShowFindMe(false);\n                      }, 5000);\n                    } // Close the else block\n                  },\n\n                  className: \"flex items-center gap-2 md:gap-3 px-6 md:px-8 py-4 md:py-5 bg-gradient-to-r from-yellow-400 to-orange-500 text-black rounded-xl font-black shadow-2xl hover:shadow-3xl transition-all duration-300 border-2 border-yellow-300 animate-pulse\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1.1rem' : '1.3rem',\n                    background: 'linear-gradient(135deg, #FDE047, #F97316)',\n                    boxShadow: '0 8px 25px rgba(245, 158, 11, 0.6), 0 0 20px rgba(255, 215, 0, 0.4)',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Find Me\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1122,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1252,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1315,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1342,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1317,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1358,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1280,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1375,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1371,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1275,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1392,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1250,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1409,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1398,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1428,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1427,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1430,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1426,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1451,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1452,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1472,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1491,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1492,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1471,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1459,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1540,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1534,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1602,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1603,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1607,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1618,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1590,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1550,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1450,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1443,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1648,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1638,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1670,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${isCurrentUser(topPerformers[1]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[1]._id) && showFindMe ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))' : isCurrentUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1727,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1726,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1741,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1744,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1766,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1772,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1756,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1755,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1787,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1794,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1799,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1802,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1798,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1738,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1731,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${isCurrentUser(topPerformers[0]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '320px',\n                    transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[0]._id) && showFindMe ? 'brightness(1.8) saturate(1.6) drop-shadow(0 0 60px rgba(255, 215, 0, 1)) hue-rotate(15deg)' : isCurrentUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1858,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1857,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1867,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1862,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1884,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1887,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1910,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1916,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1900,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1936,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1929,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1899,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1943,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1954,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1942,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1983,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1969,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1988,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1999,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2000,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1998,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2004,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1997,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2008,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2009,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2007,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2013,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2006,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1996,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1987,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1878,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1871,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1813,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${isCurrentUser(topPerformers[2]._id) && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100' : isCurrentUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: '280px',\n                    transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: isCurrentUser(topPerformers[2]._id) && showFindMe ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))' : isCurrentUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2067,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2066,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2081,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2084,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2106,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2112,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2096,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2095,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2127,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2134,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2139,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2142,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2138,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2078,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2071,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2024,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1681,\n                columnNumber: 19\n              }, this), currentUserLeague && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.3,\n                  duration: 0.8\n                },\n                className: \"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-white mb-2\",\n                    children: [\"Your League: \", currentUserLeague.league.leagueIcon, \" \", currentUserLeague.league.title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2161,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/80 text-sm\",\n                    children: [\"Rank #\", currentUserLeague.userRank, \" of \", currentUserLeague.totalInLeague, \" in your league\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2164,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2160,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold\",\n                      children: currentUserLeague.league.promotionXP > 0 ? `${currentUserLeague.league.promotionXP - ((user === null || user === void 0 ? void 0 : user.totalXP) || 0)} XP` : 'Max League'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2171,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"To Promotion\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2177,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2170,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold\",\n                      children: currentUserLeague.totalInLeague\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2180,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2181,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2179,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold\",\n                      children: [\"#\", currentUserLeague.userRank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2184,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Rank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2185,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2183,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2169,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2154,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1664,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2224,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${\n                      // Test: highlight first card when showFindMe is active\n                      index === 0 && showFindMe || isCurrentUser && showFindMe ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100 bg-red-500' : isCurrentUser ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: index === 0 && showFindMe || isCurrentUser && showFindMe ? 'scale(1.2) translateY(-10px) rotate(2deg)' : isCurrentUser ? 'scale(1.05)' : 'scale(1)',\n                        filter: index === 0 && showFindMe || isCurrentUser && showFindMe ? 'brightness(2) saturate(2) drop-shadow(0 0 100px rgba(255, 0, 0, 1)) contrast(1.5)' : isCurrentUser ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: index === 0 && showFindMe || isCurrentUser && showFindMe ? '10px solid #FF0000' : isCurrentUser ? '4px solid #FFD700' : 'none',\n                        borderRadius: isCurrentUser || index === 0 && showFindMe ? '20px' : '0px',\n                        zIndex: index === 0 && showFindMe || isCurrentUser && showFindMe ? 1000 : 'auto',\n                        backgroundColor: index === 0 && showFindMe || isCurrentUser && showFindMe ? '#FF0000' : 'transparent',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2298,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2304,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2303,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full w-full h-full\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2329,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2335,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2319,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2356,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2349,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2318,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2301,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2367,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2378,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2366,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2396,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2364,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2363,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2405,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2425,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2426,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2418,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2435,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2436,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2428,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2417,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2403,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2291,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2285,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2242,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2236,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2199,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2461,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2475,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2460,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2501,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2512,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2522,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2511,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2500,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2527,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2499,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${isCurrentUser && showFindMe ? 'find-me-highlight ring-4 ring-yellow-400/80' : isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2567,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2570,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser && showFindMe ? 'ring-2 ring-yellow-400 ring-opacity-100' : isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                                  style: {\n                                    background: '#f0f0f0',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    width: '40px',\n                                    height: '40px'\n                                  },\n                                  children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: champion.profilePicture,\n                                    alt: champion.name,\n                                    className: \"object-cover rounded-full w-full h-full\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2599,\n                                    columnNumber: 47\n                                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                    style: {\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    },\n                                    children: champion.name.charAt(0).toUpperCase()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2605,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2589,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2625,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2618,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2582,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2637,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2631,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2641,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2646,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2649,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2645,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2564,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2558,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2544,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2538,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2663,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2662,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2488,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2481,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2452,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2695,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2698,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2694,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2701,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2704,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2700,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2707,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2710,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2706,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2693,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2713,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2687,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2681,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2734,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2739,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2728,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2722,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2762,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2758,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2764,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2769,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2777,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2757,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2751,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2795,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2796,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2801,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2790,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1660,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1654,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1005,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"7ka3d9M68krY94Rgboqa09JRbY0=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "console", "log", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "userData", "totalQuizzesTaken", "transformedData", "map", "index", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "userInLeague", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "position", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "result", "currentUserId", "scrollToUser", "podiumSection", "userInCurrentView", "userElement", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "i", "y", "x", "delay", "left", "top", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "p", "_leagueData$users2", "leagueTitle", "userPosition", "_leagueData$users3", "textShadow", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "_leagueGroups$leagueK2", "isSelected", "userCount", "border", "zIndex", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "height", "borderRadius", "width", "src", "alt", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    const result = user && String(userId) === String(user._id);\n    if (showFindMe && result) {\n      console.log('🎯 isCurrentUser match found!', { userId, currentUserId: user._id, result });\n    }\n    return result;\n  };\n\n  // Auto-scroll to user position when page loads or league changes\n  useEffect(() => {\n    if (!user?._id) return;\n\n    const scrollToUser = () => {\n      // Check if user is in podium\n      const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n\n      if (isInPodium) {\n        // Scroll to podium section\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          }, 1000);\n        }\n      } else if (leagueUsers.length > 0) {\n        // Check if user is in current league view\n        const userInCurrentView = leagueUsers.some(u => String(u._id) === String(user._id));\n        if (userInCurrentView) {\n          // Scroll to user's position in league\n          const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n          if (userElement) {\n            setTimeout(() => {\n              userElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n            }, 1500);\n          }\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, topPerformers, leagueUsers]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 2s ease-in-out infinite !important;\n          border: 4px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.4)) !important;\n          box-shadow: 0 0 30px rgba(255, 215, 0, 0.8) !important;\n          transform: scale(1.02) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\">\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n                {/* Find Me Active Notification */}\n                {showFindMe && (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.8, y: -20 }}\n                    animate={{\n                      opacity: 1,\n                      scale: [1, 1.05, 1],\n                      y: 0,\n                      boxShadow: [\n                        '0 0 20px rgba(255, 215, 0, 0.5)',\n                        '0 0 40px rgba(255, 215, 0, 0.8)',\n                        '0 0 20px rgba(255, 215, 0, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 0.5,\n                      scale: { duration: 1, repeat: Infinity },\n                      boxShadow: { duration: 1.5, repeat: Infinity }\n                    }}\n                    className=\"fixed top-4 right-4 z-50 bg-gradient-to-r from-yellow-400 to-orange-400 text-black px-6 py-3 rounded-xl font-bold shadow-2xl border-2 border-yellow-300\"\n                  >\n                    <div className=\"flex flex-col items-center gap-1\">\n                      <div className=\"flex items-center gap-2\">\n                        <TbTarget className=\"w-5 h-5 animate-pulse\" />\n                        <span>🎯 LOCATING YOU!</span>\n                      </div>\n                      <div className=\"text-xs\">\n                        {user?._id ? `User: ${user.name || user._id}` : 'Testing Mode - Watch first card!'}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Find Me Button - You Feature - ALWAYS VISIBLE FOR TESTING */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => {\n                    console.log('🎯 Find Me button clicked!');\n                    console.log('🎯 User object:', user);\n                    setShowFindMe(true);\n                    console.log('🎯 showFindMe state set to true');\n\n                    // Debug user information\n                    console.log('🎯 Current user ID:', user?._id);\n                    console.log('🎯 Top performers:', topPerformers.map(p => ({ id: p._id, name: p.name })));\n                    console.log('🎯 League groups:', Object.keys(leagueGroups));\n                    console.log('🎯 Ranking data length:', rankingData.length);\n\n                    // If no user, just show the effect for testing - DON'T RETURN EARLY!\n                    if (!user?._id) {\n                      console.log('🎯 No user found - showing test effect on first card');\n                      // Still set the timeout to hide the effect, but don't return\n                      setTimeout(() => {\n                        console.log('🎯 Hiding Find Me effect (test mode)');\n                        setShowFindMe(false);\n                      }, 5000);\n                      // Don't return - let the highlighting logic run\n                    } else {\n\n                    // Check if user is in podium\n                    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n                    console.log('🎯 User in podium:', isInPodium);\n\n                    // Debug league information\n                    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n                      if (userInLeague) {\n                        console.log(`🎯 Found user in league: ${leagueKey}`, {\n                          leagueTitle: leagueData.title,\n                          userPosition: leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1,\n                          totalInLeague: leagueData.users.length\n                        });\n                      }\n                    }\n\n                      if (isInPodium) {\n                        // Scroll to podium section\n                        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n                        if (podiumSection) {\n                          setTimeout(() => {\n                            podiumSection.scrollIntoView({\n                              behavior: 'smooth',\n                              block: 'center',\n                              inline: 'nearest'\n                            });\n                          }, 100);\n                        }\n                      } else {\n                        // Find user's league and scroll to it\n                        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                          const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n                          if (userInLeague) {\n                            // First select the league\n                            setSelectedLeague(leagueKey);\n\n                            // Then scroll to the league section\n                            setTimeout(() => {\n                              const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                                                   document.getElementById(`league-${leagueKey}`);\n\n                              if (leagueElement) {\n                                leagueElement.scrollIntoView({\n                                  behavior: 'smooth',\n                                  block: 'center',\n                                  inline: 'nearest'\n                                });\n\n                                // Add visual effect\n                                leagueElement.style.transform = 'scale(1.02)';\n                                leagueElement.style.transition = 'all 0.3s ease';\n                                leagueElement.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.8)';\n\n                                setTimeout(() => {\n                                  leagueElement.style.transform = 'scale(1)';\n                                  leagueElement.style.boxShadow = '';\n                                }, 1000);\n                              }\n\n                              // Then scroll to user's position within the league\n                              setTimeout(() => {\n                                const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n                                if (userElement) {\n                                  userElement.scrollIntoView({\n                                    behavior: 'smooth',\n                                    block: 'center',\n                                    inline: 'nearest'\n                                  });\n                                }\n                              }, 1000);\n                            }, 200);\n                            break;\n                          }\n                        }\n                      }\n\n                      // Hide the find me effect after 5 seconds (increased for better visibility)\n                      setTimeout(() => {\n                        console.log('🎯 Hiding Find Me effect');\n                        setShowFindMe(false);\n                      }, 5000);\n                    } // Close the else block\n                    }}\n                    className=\"flex items-center gap-2 md:gap-3 px-6 md:px-8 py-4 md:py-5 bg-gradient-to-r from-yellow-400 to-orange-500 text-black rounded-xl font-black shadow-2xl hover:shadow-3xl transition-all duration-300 border-2 border-yellow-300 animate-pulse\"\n                    style={{\n                      fontSize: window.innerWidth < 768 ? '1.1rem' : '1.3rem',\n                      background: 'linear-gradient(135deg, #FDE047, #F97316)',\n                      boxShadow: '0 8px 25px rgba(245, 158, 11, 0.6), 0 0 20px rgba(255, 215, 0, 0.4)',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\n                    }}\n                  >\n                    <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>Find Me</span>\n                  </motion.button>\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          isCurrentUser(topPerformers[1]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[1]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[1]._id) && showFindMe\n                            ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))'\n                            : isCurrentUser(topPerformers[1]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          isCurrentUser(topPerformers[0]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[0]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '320px',\n                          transform: isCurrentUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[0]._id) && showFindMe\n                            ? 'brightness(1.8) saturate(1.6) drop-shadow(0 0 60px rgba(255, 215, 0, 1)) hue-rotate(15deg)'\n                            : isCurrentUser(topPerformers[0]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          isCurrentUser(topPerformers[2]._id) && showFindMe\n                            ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100'\n                            : isCurrentUser(topPerformers[2]._id)\n                              ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                              : ''\n                        }`}\n                        style={{\n                          height: '280px',\n                          transform: isCurrentUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: isCurrentUser(topPerformers[2]._id) && showFindMe\n                            ? 'brightness(1.5) saturate(1.4) drop-shadow(0 0 40px rgba(255, 215, 0, 1))'\n                            : isCurrentUser(topPerformers[2]._id)\n                              ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                              : 'none',\n                          transition: 'all 0.3s ease',\n                          border: isCurrentUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n                  {/* League Information Section */}\n                  {currentUserLeague && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 1.3, duration: 0.8 }}\n                      className=\"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\"\n                    >\n                      <div className=\"text-center mb-4\">\n                        <h3 className=\"text-xl font-bold text-white mb-2\">\n                          Your League: {currentUserLeague.league.leagueIcon} {currentUserLeague.league.title}\n                        </h3>\n                        <p className=\"text-white/80 text-sm\">\n                          Rank #{currentUserLeague.userRank} of {currentUserLeague.totalInLeague} in your league\n                        </p>\n                      </div>\n\n                      <div className=\"flex justify-center gap-4 text-sm\">\n                        <div className=\"bg-green-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-green-400 font-bold\">\n                            {currentUserLeague.league.promotionXP > 0 ?\n                              `${currentUserLeague.league.promotionXP - (user?.totalXP || 0)} XP` :\n                              'Max League'\n                            }\n                          </div>\n                          <div className=\"text-white/80 text-xs\">To Promotion</div>\n                        </div>\n                        <div className=\"bg-blue-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-blue-400 font-bold\">{currentUserLeague.totalInLeague}</div>\n                          <div className=\"text-white/80 text-xs\">League Members</div>\n                        </div>\n                        <div className=\"bg-purple-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-purple-400 font-bold\">#{currentUserLeague.userRank}</div>\n                          <div className=\"text-white/80 text-xs\">League Rank</div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                // Test: highlight first card when showFindMe is active\n                                (index === 0 && showFindMe) || (isCurrentUser && showFindMe)\n                                  ? 'find-me-highlight ring-8 ring-yellow-400 ring-opacity-100 bg-red-500'\n                                  : isCurrentUser\n                                    ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                    : ''\n                              }`}\n                              style={{\n                                transform: ((index === 0 && showFindMe) || (isCurrentUser && showFindMe))\n                                  ? 'scale(1.2) translateY(-10px) rotate(2deg)'\n                                  : isCurrentUser\n                                    ? 'scale(1.05)'\n                                    : 'scale(1)',\n                                filter: ((index === 0 && showFindMe) || (isCurrentUser && showFindMe))\n                                  ? 'brightness(2) saturate(2) drop-shadow(0 0 100px rgba(255, 0, 0, 1)) contrast(1.5)'\n                                  : isCurrentUser\n                                    ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                    : 'none',\n                                transition: 'all 0.3s ease',\n                                border: ((index === 0 && showFindMe) || (isCurrentUser && showFindMe))\n                                  ? '10px solid #FF0000'\n                                  : isCurrentUser\n                                    ? '4px solid #FFD700'\n                                    : 'none',\n                                borderRadius: (isCurrentUser || (index === 0 && showFindMe)) ? '20px' : '0px',\n                                zIndex: ((index === 0 && showFindMe) || (isCurrentUser && showFindMe)) ? 1000 : 'auto',\n                                backgroundColor: ((index === 0 && showFindMe) || (isCurrentUser && showFindMe)) ? '#FF0000' : 'transparent',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                        style={{\n                                          background: '#f0f0f0',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      >\n                                        {champion.profilePicture ? (\n                                          <img\n                                            src={champion.profilePicture}\n                                            alt={champion.name}\n                                            className=\"object-cover rounded-full w-full h-full\"\n                                          />\n                                        ) : (\n                                          <div\n                                            className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                            style={{\n                                              background: '#25D366',\n                                              color: '#FFFFFF',\n                                              fontSize: '12px'\n                                            }}\n                                          >\n                                            {champion.name.charAt(0).toUpperCase()}\n                                          </div>\n                                        )}\n                                      </div>\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      isCurrentUser && showFindMe\n                                        ? 'find-me-highlight ring-4 ring-yellow-400/80'\n                                        : isCurrentUser\n                                          ? 'ring-2 ring-yellow-400/60'\n                                          : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser && showFindMe\n                                            ? 'ring-2 ring-yellow-400 ring-opacity-100'\n                                            : isCurrentUser\n                                              ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                              : ''\n                                        }`}>\n                                          <div\n                                            className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                            style={{\n                                              background: '#f0f0f0',\n                                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          >\n                                            {champion.profilePicture ? (\n                                              <img\n                                                src={champion.profilePicture}\n                                                alt={champion.name}\n                                                className=\"object-cover rounded-full w-full h-full\"\n                                              />\n                                            ) : (\n                                              <div\n                                                className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                                style={{\n                                                  background: '#25D366',\n                                                  color: '#FFFFFF',\n                                                  fontSize: '16px'\n                                                }}\n                                              >\n                                                {champion.name.charAt(0).toUpperCase()}\n                                              </div>\n                                            )}\n                                          </div>\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMmE,UAAU,GAAGjE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMkE,SAAS,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMmE,cAAc,GAAGnE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMoE,aAAa,GAAGpE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMqE,WAAW,GAAGrE,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMsE,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzE,OAAO;MACb0E,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/D,SAAS;MACfgE,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzD,QAAQ;MACd0D,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE1D,OAAO;MACb2D,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE1E,QAAQ;MACd2E,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAElE,OAAO;MACbmE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExE,MAAM;MACZyE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEhE,QAAQ;MACdiE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIlE,KAAK,IAAK;IACpC,MAAMmE,OAAO,GAAG,CAAC,CAAC;IAElBnE,KAAK,CAACoE,OAAO,CAACnE,IAAI,IAAI;MACpB,MAAMoE,UAAU,GAAGT,aAAa,CAAC3D,IAAI,CAACqE,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClBrE,KAAK,EAAE;QACT,CAAC;MACH;MACAmE,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAAC9D,KAAK,CAACuE,IAAI,CAAC;QACpC,GAAGtE,IAAI;QACPuE,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAAC1E,KAAK,CAAC2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMjD,WAAW,GAAG0D,QAAQ,CAACE,MAAM,CAAChF,IAAI,IAAI;MAC1C,MAAM6D,MAAM,GAAGF,aAAa,CAAC3D,IAAI,CAACqE,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClBrE,KAAK,EAAEqB,WAAW;MAClB6D,QAAQ,EAAE7D,WAAW,CAAC8D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAEjE,WAAW,CAACkE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IAAA,IAAAe,qBAAA;IACxCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEjB,SAAS,CAAC;;IAE7C;IACAhD,iBAAiB,CAACgD,SAAS,CAAC;IAC5BlD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAAmE,qBAAA,GAAA9D,YAAY,CAAC+C,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyBzF,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACA4F,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBrB,SAAU,IAAG,CAAC,IACvDoB,QAAQ,CAACE,cAAc,CAAE,UAAStB,SAAU,EAAC,CAAC,IAC9C7C,UAAU,CAACoE,OAAO,CAACvB,SAAS,CAAC;MAElD,IAAImB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAAC1B,MAAM,CAACnB,MAAM,IAAInC,YAAY,CAACmC,MAAM,CAAC,IAAInC,YAAY,CAACmC,MAAM,CAAC,CAAC9D,KAAK,CAACuF,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAED;EACA,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFtG,UAAU,CAAC,IAAI,CAAC;MAChBoF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMkB,qBAAqB,GAAG,MAAMxH,gBAAgB,CAAC;UACnDyH,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAA9G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+G,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMyB,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAAClC,MAAM,CAACoC,QAAQ,IAC5DA,QAAQ,CAAC/C,OAAO,IAAI+C,QAAQ,CAAC/C,OAAO,GAAG,CAAC,IACxC+C,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACC,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,MAAM;YAC7DpC,GAAG,EAAEgC,QAAQ,CAAChC,GAAG;YACjBqC,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACS,YAAY,IAAI,EAAE;YAC3CxD,OAAO,EAAE+C,QAAQ,CAAC/C,OAAO,IAAI,CAAC;YAC9BgD,iBAAiB,EAAED,QAAQ,CAACC,iBAAiB,IAAI,CAAC;YAClDS,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACfjD,IAAI,EAAEZ,aAAa,CAACyD,QAAQ,CAAC/C,OAAO,IAAI,CAAC,CAAC;YAC1C8D,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEhB,QAAQ,CAACgB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElB,QAAQ,CAACkB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHvI,cAAc,CAACmH,eAAe,CAAC;;UAE/B;UACA,MAAMqB,aAAa,GAAGrB,eAAe,CAACpC,SAAS,CAAC0D,IAAI,IAAIA,IAAI,CAACxD,GAAG,MAAKpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,EAAC;UAC/E7E,kBAAkB,CAACoI,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAI3I,IAAI,EAAE;YACR,MAAM6I,cAAc,GAAGhE,wBAAwB,CAACyC,eAAe,EAAEtH,IAAI,CAAC;YACtEmB,oBAAoB,CAAC0H,cAAc,CAAC;YACpCxH,cAAc,CAAC,CAAAwH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE9I,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAM+I,OAAO,GAAG7E,kBAAkB,CAACqD,eAAe,CAAC;UACnD3F,eAAe,CAACmH,OAAO,CAAC;UAExBzI,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO0I,OAAO,EAAE;QAChBtD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEqD,OAAO,CAAC;MACpE;;MAEA;MACAtD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIsD,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFxD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDsD,eAAe,GAAG,MAAM7J,uBAAuB,CAAC,CAAC;QACjDsG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCuD,aAAa,GAAG,MAAM3J,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAO4J,KAAK,EAAE;QACdzD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwD,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAM3J,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAO6J,SAAS,EAAE;UAClB1D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyD,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAChC,OAAO,IAAIgC,aAAa,CAAC/B,IAAI,EAAE;QAChEzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAM0D,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC/B,OAAO,IAAI+B,eAAe,CAAC9B,IAAI,EAAE;UACtE8B,eAAe,CAAC9B,IAAI,CAAC/C,OAAO,CAACyE,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAT,IAAI,CAAC5I,IAAI,cAAAqJ,UAAA,uBAATA,UAAA,CAAWjE,GAAG,KAAIwD,IAAI,CAACU,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVF,cAAc,CAACE,MAAM,CAAC,GAAGV,IAAI,CAACW,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAjC,eAAe,GAAG2B,aAAa,CAAC/B,IAAI,CACjClC,MAAM,CAACoC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAChC,GAAG,IAAIgC,QAAQ,CAACoC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EjC,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,KAAK;UACxB;UACA,MAAMiC,WAAW,GAAGL,cAAc,CAAChC,QAAQ,CAAChC,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIsE,YAAY,GAAGD,WAAW,CAACnE,MAAM,IAAI8B,QAAQ,CAACC,iBAAiB,IAAI,CAAC;UACxE,IAAIsC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIjC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAGtC,QAAQ,CAACU,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAACnE,MAAM,IAAI8B,QAAQ,CAAC8C,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAAC8C,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC5H,GAAG,CAAC,EAAE,EAAE4H,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIhD,QAAQ,CAAC8C,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BrC,YAAY,GAAGkC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACnC,YAAY,GAAG4B,YAAY,CAAC;YAEpDjE,OAAO,CAACC,GAAG,CAAE,0BAAyB0B,QAAQ,CAACK,IAAK,KAAI0C,gBAAiB,aAAYG,gBAAiB,cAAalD,QAAQ,CAAC8C,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI7F,OAAO,GAAG+C,QAAQ,CAAC/C,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAI+C,QAAQ,CAAC8C,WAAW,EAAE;cACxB;cACA7F,OAAO,GAAG2F,IAAI,CAACK,KAAK,CAClBjD,QAAQ,CAAC8C,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACArF,OAAO,GAAG2F,IAAI,CAACK,KAAK,CACjBvC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGX,QAAQ,CAACW,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAACnE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIiF,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACtF,OAAO,CAAC2F,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZvC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACpC,UAAU,EAAEuC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFxC,aAAa,GAAGwC,UAAU;UAC5B,CAAC,MAAM,IAAInD,QAAQ,CAAC8C,WAAW,IAAI,CAACnC,aAAa,EAAE;YACjD;YACA,MAAMyC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAGtC,QAAQ,CAAC8C,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBzC,aAAa,GAAGiC,IAAI,CAAC5H,GAAG,CAACsH,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxExC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACrC,aAAa,EAAEiC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLpF,GAAG,EAAEgC,QAAQ,CAAChC,GAAG;YACjBqC,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACQ,cAAc,IAAI,EAAE;YAC7CvD,OAAO,EAAEA,OAAO;YAChBgD,iBAAiB,EAAEqC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACfjD,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5B8D,UAAU,EAAE,IAAI;YAChB;YACAsC,cAAc,EAAErD,QAAQ,CAAC8C,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAACnE,MAAM,GAAG,CAAC;YAClCoD,UAAU,EAAEe,WAAW,CAACnE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG8B,QAAQ,CAAC8C,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA5C,eAAe,CAAC5C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACAiD,eAAe,CAACnD,OAAO,CAAC,CAACnE,IAAI,EAAEwH,KAAK,KAAK;UACvCxH,IAAI,CAACkI,IAAI,GAAGV,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFrH,cAAc,CAACmH,eAAe,CAAC;;QAE/B;QACA,IAAIrC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIjF,IAAI,EAAE;UACR;UACAiF,QAAQ,GAAGqC,eAAe,CAACpC,SAAS,CAAC0D,IAAI,IAAIA,IAAI,CAACxD,GAAG,KAAKpF,IAAI,CAACoF,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGqC,eAAe,CAACpC,SAAS,CAAC0D,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACxD,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIjF,IAAI,CAACyH,IAAI,EAAE;YAChCxC,QAAQ,GAAGqC,eAAe,CAACpC,SAAS,CAAC0D,IAAI,IAAIA,IAAI,CAACnB,IAAI,KAAKzH,IAAI,CAACyH,IAAI,CAAC;UACvE;QACF;QAEAlH,kBAAkB,CAAC0E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIjF,IAAI,EAAE;UACR,MAAM6I,cAAc,GAAGhE,wBAAwB,CAACyC,eAAe,EAAEtH,IAAI,CAAC;UACtEmB,oBAAoB,CAAC0H,cAAc,CAAC;UACpCxH,cAAc,CAAC,CAAAwH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE9I,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAM+I,OAAO,GAAG7E,kBAAkB,CAACqD,eAAe,CAAC;QACnD3F,eAAe,CAACmH,OAAO,CAAC;;QAExB;QACArD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CX,WAAW,EAAE/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyH,IAAI;UACvB6B,MAAM,EAAEtJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG;UACjBwF,UAAU,EAAE,QAAO5K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG;UAC5ByF,OAAO,EAAE,CAAA7K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwJ,IAAI,MAAK,OAAO,KAAIxJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6K,OAAO;UAChDC,MAAM,EAAE9K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,OAAO;UACrBsE,aAAa,EAAE1D,QAAQ;UACvB8F,gBAAgB,EAAE9F,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrD+F,gBAAgB,EAAE1D,eAAe,CAAChC,MAAM;UACxC2F,eAAe,EAAE3D,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACpC,CAAC,KAAK;YAAEgG,EAAE,EAAEhG,CAAC,CAACC,GAAG;YAAEgG,IAAI,EAAE,OAAOjG,CAAC,CAACC,GAAG;YAAEqC,IAAI,EAAEtC,CAAC,CAACsC;UAAK,CAAC,CAAC,CAAC;UACxG4D,UAAU,EAAE/D,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACxD,GAAG,MAAKpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,EAAC;UAChEmG,WAAW,EAAEjE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACxD,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,CAAC,CAAC;UACjFoG,SAAS,EAAElE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACnB,IAAI,MAAKzH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyH,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMgE,WAAW,GAAG;UAClBlC,OAAO,EAAEjC,eAAe,CAACtC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,SAAS,CAAC,CAACpD,MAAM;UACvEoG,aAAa,EAAEpE,eAAe,CAACtC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,eAAe,CAAC,CAACpD,MAAM;UACnFqG,SAAS,EAAErE,eAAe,CAACtC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,WAAW,CAAC,CAACpD;QACvE,CAAC;QAEDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE4B,eAAe,CAAChC,MAAM,EAAE,gBAAgB,CAAC;QACxFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+F,WAAW,CAAC;QAC5ChG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4B,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACpC,CAAC,KAAK;UACvEsC,IAAI,EAAEtC,CAAC,CAACsC,IAAI;UACZ7D,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACbuH,OAAO,EAAEzG,CAAC,CAACkC,iBAAiB;UAC5BwE,GAAG,EAAE1G,CAAC,CAAC2C,YAAY;UACnBgE,MAAM,EAAE3G,CAAC,CAACuD;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLjD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCvF,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvC,OAAO,CAAC+N,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDlL,OAAO,CAACkL,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR7I,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACdiJ,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMqF,WAAW,GAAG/J,kBAAkB,CAAC+H,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAGhK,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FvE,oBAAoB,CAACiL,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCtL,iBAAiB,CAACuL,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B5G,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DiB,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM2F,mBAAmB,GAAIC,KAAK,IAAK;MACrC9G,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE6G,KAAK,CAACC,MAAM,CAAC;MACnE;MACA7G,UAAU,CAAC,MAAM;QACfgB,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED8F,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,iBAAiB,CAAC;IACnDI,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXK,aAAa,CAACT,cAAc,CAAC;MAC7B;MACAO,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEP,iBAAiB,CAAC;MACtDI,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEN,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5O,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAI0B,YAAY,IAAIqC,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,IAAI,CAAC9D,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAACiD,SAAS,EAAEoI,UAAU,CAAC,IAAI9I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;QAClE,MAAMoL,YAAY,GAAGD,UAAU,CAAC9M,KAAK,CAACuL,IAAI,CAACnG,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;QACnF,IAAI0H,YAAY,EAAE;UAChBrH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEjB,SAAS,CAAC;UACxDhD,iBAAiB,CAACgD,SAAS,CAAC;UAC5BlD,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAACwL,UAAU,CAAC9M,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACC,IAAI,EAAE0B,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAMuL,aAAa,GAAG7M,WAAW,CAACgL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM8B,eAAe,GAAG9M,WAAW,CAACgL,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACjN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoF,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAM8H,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAChI,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;IAC9F,IAAI8H,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAC7H,SAAS,CAACkI,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAChI,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACLgG,IAAI,EAAE,QAAQ;QACdkC,QAAQ,EAAED,cAAc;QACxBxJ,MAAM,EAAE,iBAAiB;QACzBY,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEoI,UAAU,CAAC,IAAI9I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;MAAA,IAAA6L,iBAAA;MAClE,MAAMT,YAAY,IAAAS,iBAAA,GAAGV,UAAU,CAAC9M,KAAK,cAAAwN,iBAAA,uBAAhBA,iBAAA,CAAkBjC,IAAI,CAACnG,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;MACpF,IAAI0H,YAAY,EAAE;QAChB,MAAMQ,QAAQ,GAAGT,UAAU,CAAC9M,KAAK,CAACmF,SAAS,CAACC,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACLgG,IAAI,EAAE,QAAQ;UACdkC,QAAQ,EAAEA,QAAQ;UAClBzJ,MAAM,EAAEgJ,UAAU,CAACjK,KAAK;UACxB6B,SAAS,EAAEA,SAAS;UACpB+I,UAAU,EAAEX,UAAU,CAAC9M,KAAK,CAACuF;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMmI,cAAc,GAAGR,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMS,aAAa,GAAIpE,MAAM,IAAK;IAChC,MAAMqE,MAAM,GAAG3N,IAAI,IAAI2K,MAAM,CAACrB,MAAM,CAAC,KAAKqB,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC;IAC1D,IAAIpE,UAAU,IAAI2M,MAAM,EAAE;MACxBlI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAE4D,MAAM;QAAEsE,aAAa,EAAE5N,IAAI,CAACoF,GAAG;QAAEuI;MAAO,CAAC,CAAC;IAC3F;IACA,OAAOA,MAAM;EACf,CAAC;;EAED;EACAjQ,SAAS,CAAC,MAAM;IACd,IAAI,EAACsC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoF,GAAG,GAAE;IAEhB,MAAMyI,YAAY,GAAGA,CAAA,KAAM;MACzB;MACA,MAAMX,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAChI,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;MAE9F,IAAI8H,UAAU,EAAE;QACd;QACA,MAAMY,aAAa,GAAGjI,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvE,IAAIgI,aAAa,EAAE;UACjBnI,UAAU,CAAC,MAAM;YACfmI,aAAa,CAAC7H,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM,IAAIhF,WAAW,CAACkE,MAAM,GAAG,CAAC,EAAE;QACjC;QACA,MAAMyI,iBAAiB,GAAG3M,WAAW,CAAC+L,IAAI,CAAChI,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;QACnF,IAAI2I,iBAAiB,EAAE;UACrB;UACA,MAAMC,WAAW,GAAGnI,QAAQ,CAACC,aAAa,CAAE,kBAAiB9F,IAAI,CAACoF,GAAI,IAAG,CAAC;UAC1E,IAAI4I,WAAW,EAAE;YACfrI,UAAU,CAAC,MAAM;cACfqI,WAAW,CAAC/H,cAAc,CAAC;gBACzBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE,QAAQ;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM6H,KAAK,GAAGtI,UAAU,CAACkI,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMK,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACjO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,EAAE2H,aAAa,EAAE3L,WAAW,CAAC,CAAC;;EAE3C;EACA,MAAM+M,oBAAoB,GAAGA,CAAClG,kBAAkB,EAAEmG,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1E3I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCuC,kBAAkB;MAClBmG,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAItG,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAACyG,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBvM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL8L,IAAI,EAAE,SAAS;UACfvM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL8L,IAAI,EAAE,SAAS;QACfvM,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI1C,OAAO,IAAIF,WAAW,CAACoF,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE9F,OAAA;MAAKqP,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHtP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBtP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzB5I,UAAU,EAAE;YAAE6I,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFnQ,OAAA;UAAGqP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEnQ,OAAA,CAAAE,SAAA;IAAAoP,QAAA,gBACEtP,OAAA;MAAAsP,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVnQ,OAAA;MAAKqP,SAAS,EAAC,kIAAkI;MAAAC,QAAA,gBAEjJtP,OAAA;QAAKqP,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CtP,OAAA;UAAKqP,SAAS,EAAC;QAA2H;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJnQ,OAAA;UAAKqP,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtKnQ,OAAA;UAAKqP,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnKnQ,OAAA;UAAKqP,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGNnQ,OAAA;QAAKqP,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGc,KAAK,CAAC,EAAE,CAAC,CAAC,CAACrI,GAAG,CAAC,CAACsI,CAAC,EAAEC,CAAC,kBACvBtQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAEhG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCgD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACF1I,UAAU,EAAE;YACV6I,QAAQ,EAAE,CAAC,GAAGpF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BoD,MAAM,EAAEC,QAAQ;YAChBW,KAAK,EAAEjG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACF5F,KAAK,EAAE;YACL6J,IAAI,EAAG,GAAElG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BkE,GAAG,EAAG,GAAEnG,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfG6D,CAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnQ,OAAA;QAAKqP,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BtP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE6I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtEtP,OAAA;YAAKqP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtP,OAAA;cAAKqP,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnItP,OAAA;gBAAKqP,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGtP,OAAA,CAAC5B,MAAM,CAACwS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMvQ,QAAQ,CAAC,WAAW,CAAE;kBACrC4O,SAAS,EAAC,gNAAgN;kBAC1NxI,KAAK,EAAE;oBACLoK,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFtP,OAAA,CAACjB,MAAM;oBAACsQ,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CnQ,OAAA;oBAAAsP,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGflC,cAAc,iBACbjO,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BlB,SAAS,EAAC,mJAAmJ;kBAC7JxI,KAAK,EAAE;oBACLsK,UAAU,EAAElD,cAAc,CAACrC,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/C/I,KAAK,EAAEoL,cAAc,CAACrC,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/D5E,SAAS,EAAE,oCAAoC;oBAC/CiK,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAA5B,QAAA,gBAEFtP,OAAA,CAACvB,QAAQ;oBAAC4Q,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CnQ,OAAA;oBAAAsP,QAAA,EACGrB,cAAc,CAACrC,IAAI,KAAK,QAAQ,GAC5B,cAAaqC,cAAc,CAACH,QAAS,EAAC,GACtC,GAAEG,cAAc,CAAC5J,MAAO,KAAI4J,cAAc,CAACH,QAAS;kBAAC;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,EAGA3O,UAAU,iBACTxB,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE,GAAG;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC5Cb,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVqB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBP,CAAC,EAAE,CAAC;oBACJvJ,SAAS,EAAE,CACT,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC;kBAErC,CAAE;kBACFD,UAAU,EAAE;oBACV6I,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAC;oBACxC9I,SAAS,EAAE;sBAAE4I,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC;oBAAS;kBAC/C,CAAE;kBACFT,SAAS,EAAC,yJAAyJ;kBAAAC,QAAA,eAEnKtP,OAAA;oBAAKqP,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CtP,OAAA;sBAAKqP,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtCtP,OAAA,CAACnB,QAAQ;wBAACwQ,SAAS,EAAC;sBAAuB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9CnQ,OAAA;wBAAAsP,QAAA,EAAM;sBAAgB;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACNnQ,OAAA;sBAAKqP,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACrB9O,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoF,GAAG,GAAI,SAAQpF,IAAI,CAACyH,IAAI,IAAIzH,IAAI,CAACoF,GAAI,EAAC,GAAG;oBAAkC;sBAAAoK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb,eAGDnQ,OAAA,CAAC5B,MAAM,CAACwS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACb/K,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;oBACzCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE1F,IAAI,CAAC;oBACpCiB,aAAa,CAAC,IAAI,CAAC;oBACnBwE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;oBAE9C;oBACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,CAAC;oBAC7CK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqH,aAAa,CAACxF,GAAG,CAACqJ,CAAC,KAAK;sBAAEzF,EAAE,EAAEyF,CAAC,CAACxL,GAAG;sBAAEqC,IAAI,EAAEmJ,CAAC,CAACnJ;oBAAK,CAAC,CAAC,CAAC,CAAC;oBACxFhC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE3B,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC;oBAC3D+D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAExF,WAAW,CAACoF,MAAM,CAAC;;oBAE1D;oBACA,IAAI,EAACtF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoF,GAAG,GAAE;sBACdK,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;sBACnE;sBACAC,UAAU,CAAC,MAAM;wBACfF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;wBACnDzE,aAAa,CAAC,KAAK,CAAC;sBACtB,CAAC,EAAE,IAAI,CAAC;sBACR;oBACF,CAAC,MAAM;sBAEP;sBACA,MAAMiM,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzC,MAAM,CAACyC,SAAS,CAAChI,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;sBAC9FK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwH,UAAU,CAAC;;sBAE7C;sBACA,KAAK,MAAM,CAACzI,SAAS,EAAEoI,UAAU,CAAC,IAAI9I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;wBAAA,IAAAmP,kBAAA;wBAClE,MAAM/D,YAAY,IAAA+D,kBAAA,GAAGhE,UAAU,CAAC9M,KAAK,cAAA8Q,kBAAA,uBAAhBA,kBAAA,CAAkBvF,IAAI,CAACnG,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;wBACpF,IAAI0H,YAAY,EAAE;0BAChBrH,OAAO,CAACC,GAAG,CAAE,4BAA2BjB,SAAU,EAAC,EAAE;4BACnDqM,WAAW,EAAEjE,UAAU,CAACjK,KAAK;4BAC7BmO,YAAY,EAAElE,UAAU,CAAC9M,KAAK,CAACmF,SAAS,CAACC,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC,GAAG,CAAC;4BACrFC,aAAa,EAAEwH,UAAU,CAAC9M,KAAK,CAACuF;0BAClC,CAAC,CAAC;wBACJ;sBACF;sBAEE,IAAI4H,UAAU,EAAE;wBACd;wBACA,MAAMY,aAAa,GAAGjI,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;wBACvE,IAAIgI,aAAa,EAAE;0BACjBnI,UAAU,CAAC,MAAM;4BACfmI,aAAa,CAAC7H,cAAc,CAAC;8BAC3BC,QAAQ,EAAE,QAAQ;8BAClBC,KAAK,EAAE,QAAQ;8BACfC,MAAM,EAAE;4BACV,CAAC,CAAC;0BACJ,CAAC,EAAE,GAAG,CAAC;wBACT;sBACF,CAAC,MAAM;wBACL;wBACA,KAAK,MAAM,CAAC3B,SAAS,EAAEoI,UAAU,CAAC,IAAI9I,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;0BAAA,IAAAsP,kBAAA;0BAClE,MAAMlE,YAAY,IAAAkE,kBAAA,GAAGnE,UAAU,CAAC9M,KAAK,cAAAiR,kBAAA,uBAAhBA,kBAAA,CAAkB1F,IAAI,CAACnG,CAAC,IAAIwF,MAAM,CAACxF,CAAC,CAACC,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,CAAC;0BACpF,IAAI0H,YAAY,EAAE;4BAChB;4BACArL,iBAAiB,CAACgD,SAAS,CAAC;;4BAE5B;4BACAkB,UAAU,CAAC,MAAM;8BACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBrB,SAAU,IAAG,CAAC,IACvDoB,QAAQ,CAACE,cAAc,CAAE,UAAStB,SAAU,EAAC,CAAC;8BAEnE,IAAImB,aAAa,EAAE;gCACjBA,aAAa,CAACK,cAAc,CAAC;kCAC3BC,QAAQ,EAAE,QAAQ;kCAClBC,KAAK,EAAE,QAAQ;kCACfC,MAAM,EAAE;gCACV,CAAC,CAAC;;gCAEF;gCACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;gCAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;gCAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,iCAAiC;gCAEjEb,UAAU,CAAC,MAAM;kCACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;kCAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;gCACpC,CAAC,EAAE,IAAI,CAAC;8BACV;;8BAEA;8BACAb,UAAU,CAAC,MAAM;gCACf,MAAMqI,WAAW,GAAGnI,QAAQ,CAACC,aAAa,CAAE,kBAAiB9F,IAAI,CAACoF,GAAI,IAAG,CAAC;gCAC1E,IAAI4I,WAAW,EAAE;kCACfA,WAAW,CAAC/H,cAAc,CAAC;oCACzBC,QAAQ,EAAE,QAAQ;oCAClBC,KAAK,EAAE,QAAQ;oCACfC,MAAM,EAAE;kCACV,CAAC,CAAC;gCACJ;8BACF,CAAC,EAAE,IAAI,CAAC;4BACV,CAAC,EAAE,GAAG,CAAC;4BACP;0BACF;wBACF;sBACF;;sBAEA;sBACAT,UAAU,CAAC,MAAM;wBACfF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;wBACvCzE,aAAa,CAAC,KAAK,CAAC;sBACtB,CAAC,EAAE,IAAI,CAAC;oBACV,CAAC,CAAC;kBACF,CAAE;;kBACF4N,SAAS,EAAC,6OAA6O;kBACvPxI,KAAK,EAAE;oBACLoK,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;oBACvDC,UAAU,EAAE,2CAA2C;oBACvDnK,SAAS,EAAE,qEAAqE;oBAChFyK,UAAU,EAAE;kBACd,CAAE;kBAAAnC,QAAA,gBAEFtP,OAAA,CAACnB,QAAQ;oBAACwQ,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CnQ,OAAA;oBAAAsP,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eASlBnQ,OAAA;kBAAKqP,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpItP,OAAA,CAAC5B,MAAM,CAACsT,EAAE;oBACRrC,SAAS,EAAC,sCAAsC;oBAChDxI,KAAK,EAAE;sBACLsK,UAAU,EAAE,mDAAmD;sBAC/DQ,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCH,UAAU,EAAE,6BAA6B;sBACzCjM,MAAM,EAAE;oBACV,CAAE;oBACFkK,OAAO,EAAE;sBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjC/J,UAAU,EAAE;sBAAE6I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAR,QAAA,EAC/C;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZnQ,OAAA;oBAAKqP,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvErI,iBAAiB,CAAC,CAAC,CAACc,GAAG,CAAE9C,SAAS,IAAK;sBAAA,IAAA4M,sBAAA;sBACtC,MAAMxN,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAM6M,UAAU,GAAG9P,cAAc,KAAKiD,SAAS;sBAC/C,MAAM8M,SAAS,GAAG,EAAAF,sBAAA,GAAA3P,YAAY,CAAC+C,SAAS,CAAC,cAAA4M,sBAAA,uBAAvBA,sBAAA,CAAyBtR,KAAK,CAACuF,MAAM,KAAI,CAAC;sBAE5D,oBACE9F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;wBAETF,SAAS,EAAC,kCAAkC;wBAC5CwB,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAAxB,QAAA,gBAE5BtP,OAAA,CAAC5B,MAAM,CAACwS,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCQ,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BE,OAAO,EAAEA,CAAA,KAAMjL,kBAAkB,CAACd,SAAS,CAAE;0BAC7CoK,SAAS,EAAG,+GACVyC,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACHjL,KAAK,EAAE;4BACLsK,UAAU,EAAEW,UAAU,GACjB,2BAA0BzN,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7EiP,MAAM,EAAG,aAAYF,UAAU,GAAG,SAAS,GAAGzN,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzE0D,SAAS,EAAE8K,UAAU,GAChB,YAAWzN,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxC6D,SAAS,EAAEgL,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjDtM,MAAM,EAAEsM,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACFpC,OAAO,EAAEoC,UAAU,GAAG;4BACpB9K,SAAS,EAAE,CACR,YAAW3C,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACD6N,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACP/J,UAAU,EAAE;4BACV6I,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAEiC,UAAU,GAAGhC,QAAQ,GAAG,CAAC;4BACjCC,IAAI,EAAE;0BACR,CAAE;0BACF3M,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAW2O,SAAU,SAAS;0BAAAzC,QAAA,gBAEnEtP,OAAA;4BAAMqP,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAEjL,MAAM,CAACb;0BAAU;4BAAAwM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChE2B,UAAU,iBACT9R,OAAA,CAAC5B,MAAM,CAACmR,GAAG;4BACTC,OAAO,EAAE;8BAAEsB,KAAK,EAAE,CAAC;8BAAEnB,MAAM,EAAE,CAAC,GAAG;8BAAEF,OAAO,EAAE;4BAAE,CAAE;4BAChDC,OAAO,EAAE;8BACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClBnB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBF,OAAO,EAAE,CAAC;8BACVzI,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACV+J,KAAK,EAAE;gCAAElB,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BAC3DJ,MAAM,EAAE;gCAAEC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAS,CAAC;8BACzD/I,SAAS,EAAE;gCAAE4I,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAEC,IAAI,EAAE;8BAAY,CAAC;8BACjEN,OAAO,EAAE;gCAAEG,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFP,SAAS,EAAC,8KAA8K;4BACxLxI,KAAK,EAAE;8BACLsK,UAAU,EAAE,mDAAmD;8BAC/Da,MAAM,EAAE,iBAAiB;8BACzBC,MAAM,EAAE;4BACV,CAAE;4BAAA3C,QAAA,eAEFtP,OAAA,CAAC5B,MAAM,CAAC8T,IAAI;8BACV7C,SAAS,EAAC,kCAAkC;8BAC5CK,OAAO,EAAE;gCACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClBnB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACF5I,UAAU,EAAE;gCACV6I,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChBC,IAAI,EAAE;8BACR,CAAE;8BAAAT,QAAA,EACH;4BAED;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACDnQ,OAAA;4BACEqP,SAAS,EAAC,2HAA2H;4BACrIxI,KAAK,EAAE;8BACLsK,UAAU,EAAE9M,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChBoO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAEDyC;0BAAS;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhBnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;0BACTF,SAAS,EAAC,aAAa;0BACvBwB,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAAxB,QAAA,eAE5BtP,OAAA;4BACEqP,SAAS,EAAC,mDAAmD;4BAC7DxI,KAAK,EAAE;8BACLhE,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByO,UAAU,EAAG,eAAcpN,MAAM,CAACpB,WAAY,EAAC;8BAC/CkO,UAAU,EAAG,GAAE9M,MAAM,CAACf,WAAY,IAAG;8BACrC0O,MAAM,EAAG,aAAY3N,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAAgM,QAAA,EAEDjL,MAAM,CAACjB;0BAAK;4BAAA4M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GRlL,SAAS;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENnQ,OAAA;oBAAGqP,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGNnQ,OAAA,CAAC5B,MAAM,CAACwS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEnB,MAAM,EAAE;kBAAI,CAAE;kBACzCoB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE7J,gBAAiB;kBAC1BgL,QAAQ,EAAEvR,OAAQ;kBAClByO,SAAS,EAAC,qNAAqN;kBAC/NxI,KAAK,EAAE;oBACLoK,QAAQ,EAAEhE,MAAM,CAACiE,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFtP,OAAA,CAAChB,SAAS;oBAACqQ,SAAS,EAAG,yBAAwBzO,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAoP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFnQ,OAAA;oBAAAsP,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAA3P,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwJ,IAAI,MAAK,OAAO,KAAIxJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6K,OAAO,mBACvCrL,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE6I,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CtP,OAAA;YAAKqP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtP,OAAA;cAAKqP,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HtP,OAAA;gBAAKqP,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCtP,OAAA;kBAAKqP,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFtP,OAAA;oBAAMqP,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNnQ,OAAA;kBAAAsP,QAAA,gBACEtP,OAAA;oBAAIqP,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDnQ,OAAA;oBAAGqP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE6I,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzCtP,OAAA;YAAKqP,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GtP,OAAA;cAAKqP,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGnQ,OAAA;cAAKqP,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGnQ,OAAA;cAAKqP,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFtP,OAAA;gBAAKqP,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CtP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTG,OAAO,EAAE;oBACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFrL,UAAU,EAAE;oBACV6I,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBtP,OAAA;oBAAIqP,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GtP,OAAA,CAAC5B,MAAM,CAAC8T,IAAI;sBACVxC,OAAO,EAAE;wBACP2C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFtL,UAAU,EAAE;wBACV6I,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIxI,KAAK,EAAE;wBACLyL,cAAc,EAAE,WAAW;wBAC3BX,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCpM,MAAM,EAAE;sBACV,CAAE;sBAAA8J,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdnQ,OAAA;sBAAAgQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnQ,OAAA,CAAC5B,MAAM,CAAC8T,IAAI;sBACVxC,OAAO,EAAE;wBACP+B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF1K,UAAU,EAAE;wBACV6I,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFlJ,KAAK,EAAE;wBACLhE,KAAK,EAAE,SAAS;wBAChB0P,UAAU,EAAE,KAAK;wBACjBd,UAAU,EAAE;sBACd,CAAE;sBAAAnC,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbnQ,OAAA,CAAC5B,MAAM,CAACgT,CAAC;kBACP5B,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BxJ,UAAU,EAAE;oBAAE0J,KAAK,EAAE,GAAG;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHxI,KAAK,EAAE;oBACLhE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCN,UAAU,EAAE,0CAA0C;oBACtDQ,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAtC,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAI,CAAE;kBACpCpB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBAClC/J,UAAU,EAAE;oBAAE0J,KAAK,EAAE,GAAG;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBtP,OAAA;oBAAGqP,SAAS,EAAC,6JAA6J;oBACvKxI,KAAK,EAAE;sBACL4K,UAAU,EAAE,6BAA6B;sBACzCe,SAAS,EAAE;oBACb,CAAE;oBAAAlD,QAAA,EACFhO;kBAAiB;oBAAA0O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAG,CAAE;kBAC/Bb,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE;kBAAE,CAAE;kBAC9BxJ,UAAU,EAAE;oBAAE0J,KAAK,EAAE,CAAC;oBAAEb,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACEnM,IAAI,EAAE5D,OAAO;oBACbkT,KAAK,EAAE/R,WAAW,CAACoF,MAAM;oBACzB4M,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBtP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE1E,QAAQ;oBACdgU,KAAK,EAAElF,aAAa,CAACzH,MAAM;oBAC3B4M,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBtP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEvE,OAAO;oBACb6T,KAAK,EAAE/R,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC4C,aAAa,GAAG,CAAC,CAAC,CAACzC,MAAM;oBAC1D4M,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBtP,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAExE,MAAM;oBACZ8T,KAAK,EAAE/R,WAAW,CAAC0J,MAAM,CAAC,CAACC,GAAG,EAAE1E,CAAC,KAAK0E,GAAG,IAAI1E,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgO,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBtP,WAAW,EAAE;kBACf,CAAC,CACF,CAACyE,GAAG,CAAC,CAAC+K,IAAI,EAAE9K,KAAK,kBAChBhI,OAAA,CAAC5B,MAAM,CAACmR,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAI,CAAE;oBACpCpB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAE,CAAE;oBAClC/J,UAAU,EAAE;sBAAE0J,KAAK,EAAE,GAAG,GAAGzI,KAAK,GAAG,GAAG;sBAAE4H,QAAQ,EAAE;oBAAI,CAAE;oBACxDiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEP,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnClB,SAAS,EAAG,qBAAoByD,IAAI,CAACH,UAAW,8EAA8E;oBAC9H9L,KAAK,EAAE;sBACLmL,MAAM,EAAG,aAAYc,IAAI,CAACxP,WAAY,IAAG;sBACzC0D,SAAS,EAAG,cAAa8L,IAAI,CAACxP,WAAY;oBAC5C,CAAE;oBAAAgM,QAAA,gBAEFtP,OAAA;sBAAKqP,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFnQ,OAAA,CAAC8S,IAAI,CAAC3P,IAAI;sBACRkM,SAAS,EAAC,kDAAkD;sBAC5DxI,KAAK,EAAE;wBAAEhE,KAAK,EAAEiQ,IAAI,CAACF,SAAS;wBAAEpN,MAAM,EAAE;sBAAyC;oBAAE;sBAAAwK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFnQ,OAAA;sBACEqP,SAAS,EAAC,0EAA0E;sBACpFxI,KAAK,EAAE;wBACLhE,KAAK,EAAEiQ,IAAI,CAACF,SAAS;wBACrBnB,UAAU,EAAG,6BAA4B;wBACzCjM,MAAM,EAAE,oCAAoC;wBAC5CyL,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDwD,IAAI,CAACL;oBAAK;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNnQ,OAAA;sBACEqP,SAAS,EAAC,4CAA4C;sBACtDxI,KAAK,EAAE;wBACLhE,KAAK,EAAE,SAAS;wBAChB4O,UAAU,EAAE,6BAA6B;wBACzCR,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDwD,IAAI,CAACJ;oBAAK;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDnI,KAAK;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZvP,OAAO,iBACNZ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DtP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzB5I,UAAU,EAAE;cAAE6I,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFnQ,OAAA;YAAGqP,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACvP,OAAO,iBACPZ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAG,CAAE;UAC/Bb,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BxJ,UAAU,EAAE;YAAE0J,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjEtP,OAAA;YAAKqP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/B/B,aAAa,CAACzH,MAAM,GAAG,CAAC,iBACvB9F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC/J,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBtP,OAAA;gBAAIqP,SAAS,EAAC,gGAAgG;gBAACxI,KAAK,EAAE;kBACpHsK,UAAU,EAAE,mDAAmD;kBAC/DQ,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCH,UAAU,EAAE,6BAA6B;kBACzCjM,MAAM,EAAE;gBACV,CAAE;gBAAA8J,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLnQ,OAAA;gBAAKqP,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/G/B,aAAa,CAAC,CAAC,CAAC,iBACfvN,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBAETwD,GAAG,EAAEvS,IAAI,IAAI2K,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcgL,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI;kBACnC,kBAAgB,CAAE;kBAClB4J,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCb,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVe,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFrL,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DqC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,oBACVnB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GAC7C,2DAA2D,GAC3D0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLmM,MAAM,EAAE,OAAO;oBACflM,SAAS,EAAEoH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE0I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GACrD,0EAA0E,GAC1E0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3BiL,MAAM,EAAE9D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EqN,YAAY,EAAE/E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEuL,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA0J,QAAA,gBAGFtP,OAAA;oBAAKqP,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnKtP,OAAA;sBAAMqP,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNnQ,OAAA;oBACEqP,SAAS,EAAG,8BAA6B9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAClC,KAAM,mBAAkB0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpI2D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAauG,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,IAAG;sBAC9DiQ,KAAK,EAAE;oBACT,CAAE;oBAAA5D,QAAA,eAEFtP,OAAA;sBACEqP,SAAS,EAAG,GAAE9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAwM,QAAA,gBAEnHtP,OAAA;wBAAKqP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFnQ,OAAA;wBACEqP,SAAS,EAAC,oMAAoM;wBAC9MxI,KAAK,EAAE;0BACLhE,KAAK,EAAE,SAAS;0BAChBmP,MAAM,EAAE;wBACV,CAAE;wBAAA1C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnQ,OAAA;wBAAKqP,SAAS,EAAG,yBAAwB7O,IAAI,IAAI+M,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA0J,QAAA,eACnItP,OAAA;0BACEqP,SAAS,EAAC,wEAAwE;0BAClFxI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvCkM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA1D,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BpI,OAAA;4BACEmT,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrCgL,GAAG,EAAE7F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BoH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnQ,OAAA;4BACEqP,SAAS,EAAC,2EAA2E;4BACrFxI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBtO,KAAK,EAAE,SAAS;8BAChBoO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNnQ,OAAA;wBACEqP,SAAS,EAAC,iCAAiC;wBAC3CxI,KAAK,EAAE;0BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAsM,QAAA,EAEjD/B,aAAa,CAAC,CAAC,CAAC,CAACtF;sBAAI;wBAAA+H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELnQ,OAAA;wBAAKqP,SAAS,EAAC,yBAAyB;wBAACxI,KAAK,EAAE;0BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;wBAAU,CAAE;wBAAAuM,QAAA,GACxF/B,aAAa,CAAC,CAAC,CAAC,CAAC1I,OAAO,CAACgO,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENnQ,OAAA;wBAAKqP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDtP,OAAA;0BAAM6G,KAAK,EAAE;4BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;0BAAU,CAAE;0BAAAuM,QAAA,GAAC,eACpD,EAAC/B,aAAa,CAAC,CAAC,CAAC,CAAC1F,iBAAiB;wBAAA;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPnQ,OAAA;0BAAM6G,KAAK,EAAE;4BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;0BAAU,CAAE;0BAAAuM,QAAA,GAAC,eACpD,EAAC/B,aAAa,CAAC,CAAC,CAAC,CAAChF,aAAa;wBAAA;0BAAAyH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA1HA,UAAS5C,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI,EAAC;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2H3B,CACb,EAGA5C,aAAa,CAAC,CAAC,CAAC,iBACfvN,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBAETwD,GAAG,EAAEvS,IAAI,IAAI2K,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcgL,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI;kBACnC,kBAAgB,CAAE;kBAClB4J,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEc,CAAC,EAAE,CAAC,GAAG;oBAAEO,KAAK,EAAE;kBAAI,CAAE;kBAC7CpB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVc,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC;oBACRsB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxB7B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFxJ,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbwC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DQ,CAAC,EAAE;sBAAEX,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,yBACVnB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GAC7C,2DAA2D,GAC3D0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLmM,MAAM,EAAE,OAAO;oBACflM,SAAS,EAAEoH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE0I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GACrD,4FAA4F,GAC5F0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3BiL,MAAM,EAAE9D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EqN,YAAY,EAAE/E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEuL,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAA0J,QAAA,gBAIrBtP,OAAA;oBAAKqP,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzKtP,OAAA;sBAAMqP,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGNnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDxJ,UAAU,EAAE;sBAAE6I,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErEtP,OAAA,CAACtB,OAAO;sBAAC2Q,SAAS,EAAC;oBAA0C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGbnQ,OAAA;oBACEqP,SAAS,EAAG,8BAA6B9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAClC,KAAM,sBAAqB0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5J2D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAauG,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/FiQ,KAAK,EAAE;oBACT,CAAE;oBAAA5D,QAAA,eAEFtP,OAAA;sBACEqP,SAAS,EAAG,GAAE9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH+D,KAAK,EAAE;wBACLsK,UAAU,EAAG,GAAE5D,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAwM,QAAA,gBAEFtP,OAAA;wBAAKqP,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlGnQ,OAAA;wBACEqP,SAAS,EAAC,wMAAwM;wBAClNxI,KAAK,EAAE;0BACLhE,KAAK,EAAE,SAAS;0BAChB4O,UAAU,EAAE,6BAA6B;0BACzCO,MAAM,EAAE;wBACV,CAAE;wBAAA1C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnQ,OAAA;wBAAKqP,SAAS,EAAG,yBAAwB7O,IAAI,IAAI+M,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA0J,QAAA,gBACnItP,OAAA;0BACEqP,SAAS,EAAC,wEAAwE;0BAClFxI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvCkM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA1D,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BpI,OAAA;4BACEmT,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrCgL,GAAG,EAAE7F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BoH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnQ,OAAA;4BACEqP,SAAS,EAAC,2EAA2E;4BACrFxI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBtO,KAAK,EAAE,SAAS;8BAChBoO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL3P,IAAI,IAAI+M,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,KAAKpF,IAAI,CAACoF,GAAG,iBACxC5F,OAAA;0BACEqP,SAAS,EAAC,4DAA4D;0BACtExI,KAAK,EAAE;4BACLsK,UAAU,EAAE,0CAA0C;4BACtDnK,SAAS,EAAE;0BACb,CAAE;0BAAAsI,QAAA,eAEFtP,OAAA,CAACrB,MAAM;4BAAC0Q,SAAS,EAAC;0BAAuB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNnQ,OAAA;wBAAKqP,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1DtP,OAAA;0BACEqP,SAAS,EAAC,6BAA6B;0BACvCxI,KAAK,EAAE;4BACLhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC/B,SAAS;4BACtCyO,UAAU,EAAG,eAAclE,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA8J,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACtF;wBAAI;0BAAA+H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJjC,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,iBAClC5F,OAAA;0BACEqP,SAAS,EAAC,yDAAyD;0BACnExI,KAAK,EAAE;4BACLsK,UAAU,EAAE,0CAA0C;4BACtDtO,KAAK,EAAE,SAAS;4BAChBmE,SAAS,EAAE,+BAA+B;4BAC1CgL,MAAM,EAAE,mBAAmB;4BAC3Bf,QAAQ,EAAE;0BACZ,CAAE;0BAAA3B,QAAA,EACH;wBAED;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENnQ,OAAA;wBACEqP,SAAS,EAAG,6DAA4D9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAClC,KAAM,qDAAqD;wBACzJgE,KAAK,EAAE;0BACLsK,UAAU,EAAG,2BAA0B5D,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAACzB,WAAY,KAAIiK,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChB4O,UAAU,EAAE,6BAA6B;0BACzCzK,SAAS,EAAG,cAAauG,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,IAAG;0BAC9D+O,MAAM,EAAE;wBACV,CAAE;wBAAA1C,QAAA,GAED/B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC5B,IAAI,iBAAInF,KAAK,CAACuV,aAAa,CAAChG,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC5B,IAAI,EAAE;0BAC7EkM,SAAS,EAAE,SAAS;0BACpBxI,KAAK,EAAE;4BAAEhE,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACF7C,OAAA;0BAAM6G,KAAK,EAAE;4BAAEhE,KAAK,EAAE;0BAAU,CAAE;0BAAAyM,QAAA,EAAE/B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC3B;wBAAK;0BAAA4M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGNnQ,OAAA;wBAAKqP,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCtP,OAAA;0BAAKqP,SAAS,EAAC,oBAAoB;0BAACxI,KAAK,EAAE;4BACzChE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC/B,SAAS;4BACtCyO,UAAU,EAAG,eAAclE,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA8J,QAAA,GACC/B,aAAa,CAAC,CAAC,CAAC,CAAC1I,OAAO,CAACgO,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAENnQ,OAAA;0BAAKqP,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDtP,OAAA;4BAAKqP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BtP,OAAA;8BAAKqP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDtP,OAAA,CAAClB,OAAO;gCAACuQ,SAAS,EAAC,SAAS;gCAACxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;gCAAU;8BAAE;gCAAAiN,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClFnQ,OAAA;gCAAMqP,SAAS,EAAC,WAAW;gCAACxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;gCAAU,CAAE;gCAAAuM,QAAA,EAC3E/B,aAAa,CAAC,CAAC,CAAC,CAAC1F;8BAAiB;gCAAAmI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNnQ,OAAA;8BAAKqP,SAAS,EAAC,oBAAoB;8BAACxI,KAAK,EAAE;gCAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;8BAAU,CAAE;8BAAAuM,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACNnQ,OAAA;4BAAKqP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BtP,OAAA;8BAAKqP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDtP,OAAA,CAACpB,OAAO;gCAACyQ,SAAS,EAAC,SAAS;gCAACxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE;gCAAU;8BAAE;gCAAAmN,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5DnQ,OAAA;gCAAMqP,SAAS,EAAC,WAAW;gCAACxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;gCAAU,CAAE;gCAAAuM,QAAA,EAC3E/B,aAAa,CAAC,CAAC,CAAC,CAAChF;8BAAa;gCAAAyH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNnQ,OAAA;8BAAKqP,SAAS,EAAC,oBAAoB;8BAACxI,KAAK,EAAE;gCAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;8BAAU,CAAE;8BAAAuM,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA5MA,SAAQ5C,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI,EAAC;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6M1B,CACb,EAGA5C,aAAa,CAAC,CAAC,CAAC,iBACfvN,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBAETwD,GAAG,EAAEvS,IAAI,IAAI2K,MAAM,CAACoC,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAcgL,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI;kBACnC,kBAAgB,CAAE;kBAClB4J,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCb,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVe,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJO,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFrL,UAAU,EAAE;oBACV0J,KAAK,EAAE,GAAG;oBACVb,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DqC,OAAO,EAAE;sBAAExC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpClB,SAAS,EAAG,oBACVnB,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GAC7C,2DAA2D,GAC3D0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,yCAAyC,GACzC,EACP,EAAE;kBACHiB,KAAK,EAAE;oBACLmM,MAAM,EAAE,OAAO;oBACflM,SAAS,EAAEoH,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBAC3EJ,MAAM,EAAE0I,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,IAAIpE,UAAU,GACrD,0EAA0E,GAC1E0M,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GACjC,0EAA0E,GAC1E,MAAM;oBACZmB,UAAU,EAAE,eAAe;oBAC3BiL,MAAM,EAAE9D,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAC1EqN,YAAY,EAAE/E,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEuL,UAAU,EAAEjD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA0J,QAAA,gBAGFtP,OAAA;oBAAKqP,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtKtP,OAAA;sBAAMqP,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNnQ,OAAA;oBACEqP,SAAS,EAAG,8BAA6B9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAClC,KAAM,mBAAkB0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpI2D,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAauG,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC9B,WAAY,IAAG;sBAC9DiQ,KAAK,EAAE;oBACT,CAAE;oBAAA5D,QAAA,eAEFtP,OAAA;sBACEqP,SAAS,EAAG,GAAE9B,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAwM,QAAA,gBAEnHtP,OAAA;wBAAKqP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFnQ,OAAA;wBACEqP,SAAS,EAAC,sMAAsM;wBAChNxI,KAAK,EAAE;0BACLhE,KAAK,EAAE,SAAS;0BAChBmP,MAAM,EAAE;wBACV,CAAE;wBAAA1C,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnQ,OAAA;wBAAKqP,SAAS,EAAG,yBAAwB7O,IAAI,IAAI+M,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA0J,QAAA,eACnItP,OAAA;0BACEqP,SAAS,EAAC,wEAAwE;0BAClFxI,KAAK,EAAE;4BACLsK,UAAU,EAAE,SAAS;4BACrBnK,SAAS,EAAE,4BAA4B;4BACvCkM,KAAK,EAAE,MAAM;4BACbF,MAAM,EAAE;0BACV,CAAE;0BAAA1D,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAc,gBAC9BpI,OAAA;4BACEmT,GAAG,EAAE5F,aAAa,CAAC,CAAC,CAAC,CAACnF,cAAe;4BACrCgL,GAAG,EAAE7F,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAK;4BAC3BoH,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnQ,OAAA;4BACEqP,SAAS,EAAC,2EAA2E;4BACrFxI,KAAK,EAAE;8BACLsK,UAAU,EAAE,SAAS;8BACrBtO,KAAK,EAAE,SAAS;8BAChBoO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED/B,aAAa,CAAC,CAAC,CAAC,CAACtF,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNnQ,OAAA;wBACEqP,SAAS,EAAC,iCAAiC;wBAC3CxI,KAAK,EAAE;0BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAsM,QAAA,EAEjD/B,aAAa,CAAC,CAAC,CAAC,CAACtF;sBAAI;wBAAA+H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELnQ,OAAA;wBAAKqP,SAAS,EAAC,yBAAyB;wBAACxI,KAAK,EAAE;0BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;wBAAU,CAAE;wBAAAuM,QAAA,GACxF/B,aAAa,CAAC,CAAC,CAAC,CAAC1I,OAAO,CAACgO,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENnQ,OAAA;wBAAKqP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDtP,OAAA;0BAAM6G,KAAK,EAAE;4BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;0BAAU,CAAE;0BAAAuM,QAAA,GAAC,eACpD,EAAC/B,aAAa,CAAC,CAAC,CAAC,CAAC1F,iBAAiB;wBAAA;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPnQ,OAAA;0BAAM6G,KAAK,EAAE;4BAAEhE,KAAK,EAAE0K,aAAa,CAAC,CAAC,CAAC,CAACxI,IAAI,CAAChC;0BAAU,CAAE;0BAAAuM,QAAA,GAAC,eACpD,EAAC/B,aAAa,CAAC,CAAC,CAAC,CAAChF,aAAa;wBAAA;0BAAAyH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA1HA,SAAQ5C,aAAa,CAAC,CAAC,CAAC,CAAC3H,GAAI,EAAC;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2H1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLzO,iBAAiB,iBAChB1B,OAAA,CAAC5B,MAAM,CAACmR,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAG,CAAE;gBAC/Bb,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEc,CAAC,EAAE;gBAAE,CAAE;gBAC9BxJ,UAAU,EAAE;kBAAE0J,KAAK,EAAE,GAAG;kBAAEb,QAAQ,EAAE;gBAAI,CAAE;gBAC1CP,SAAS,EAAC,wJAAwJ;gBAAAC,QAAA,gBAElKtP,OAAA;kBAAKqP,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BtP,OAAA;oBAAIqP,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,eACnC,EAAC5N,iBAAiB,CAAC2C,MAAM,CAACb,UAAU,EAAC,GAAC,EAAC9B,iBAAiB,CAAC2C,MAAM,CAACjB,KAAK;kBAAA;oBAAA4M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACLnQ,OAAA;oBAAGqP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAC7B,EAAC5N,iBAAiB,CAAC+D,QAAQ,EAAC,MAAI,EAAC/D,iBAAiB,CAACmE,aAAa,EAAC,iBACzE;kBAAA;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENnQ,OAAA;kBAAKqP,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDtP,OAAA;oBAAKqP,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDtP,OAAA;sBAAKqP,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACtC5N,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,GAAG,CAAC,GACtC,GAAE/B,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,IAAI,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,OAAO,KAAI,CAAC,CAAE,KAAI,GACnE;oBAAY;sBAAAmL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CAAC,eACNnQ,OAAA;sBAAKqP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNnQ,OAAA;oBAAKqP,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACxDtP,OAAA;sBAAKqP,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAE5N,iBAAiB,CAACmE;oBAAa;sBAAAmK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFnQ,OAAA;sBAAKqP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNnQ,OAAA;oBAAKqP,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1DtP,OAAA;sBAAKqP,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GAAC,GAAC,EAAC5N,iBAAiB,CAAC+D,QAAQ;oBAAA;sBAAAuK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9EnQ,OAAA;sBAAKqP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGS,CACb,EAGAnO,cAAc,GACb;YACAJ,WAAW,CAACkE,MAAM,GAAG,CAAC,iBACpB9F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtCtP,OAAA;gBAAKqP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtP,OAAA,CAAC5B,MAAM,CAACoV,EAAE;kBACRnE,SAAS,EAAC,kDAAkD;kBAC5DxI,KAAK,EAAE;oBACLsK,UAAU,EAAG,0BAAyBzO,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY,KAAIZ,YAAY,CAACV,cAAc,CAAC,CAACe,SAAU,GAAE;oBAC5H4O,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCH,UAAU,EAAE,6BAA6B;oBACzCjM,MAAM,EAAG,wBAAuB9C,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY;kBAC3E,CAAE;kBACFoM,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE6I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,GAE7C5M,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU,EAAC,GAAC,EAACd,YAAY,CAACV,cAAc,CAAC,CAACoB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU;gBAAA;kBAAAwM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZnQ,OAAA;kBAAGqP,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1D1N,WAAW,CAACkE,MAAM,EAAC,2BACtB;gBAAA;kBAAAkK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJnQ,OAAA,CAAC5B,MAAM,CAACwS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM/O,iBAAiB,CAAC,IAAI,CAAE;kBACvCoN,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGNnQ,OAAA;gBAAKqP,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCtP,OAAA;kBAAKqP,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjC1N,WAAW,CAACmG,GAAG,CAAC,CAAC0L,QAAQ,EAAEzL,KAAK,KAAK;oBACpC,MAAM0L,UAAU,GAAG1L,KAAK,GAAG,CAAC;oBAC5B,MAAMkG,aAAa,GAAG1N,IAAI,IAAI2K,MAAM,CAACsI,QAAQ,CAAC7N,GAAG,CAAC,KAAKuF,MAAM,CAAC3K,IAAI,CAACoF,GAAG,CAAC;oBAEvE,oBACE5F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;sBAETwD,GAAG,EAAE7E,aAAa,GAAG1L,WAAW,GAAG,IAAK;sBACxC,gBAAciR,QAAQ,CAAC7N,GAAI;sBAC3B,kBAAgB8N,UAAW;sBAC3BlE,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEc,CAAC,EAAE;sBAAG,CAAE;sBAC/Bb,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEc,CAAC,EAAE;sBAAE,CAAE;sBAC9BxJ,UAAU,EAAE;wBAAE0J,KAAK,EAAE,GAAG,GAAGzI,KAAK,GAAG,IAAI;wBAAE4H,QAAQ,EAAE;sBAAI,CAAE;sBACzDiB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEP,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnClB,SAAS,EAAG;sBACV;sBACCrH,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GACxD,sEAAsE,GACtE0M,aAAa,GACX,yCAAyC,GACzC,EACP,EAAE;sBACHrH,KAAK,EAAE;wBACLC,SAAS,EAAIkB,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GACpE,2CAA2C,GAC3C0M,aAAa,GACX,aAAa,GACb,UAAU;wBAChB1I,MAAM,EAAIwC,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GACjE,mFAAmF,GACnF0M,aAAa,GACX,2EAA2E,GAC3E,MAAM;wBACZnH,UAAU,EAAE,eAAe;wBAC3BiL,MAAM,EAAIhK,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GACjE,oBAAoB,GACpB0M,aAAa,GACX,mBAAmB,GACnB,MAAM;wBACZ+E,YAAY,EAAG/E,aAAa,IAAKlG,KAAK,KAAK,CAAC,IAAIxG,UAAW,GAAI,MAAM,GAAG,KAAK;wBAC7EyQ,MAAM,EAAIjK,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GAAI,IAAI,GAAG,MAAM;wBACtFmS,eAAe,EAAI3L,KAAK,KAAK,CAAC,IAAIxG,UAAU,IAAM0M,aAAa,IAAI1M,UAAW,GAAI,SAAS,GAAG,aAAa;wBAC3G2P,UAAU,EAAEjD,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvHJ,QAAQ,EAAE,UAAU;wBACpBmE,MAAM,EAAE/D,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAoB,QAAA,eAGFtP,OAAA;wBACEqP,SAAS,EAAG,oBAAmBoE,QAAQ,CAAC1O,IAAI,CAAClC,KAAM,sBAAqB4Q,QAAQ,CAAC1O,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJ2D,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAayM,QAAQ,CAAC1O,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAqM,QAAA,eAEFtP,OAAA;0BACEqP,SAAS,EAAG,GAAEoE,QAAQ,CAAC1O,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH+D,KAAK,EAAE;4BACLmL,MAAM,EAAG,aAAYyB,QAAQ,CAAC1O,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAAgM,QAAA,gBAGFtP,OAAA;4BAAKqP,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjGnQ,OAAA;4BAAKqP,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDtP,OAAA;8BAAKqP,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvBtP,OAAA;gCACEqP,SAAS,EAAC,kJAAkJ;gCAC5JxI,KAAK,EAAE;kCACLhE,KAAK,EAAE,SAAS;kCAChB4O,UAAU,EAAE,6BAA6B;kCACzCO,MAAM,EAAE,iCAAiC;kCACzChL,SAAS,EAAE;gCACb,CAAE;gCAAAsI,QAAA,GACH,GACE,EAACoE,UAAU;8BAAA;gCAAA1D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGNnQ,OAAA;8BAAKqP,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBtP,OAAA;gCACEqP,SAAS,EAAC,gEAAgE;gCAC1ExI,KAAK,EAAE;kCACLsK,UAAU,EAAE,SAAS;kCACrBnK,SAAS,EAAE,4BAA4B;kCACvCkM,KAAK,EAAE,MAAM;kCACbF,MAAM,EAAE;gCACV,CAAE;gCAAA1D,QAAA,EAEDmE,QAAQ,CAACrL,cAAc,gBACtBpI,OAAA;kCACEmT,GAAG,EAAEM,QAAQ,CAACrL,cAAe;kCAC7BgL,GAAG,EAAEK,QAAQ,CAACxL,IAAK;kCACnBoH,SAAS,EAAC;gCAAyC;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACpD,CAAC,gBAEFnQ,OAAA;kCACEqP,SAAS,EAAC,2EAA2E;kCACrFxI,KAAK,EAAE;oCACLsK,UAAU,EAAE,SAAS;oCACrBtO,KAAK,EAAE,SAAS;oCAChBoO,QAAQ,EAAE;kCACZ,CAAE;kCAAA3B,QAAA,EAEDmE,QAAQ,CAACxL,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAELjC,aAAa,iBACZlO,OAAA;gCACEqP,SAAS,EAAC,8FAA8F;gCACxGxI,KAAK,EAAE;kCACLsK,UAAU,EAAE,0CAA0C;kCACtDnK,SAAS,EAAE;gCACb,CAAE;gCAAAsI,QAAA,eAEFtP,OAAA,CAACrB,MAAM;kCAAC0Q,SAAS,EAAC;gCAA2B;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNnQ,OAAA;4BAAKqP,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCtP,OAAA;8BAAKqP,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBtP,OAAA;gCAAKqP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3CtP,OAAA;kCACEqP,SAAS,EAAC,yCAAyC;kCACnDxI,KAAK,EAAE;oCACLhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAC/B,SAAS;oCAC9ByO,UAAU,EAAG,eAAcgC,QAAQ,CAAC1O,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAA8J,QAAA,EAEDmE,QAAQ,CAACxL;gCAAI;kCAAA+H,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJjC,aAAa,iBACZlO,OAAA;kCACEqP,SAAS,EAAC,yDAAyD;kCACnExI,KAAK,EAAE;oCACLsK,UAAU,EAAE,mDAAmD;oCAC/DtO,KAAK,EAAE,SAAS;oCAChBmE,SAAS,EAAE,8DAA8D;oCACzEgL,MAAM,EAAE,mBAAmB;oCAC3BP,UAAU,EAAE,6BAA6B;oCACzCR,QAAQ,EAAE,MAAM;oCAChBsB,UAAU,EAAE;kCACd,CAAE;kCAAAjD,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNnQ,OAAA;gCAAKqP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1CmE,QAAQ,CAAClM,KAAK,EAAC,gBAAS,EAACkM,QAAQ,CAACtL,KAAK;8BAAA;gCAAA6H,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNnQ,OAAA;4BAAKqP,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DtP,OAAA;8BACEqP,SAAS,EAAC,oCAAoC;8BAC9CxI,KAAK,EAAE;gCACLhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAC/B,SAAS;gCAC9ByO,UAAU,EAAG,eAAcgC,QAAQ,CAAC1O,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAA8J,QAAA,GAEDmE,QAAQ,CAAC5O,OAAO,CAACgO,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAA7C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGNnQ,OAAA;8BAAKqP,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9CtP,OAAA;gCACEqP,SAAS,EAAC,8CAA8C;gCACxDxI,KAAK,EAAE;kCACL8M,eAAe,EAAG,GAAEF,QAAQ,CAAC1O,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAChC;gCACvB,CAAE;gCAAAuM,QAAA,gBAEFtP,OAAA,CAAClB,OAAO;kCAACuQ,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BnQ,OAAA;kCAAMqP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEmE,QAAQ,CAAC5L;gCAAiB;kCAAAmI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACNnQ,OAAA;gCACEqP,SAAS,EAAC,8CAA8C;gCACxDxI,KAAK,EAAE;kCACL8M,eAAe,EAAE,WAAW;kCAC5B9Q,KAAK,EAAE;gCACT,CAAE;gCAAAyM,QAAA,gBAEFtP,OAAA,CAACpB,OAAO;kCAACyQ,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BnQ,OAAA;kCAAMqP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEmE,QAAQ,CAAClL;gCAAa;kCAAAyH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAtMDsD,QAAQ,CAAC7N,GAAG;sBAAAoK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuMP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACA5L,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,iBAClC9F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cACtC1D,EAAE,EAAC,yBAAyB;cAAA2D,QAAA,gBAG5BtP,OAAA;gBAAKqP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtP,OAAA,CAAC5B,MAAM,CAACoV,EAAE;kBACRnE,SAAS,EAAC,kDAAkD;kBAC5DxI,KAAK,EAAE;oBACLsK,UAAU,EAAE,mDAAmD;oBAC/DQ,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCH,UAAU,EAAE,6BAA6B;oBACzCjM,MAAM,EAAE;kBACV,CAAE;kBACFkK,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE6I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZnQ,OAAA;kBAAGqP,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNnQ,OAAA;gBAAKqP,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CrI,iBAAiB,CAAC,CAAC,CAACc,GAAG,CAAE9C,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMoI,UAAU,GAAGnL,YAAY,CAAC+C,SAAS,CAAC;kBAC1C,MAAM2O,QAAQ,GAAGvG,UAAU,CAAC9M,KAAK,CAACmL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACE1L,OAAA,CAAC5B,MAAM,CAACmR,GAAG;oBAETwD,GAAG,EAAGc,EAAE,IAAMzR,UAAU,CAACoE,OAAO,CAACvB,SAAS,CAAC,GAAG4O,EAAI;oBAClDrE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEc,CAAC,EAAE;oBAAG,CAAE;oBAC/Bb,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEc,CAAC,EAAE;oBAAE,CAAE;oBAC9BxJ,UAAU,EAAE;sBAAE0J,KAAK,EAAE,GAAG;sBAAEb,QAAQ,EAAE;oBAAI,CAAE;oBAC1CP,SAAS,EAAC,mGAAmG;oBAC7G1D,EAAE,EAAG,UAAS1G,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAqK,QAAA,gBAGvBtP,OAAA;sBAAKqP,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDtP,OAAA;wBAAKqP,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCtP,OAAA;0BACEqP,SAAS,EAAC,gEAAgE;0BAC1ExI,KAAK,EAAE;4BACLsK,UAAU,EAAG,2BAA0B9M,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrFiP,MAAM,EAAG,aAAY3N,MAAM,CAACf,WAAY,IAAG;4BAC3C0D,SAAS,EAAG,cAAa3C,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAqM,QAAA,EAEDjL,MAAM,CAACb;wBAAU;0BAAAwM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACNnQ,OAAA;0BAAAsP,QAAA,gBACEtP,OAAA;4BACEqP,SAAS,EAAC,0BAA0B;4BACpCxI,KAAK,EAAE;8BACLhE,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvByO,UAAU,EAAG,eAAcpN,MAAM,CAACpB,WAAY,EAAC;8BAC/CuC,MAAM,EAAE;4BACV,CAAE;4BAAA8J,QAAA,GAEDjL,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAA4M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLnQ,OAAA;4BAAGqP,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjCjC,UAAU,CAAC9M,KAAK,CAACuF,MAAM,EAAC,oBAAa,EAACzB,MAAM,CAAChB,WAAW;0BAAA;4BAAA2M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnQ,OAAA,CAAC5B,MAAM,CAACwS,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BE,OAAO,EAAEA,CAAA,KAAMjL,kBAAkB,CAACd,SAAS,CAAE;wBAC7CoK,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAACjC,UAAU,CAAC9M,KAAK,CAACuF,MAAM,EAAC,GACrC;sBAAA;wBAAAkK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGNnQ,OAAA;sBAAKqP,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClEsE,QAAQ,CAAC7L,GAAG,CAAC,CAAC0L,QAAQ,EAAEzL,KAAK,KAAK;wBACjC,MAAMkG,aAAa,GAAG1N,IAAI,IAAIiT,QAAQ,CAAC7N,GAAG,KAAKpF,IAAI,CAACoF,GAAG;wBACvD,MAAMkO,UAAU,GAAG9L,KAAK,GAAG,CAAC;wBAE5B,oBACEhI,OAAA,CAAC5B,MAAM,CAACmR,GAAG;0BAETC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAI,CAAE;0BACpCpB,OAAO,EAAE;4BAAED,OAAO,EAAE,CAAC;4BAAEqB,KAAK,EAAE;0BAAE,CAAE;0BAClC/J,UAAU,EAAE;4BAAE0J,KAAK,EAAE,GAAG,GAAGzI,KAAK,GAAG,GAAG;4BAAE4H,QAAQ,EAAE;0BAAI,CAAE;0BACxDiB,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAEP,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnClB,SAAS,EAAG,YACVnB,aAAa,IAAI1M,UAAU,GACvB,6CAA6C,GAC7C0M,aAAa,GACX,2BAA2B,GAC3B,EACP,EAAE;0BAAAoB,QAAA,eAEHtP,OAAA;4BACEqP,SAAS,EAAG,qBAAoBoE,QAAQ,CAAC1O,IAAI,CAAClC,KAAM,qBAAoB4Q,QAAQ,CAAC1O,IAAI,CAAC7B,IAAK,YAAY;4BACvG2D,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAayM,QAAQ,CAAC1O,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAAqM,QAAA,eAEFtP,OAAA;8BACEqP,SAAS,EAAG,GAAEoE,QAAQ,CAAC1O,IAAI,CAACjC,OAAQ,uEAAuE;8BAAAwM,QAAA,gBAE3GtP,OAAA;gCAAKqP,SAAS,EAAC;8BAAgE;gCAAAW,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtFnQ,OAAA;gCACEqP,SAAS,EAAC,mGAAmG;gCAC7GxI,KAAK,EAAE;kCACLsK,UAAU,EAAE9M,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChBmP,MAAM,EAAE;gCACV,CAAE;gCAAA1C,QAAA,GACH,GACE,EAACwE,UAAU;8BAAA;gCAAA9D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGNnQ,OAAA;gCAAKqP,SAAS,EAAG,yBACfnB,aAAa,IAAI1M,UAAU,GACvB,yCAAyC,GACzC0M,aAAa,GACX,wCAAwC,GACxC,EACP,EAAE;gCAAAoB,QAAA,gBACDtP,OAAA;kCACEqP,SAAS,EAAC,wEAAwE;kCAClFxI,KAAK,EAAE;oCACLsK,UAAU,EAAE,SAAS;oCACrBnK,SAAS,EAAE,4BAA4B;oCACvCkM,KAAK,EAAE,MAAM;oCACbF,MAAM,EAAE;kCACV,CAAE;kCAAA1D,QAAA,EAEDmE,QAAQ,CAACrL,cAAc,gBACtBpI,OAAA;oCACEmT,GAAG,EAAEM,QAAQ,CAACrL,cAAe;oCAC7BgL,GAAG,EAAEK,QAAQ,CAACxL,IAAK;oCACnBoH,SAAS,EAAC;kCAAyC;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpD,CAAC,gBAEFnQ,OAAA;oCACEqP,SAAS,EAAC,2EAA2E;oCACrFxI,KAAK,EAAE;sCACLsK,UAAU,EAAE,SAAS;sCACrBtO,KAAK,EAAE,SAAS;sCAChBoO,QAAQ,EAAE;oCACZ,CAAE;oCAAA3B,QAAA,EAEDmE,QAAQ,CAACxL,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kCAAC;oCAAAtD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACnC;gCACN;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,EACLjC,aAAa,iBACZlO,OAAA;kCACEqP,SAAS,EAAC,iGAAiG;kCAC3GxI,KAAK,EAAE;oCACLsK,UAAU,EAAE,0CAA0C;oCACtDnK,SAAS,EAAE;kCACb,CAAE;kCAAAsI,QAAA,eAEFtP,OAAA,CAACrB,MAAM;oCAAC0Q,SAAS,EAAC;kCAA2B;oCAAAW,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNnQ,OAAA;gCACEqP,SAAS,EAAC,iCAAiC;gCAC3CxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAC/B;gCAAU,CAAE;gCAAAsM,QAAA,GAEzCmE,QAAQ,CAACxL,IAAI,EACbiG,aAAa,iBACZlO,OAAA;kCAAMqP,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAELnQ,OAAA;gCAAKqP,SAAS,EAAC,yBAAyB;gCAACxI,KAAK,EAAE;kCAAEhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAChC;gCAAU,CAAE;gCAAAuM,QAAA,GAChFmE,QAAQ,CAAC5O,OAAO,CAACgO,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAA7C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAENnQ,OAAA;gCAAKqP,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChDtP,OAAA;kCAAM6G,KAAK,EAAE;oCAAEhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAChC;kCAAU,CAAE;kCAAAuM,QAAA,GAAC,eAC5C,EAACmE,QAAQ,CAAC5L,iBAAiB;gCAAA;kCAAAmI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACPnQ,OAAA;kCAAM6G,KAAK,EAAE;oCAAEhE,KAAK,EAAE4Q,QAAQ,CAAC1O,IAAI,CAAChC;kCAAU,CAAE;kCAAAuM,QAAA,GAAC,eAC5C,EAACmE,QAAQ,CAAClL,aAAa;gCAAA;kCAAAyH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GA7GDsD,QAAQ,CAAC7N,GAAG;0BAAAoK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA8GP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGL9C,UAAU,CAAC9M,KAAK,CAACuF,MAAM,GAAG,CAAC,iBAC1B9F,OAAA;sBAAKqP,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BtP,OAAA;wBAAGqP,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAACjC,UAAU,CAAC9M,KAAK,CAACuF,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAAkK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GAlLIlL,SAAS;oBAAA+K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmLJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMAzP,WAAW,CAACoF,MAAM,GAAG,CAAC,iBACrB9F,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJtP,OAAA;gBAAKqP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtP,OAAA;kBAAIqP,SAAS,EAAC,wBAAwB;kBAACxI,KAAK,EAAE;oBAC5ChE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCnQ,OAAA;kBAAKqP,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DtP,OAAA;oBAAKqP,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CtP,OAAA;sBAAKqP,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9C5O,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,SAAS,CAAC,CAACpD;oBAAM;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNnQ,OAAA;sBAAKqP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNnQ,OAAA;oBAAKqP,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CtP,OAAA;sBAAKqP,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C5O,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,eAAe,CAAC,CAACpD;oBAAM;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNnQ,OAAA;sBAAKqP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNnQ,OAAA;oBAAKqP,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CtP,OAAA;sBAAKqP,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/C5O,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,UAAU,KAAK,WAAW,CAAC,CAACpD;oBAAM;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNnQ,OAAA;sBAAKqP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnQ,OAAA;kBAAGqP,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGArP,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCd,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC/J,UAAU,EAAE;gBAAE0J,KAAK,EAAE,GAAG;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJtP,OAAA;gBAAKqP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtP,OAAA;kBAAIqP,SAAS,EAAC,yBAAyB;kBAACxI,KAAK,EAAE;oBAC7ChE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BnQ,OAAA;kBAAKqP,SAAS,EAAC,0BAA0B;kBAACxI,KAAK,EAAE;oBAC/ChE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,GAAC,GAAC,EAACxO,eAAe;gBAAA;kBAAAkP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BnQ,OAAA;kBAAGqP,SAAS,EAAC,SAAS;kBAACxI,KAAK,EAAE;oBAC5BhE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDnQ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAG,CAAE;cAC/Bb,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAC9BxJ,UAAU,EAAE;gBAAE0J,KAAK,EAAE,CAAC;gBAAEb,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BtP,OAAA;gBAAKqP,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3ItP,OAAA,CAAC5B,MAAM,CAACmR,GAAG;kBACTG,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC/J,UAAU,EAAE;oBAAE6I,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9CtP,OAAA,CAACb,QAAQ;oBAACkQ,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACbnQ,OAAA;kBAAIqP,SAAS,EAAC,yBAAyB;kBAACxI,KAAK,EAAE;oBAC7ChE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BnQ,OAAA;kBAAGqP,SAAS,EAAC,gCAAgC;kBAACxI,KAAK,EAAE;oBACnDhE,KAAK,EAAE,SAAS;oBAChB4O,UAAU,EAAE,6BAA6B;oBACzCc,UAAU,EAAE;kBACd,CAAE;kBAAAjD,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJnQ,OAAA,CAAC5B,MAAM,CAACwS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BzB,SAAS,EAAC,sJAAsJ;kBAChK2B,OAAO,EAAEA,CAAA,KAAM/D,MAAM,CAAC8G,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAA1E,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZzP,WAAW,CAACoF,MAAM,KAAK,CAAC,IAAI,CAAClF,OAAO,iBACnCZ,OAAA,CAAC5B,MAAM,CAACmR,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClCzB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BtP,OAAA,CAACvB,QAAQ;gBAAC4Q,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DnQ,OAAA;gBAAIqP,SAAS,EAAC,yBAAyB;gBAACxI,KAAK,EAAE;kBAC7ChE,KAAK,EAAE,SAAS;kBAChB4O,UAAU,EAAE,6BAA6B;kBACzCc,UAAU,EAAE;gBACd,CAAE;gBAAAjD,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBnQ,OAAA;gBAAGqP,SAAS,EAAC,SAAS;gBAACxI,KAAK,EAAE;kBAC5BhE,KAAK,EAAE,SAAS;kBAChB4O,UAAU,EAAE,6BAA6B;kBACzCc,UAAU,EAAE;gBACd,CAAE;gBAAAjD,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC/P,EAAA,CApuFID,kBAAkB;EAAA,QACJ7B,WAAW,EAEZC,WAAW;AAAA;AAAA0V,EAAA,GAHxB9T,kBAAkB;AAsuFxB,eAAeA,kBAAkB;AAAC,IAAA8T,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}