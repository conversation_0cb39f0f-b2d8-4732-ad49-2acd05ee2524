import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Row, Col, Statistic, Progress } from 'antd';
import {
  TbUsers,
  TbBook,
  TbFileText,
  TbChartBar,
  TbTrendingUp,
  TbTarget,
  TbAward,
  TbClock
} from 'react-icons/tb';
import { getAllUsers } from '../../../apicalls/users';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReports } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';

const AdminDashboard = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalExams: 0,
    totalReports: 0,
    averageScore: 0,
    completionRate: 0
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      dispatch(ShowLoading());

      // Fetch users data
      const usersResponse = await getAllUsers();
      const users = usersResponse.success ? usersResponse.users : [];

      // Fetch exams data
      const examsResponse = await getAllExams();
      const exams = examsResponse.success ? examsResponse.data : [];

      // Fetch reports data (with empty filters to get all reports)
      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });
      const reports = reportsResponse.success ? reportsResponse.data : [];

      // Calculate statistics
      const totalUsers = users.length;
      const activeUsers = users.filter(u => !u.isBlocked).length;
      const totalExams = exams.length;
      const totalReports = reports.length;
      
      // Calculate average score from reports
      const averageScore = reports.length > 0 
        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length
        : 0;
      
      // Calculate completion rate
      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;

      setStats({
        totalUsers,
        activeUsers,
        totalExams,
        totalReports,
        averageScore: Math.round(averageScore),
        completionRate: Math.round(completionRate)
      });

      dispatch(HideLoading());
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      dispatch(HideLoading());
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const quickActions = [
    {
      title: 'Manage Users',
      description: 'View and manage student accounts',
      icon: TbUsers,
      path: '/admin/users',
      color: 'bg-blue-500'
    },
    {
      title: 'Create Exam',
      description: 'Add new exams and questions',
      icon: TbFileText,
      path: '/admin/exams/add',
      color: 'bg-green-500'
    },
    {
      title: 'Study Materials',
      description: 'Upload learning resources',
      icon: TbBook,
      path: '/admin/study-materials',
      color: 'bg-purple-500'
    },
    {
      title: 'View Reports',
      description: 'Analyze student performance',
      icon: TbChartBar,
      path: '/admin/reports',
      color: 'bg-orange-500'
    }
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="p-6 max-w-7xl mx-auto"
    >
      {/* Welcome Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.name}! 👋
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your educational platform today.
        </p>
      </motion.div>

      {/* Statistics Cards */}
      <motion.div variants={itemVariants} className="mb-8">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card className="h-full">
              <Statistic
                title="Total Students"
                value={stats.totalUsers}
                prefix={<TbUsers className="text-blue-500" />}
                valueStyle={{ color: '#1890ff' }}
              />
              <div className="mt-2">
                <span className="text-green-500 text-sm">
                  {stats.activeUsers} active
                </span>
              </div>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card className="h-full">
              <Statistic
                title="Total Exams"
                value={stats.totalExams}
                prefix={<TbFileText className="text-green-500" />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card className="h-full">
              <Statistic
                title="Exam Attempts"
                value={stats.totalReports}
                prefix={<TbTarget className="text-orange-500" />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card className="h-full">
              <Statistic
                title="Average Score"
                value={stats.averageScore}
                suffix="%"
                prefix={<TbAward className="text-purple-500" />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>

      {/* Performance Overview */}
      <motion.div variants={itemVariants} className="mb-8">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="Student Engagement" className="h-full">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Active Students</span>
                    <span>{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</span>
                  </div>
                  <Progress 
                    percent={Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0} 
                    strokeColor="#52c41a"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Exam Completion Rate</span>
                    <span>{stats.completionRate}%</span>
                  </div>
                  <Progress 
                    percent={stats.completionRate} 
                    strokeColor="#1890ff"
                  />
                </div>
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card title="Quick Actions" className="h-full">
              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action, index) => {
                  const IconComponent = action.icon;
                  return (
                    <motion.div
                      key={action.title}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all"
                      onClick={() => window.location.href = action.path}
                    >
                      <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center mb-2`}>
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="font-medium text-sm mb-1">{action.title}</h4>
                      <p className="text-xs text-gray-500">{action.description}</p>
                    </motion.div>
                  );
                })}
              </div>
            </Card>
          </Col>
        </Row>
      </motion.div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <Card title="System Status" className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <TbTrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-medium">System Health</h4>
              <p className="text-green-600 text-sm">All systems operational</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <TbClock className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-medium">Last Updated</h4>
              <p className="text-gray-600 text-sm">Just now</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <TbChartBar className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-medium">Data Sync</h4>
              <p className="text-purple-600 text-sm">Synchronized</p>
            </div>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default AdminDashboard;
