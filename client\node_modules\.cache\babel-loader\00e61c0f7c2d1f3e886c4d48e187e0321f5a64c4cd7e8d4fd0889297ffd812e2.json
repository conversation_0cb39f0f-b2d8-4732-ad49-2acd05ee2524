{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with smooth scrolling - Fixed for single click\n  const handleLeagueSelect = leagueKey => {\n    console.log('🎯 League selected:', leagueKey);\n    console.log('Available league refs:', Object.keys(leagueRefs.current));\n    console.log('League groups:', Object.keys(leagueGroups));\n    if (selectedLeague === leagueKey) {\n      setSelectedLeague(null);\n      setShowLeagueView(false);\n\n      // Scroll to grouped leagues view\n      const groupedLeaguesElement = document.getElementById('grouped-leagues-section');\n      if (groupedLeaguesElement) {\n        groupedLeaguesElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start',\n          inline: 'nearest'\n        });\n      }\n    } else {\n      var _leagueGroups$leagueK;\n      setSelectedLeague(leagueKey);\n      setShowLeagueView(true);\n      setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n      // Wait for state update and DOM render, then scroll\n      setTimeout(() => {\n        // Try multiple methods to find the league element\n        let leagueElement = leagueRefs.current[leagueKey];\n        if (!leagueElement) {\n          // Fallback: try to find by ID\n          leagueElement = document.getElementById(`league-${leagueKey}`);\n        }\n        if (!leagueElement) {\n          // Fallback: try to find by data attribute\n          leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`);\n        }\n        if (leagueElement) {\n          console.log('✅ Scrolling to league element:', leagueElement);\n          leagueElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center',\n            inline: 'nearest'\n          });\n        } else {\n          console.log('❌ League element not found for:', leagueKey);\n          console.log('Available refs:', Object.keys(leagueRefs.current));\n\n          // Fallback: scroll to grouped leagues section\n          const groupedSection = document.getElementById('grouped-leagues-section');\n          if (groupedSection) {\n            console.log('📍 Scrolling to grouped leagues section as fallback');\n            groupedSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }\n      }, 100);\n    }\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Enhanced Find Me functionality - Navigate to user's league and highlight position\n  const handleFindMe = () => {\n    console.log('🎯 Enhanced Find Me clicked!');\n    console.log('User data:', user);\n    console.log('Current user league:', currentUserLeague);\n    console.log('League groups:', leagueGroups);\n\n    // First, let's find the user in the ranking data\n    const userInRanking = rankingData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id));\n    console.log('User found in ranking:', userInRanking);\n    if (!user) {\n      console.log('❌ No user data available');\n      return;\n    }\n\n    // If we have league groups, find user's league\n    if (Object.keys(leagueGroups).length > 0) {\n      let userLeague = null;\n      let userLeagueData = null;\n\n      // Find which league the user belongs to\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          userLeague = leagueKey;\n          userLeagueData = leagueData;\n          console.log('✅ Found user in league:', leagueKey, userInLeague);\n          break;\n        }\n      }\n      if (userLeague && userLeagueData) {\n        // Set to show user's league\n        setSelectedLeague(userLeague);\n        setShowLeagueView(true);\n        setLeagueUsers(userLeagueData.users);\n\n        // Show highlight effect\n        setShowFindMe(true);\n\n        // Clear the highlight after animation\n        setTimeout(() => {\n          setShowFindMe(false);\n        }, 4500);\n\n        // Scroll to user's league section first\n        setTimeout(() => {\n          const leagueElement = leagueRefs.current[userLeague];\n          if (leagueElement) {\n            console.log('✅ Scrolling to user league element:', leagueElement);\n            leagueElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n\n            // Then try to find the specific user element\n            setTimeout(() => {\n              const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n              if (userElement) {\n                console.log('✅ Found user element:', userElement);\n                userElement.scrollIntoView({\n                  behavior: 'smooth',\n                  block: 'center',\n                  inline: 'nearest'\n                });\n              }\n            }, 500);\n          } else {\n            console.log('❌ League element not found for:', userLeague);\n          }\n        }, 100);\n      } else {\n        console.log('❌ User not found in any league');\n      }\n    } else {\n      console.log('❌ No league groups available');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1012,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1010,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1064,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleFindMe,\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-gray-900 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#1f2937',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: currentUserRank ? `Find Me #${currentUserRank}` : (user === null || user === void 0 ? void 0 : user.role) === 'admin' || user !== null && user !== void 0 && user.isAdmin ? 'Admin View' : 'Find Me'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1082,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-3 ring-yellow-400 ring-opacity-80 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${league.borderColor}80`,\n                            boxShadow: isSelected ? `0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1137,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0\n                            },\n                            animate: {\n                              scale: 1\n                            },\n                            className: \"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center border-2 border-white\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-xs font-bold text-gray-900\",\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1144,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1139,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1147,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1121,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1164,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1160,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1116,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1197,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1217,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1220,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1241,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1261,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1280,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1281,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1329,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1391,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1396,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1407,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1339,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1244,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1232,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1427,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[1]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '280px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1498,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1497,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1512,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1515,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1537,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1543,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1527,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1526,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1558,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1565,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1570,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1573,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1569,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1509,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1473,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[0]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '320px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1609,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1608,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1618,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1613,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1635,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1638,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1661,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1667,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1651,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1687,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1680,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1650,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-black mb-2 truncate px-2\",\n                        style: {\n                          color: topPerformers[0].tier.nameColor,\n                          textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                          filter: 'drop-shadow(0 0 8px currentColor)'\n                        },\n                        children: topPerformers[0].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1693,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1718,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1704,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1723,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1734,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1735,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1733,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1739,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1732,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1743,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1744,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1742,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1748,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1741,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1731,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1722,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1629,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1622,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1584,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[2]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '280px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1784,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1783,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1798,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1801,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1823,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1829,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1813,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1812,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1844,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1851,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1856,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1859,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1855,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1795,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1788,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1759,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1470,\n                columnNumber: 19\n              }, this), currentUserLeague && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.3,\n                  duration: 0.8\n                },\n                className: \"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-white mb-2\",\n                    children: [\"Your League: \", currentUserLeague.league.leagueIcon, \" \", currentUserLeague.league.title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1878,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/80 text-sm\",\n                    children: [\"Rank #\", currentUserLeague.userRank, \" of \", currentUserLeague.totalInLeague, \" in your league\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1881,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1877,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold\",\n                      children: currentUserLeague.league.promotionXP > 0 ? `${currentUserLeague.league.promotionXP - ((user === null || user === void 0 ? void 0 : user.totalXP) || 0)} XP` : 'Max League'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1888,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"To Promotion\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1894,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1887,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold\",\n                      children: currentUserLeague.totalInLeague\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1897,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1898,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1896,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold\",\n                      children: [\"#\", currentUserLeague.userRank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1901,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Rank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1902,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1900,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1886,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1871,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1453,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1924,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1938,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1941,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1923,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && champion._id === user._id;\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1984,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1990,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1989,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full w-full h-full\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2015,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2021,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2005,\n                                columnNumber: 39\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2042,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2035,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2004,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1987,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2053,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-2 py-0.5 rounded-full text-xs font-bold\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                  },\n                                  children: \"YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2064,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2052,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2078,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2050,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2049,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2087,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2107,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2108,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2100,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2117,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2118,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2110,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2099,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2085,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1977,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1971,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1959,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1953,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1952,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1916,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2143,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2157,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2142,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2183,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2194,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2204,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2193,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2182,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2209,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2181,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2243,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2246,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                                  style: {\n                                    background: '#f0f0f0',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                    width: '40px',\n                                    height: '40px'\n                                  },\n                                  children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                    src: champion.profilePicture,\n                                    alt: champion.name,\n                                    className: \"object-cover rounded-full w-full h-full\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2269,\n                                    columnNumber: 47\n                                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                                    style: {\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    },\n                                    children: champion.name.charAt(0).toUpperCase()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2275,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2259,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2295,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2288,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2258,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2307,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2301,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2311,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2316,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2319,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2315,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2240,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2234,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2226,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2220,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2333,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2332,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2170,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2163,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2134,\n              columnNumber: 19\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2365,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2368,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2364,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2371,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2374,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2370,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2377,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2380,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2357,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2351,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2409,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2398,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2432,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2427,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2421,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2465,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2471,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1449,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1443,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1041,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1008,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"rf+YE4kGvgulp9gF3j/Y2RQ+Qv0=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "console", "log", "current", "groupedLeaguesElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "inline", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "querySelector", "groupedSection", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "userData", "totalQuizzesTaken", "transformedData", "map", "index", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "window", "addEventListener", "clearInterval", "removeEventListener", "topPerformers", "otherPerformers", "handleFindMe", "userInRanking", "leagueData", "userInLeague", "userElement", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "_", "i", "y", "x", "delay", "style", "left", "top", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "textShadow", "fontWeight", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "_leagueGroups$leagueK2", "isSelected", "userCount", "border", "boxShadow", "disabled", "rotateY", "span", "backgroundPosition", "backgroundSize", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "height", "width", "src", "alt", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "isCurrentUser", "backgroundColor", "topUsers", "el", "leagueRank", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with smooth scrolling - Fixed for single click\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n    console.log('Available league refs:', Object.keys(leagueRefs.current));\n    console.log('League groups:', Object.keys(leagueGroups));\n\n    if (selectedLeague === leagueKey) {\n      setSelectedLeague(null);\n      setShowLeagueView(false);\n\n      // Scroll to grouped leagues view\n      const groupedLeaguesElement = document.getElementById('grouped-leagues-section');\n      if (groupedLeaguesElement) {\n        groupedLeaguesElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start',\n          inline: 'nearest'\n        });\n      }\n    } else {\n      setSelectedLeague(leagueKey);\n      setShowLeagueView(true);\n      setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n      // Wait for state update and DOM render, then scroll\n      setTimeout(() => {\n        // Try multiple methods to find the league element\n        let leagueElement = leagueRefs.current[leagueKey];\n\n        if (!leagueElement) {\n          // Fallback: try to find by ID\n          leagueElement = document.getElementById(`league-${leagueKey}`);\n        }\n\n        if (!leagueElement) {\n          // Fallback: try to find by data attribute\n          leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`);\n        }\n\n        if (leagueElement) {\n          console.log('✅ Scrolling to league element:', leagueElement);\n          leagueElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center',\n            inline: 'nearest'\n          });\n        } else {\n          console.log('❌ League element not found for:', leagueKey);\n          console.log('Available refs:', Object.keys(leagueRefs.current));\n\n          // Fallback: scroll to grouped leagues section\n          const groupedSection = document.getElementById('grouped-leagues-section');\n          if (groupedSection) {\n            console.log('📍 Scrolling to grouped leagues section as fallback');\n            groupedSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start',\n              inline: 'nearest'\n            });\n          }\n        }\n      }, 100);\n    }\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Enhanced Find Me functionality - Navigate to user's league and highlight position\n  const handleFindMe = () => {\n    console.log('🎯 Enhanced Find Me clicked!');\n    console.log('User data:', user);\n    console.log('Current user league:', currentUserLeague);\n    console.log('League groups:', leagueGroups);\n\n    // First, let's find the user in the ranking data\n    const userInRanking = rankingData.find(item => String(item._id) === String(user?._id));\n    console.log('User found in ranking:', userInRanking);\n\n    if (!user) {\n      console.log('❌ No user data available');\n      return;\n    }\n\n    // If we have league groups, find user's league\n    if (Object.keys(leagueGroups).length > 0) {\n      let userLeague = null;\n      let userLeagueData = null;\n\n      // Find which league the user belongs to\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          userLeague = leagueKey;\n          userLeagueData = leagueData;\n          console.log('✅ Found user in league:', leagueKey, userInLeague);\n          break;\n        }\n      }\n\n      if (userLeague && userLeagueData) {\n        // Set to show user's league\n        setSelectedLeague(userLeague);\n        setShowLeagueView(true);\n        setLeagueUsers(userLeagueData.users);\n\n        // Show highlight effect\n        setShowFindMe(true);\n\n        // Clear the highlight after animation\n        setTimeout(() => {\n          setShowFindMe(false);\n        }, 4500);\n\n        // Scroll to user's league section first\n        setTimeout(() => {\n          const leagueElement = leagueRefs.current[userLeague];\n          if (leagueElement) {\n            console.log('✅ Scrolling to user league element:', leagueElement);\n            leagueElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n\n            // Then try to find the specific user element\n            setTimeout(() => {\n              const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n              if (userElement) {\n                console.log('✅ Found user element:', userElement);\n                userElement.scrollIntoView({\n                  behavior: 'smooth',\n                  block: 'center',\n                  inline: 'nearest'\n                });\n              }\n            }, 500);\n          } else {\n            console.log('❌ League element not found for:', userLeague);\n          }\n        }, 100);\n      } else {\n        console.log('❌ User not found in any league');\n      }\n    } else {\n      console.log('❌ No league groups available');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style jsx>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\">\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-gray-900 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#1f2937',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` :\n                     (user?.role === 'admin' || user?.isAdmin) ? 'Admin View' : 'Find Me'}\n                  </span>\n                </motion.button>\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-3 ring-yellow-400 ring-opacity-80 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${league.borderColor}80`,\n                              boxShadow: isSelected ? `0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0 }}\n                                animate={{ scale: 1 }}\n                                className=\"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center border-2 border-white\"\n                              >\n                                <span className=\"text-xs font-bold text-gray-900\">✓</span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && topPerformers[1]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '280px' }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && topPerformers[0]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '320px' }}\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <h3\n                              className=\"text-lg font-black mb-2 truncate px-2\"\n                              style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}\n                            >\n                              {topPerformers[0].name}\n                            </h3>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && topPerformers[2]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '280px' }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n                  {/* League Information Section */}\n                  {currentUserLeague && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 1.3, duration: 0.8 }}\n                      className=\"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\"\n                    >\n                      <div className=\"text-center mb-4\">\n                        <h3 className=\"text-xl font-bold text-white mb-2\">\n                          Your League: {currentUserLeague.league.leagueIcon} {currentUserLeague.league.title}\n                        </h3>\n                        <p className=\"text-white/80 text-sm\">\n                          Rank #{currentUserLeague.userRank} of {currentUserLeague.totalInLeague} in your league\n                        </p>\n                      </div>\n\n                      <div className=\"flex justify-center gap-4 text-sm\">\n                        <div className=\"bg-green-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-green-400 font-bold\">\n                            {currentUserLeague.league.promotionXP > 0 ?\n                              `${currentUserLeague.league.promotionXP - (user?.totalXP || 0)} XP` :\n                              'Max League'\n                            }\n                          </div>\n                          <div className=\"text-white/80 text-xs\">To Promotion</div>\n                        </div>\n                        <div className=\"bg-blue-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-blue-400 font-bold\">{currentUserLeague.totalInLeague}</div>\n                          <div className=\"text-white/80 text-xs\">League Members</div>\n                        </div>\n                        <div className=\"bg-purple-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-purple-400 font-bold\">#{currentUserLeague.userRank}</div>\n                          <div className=\"text-white/80 text-xs\">League Rank</div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && champion._id === user._id;\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                        style={{\n                                          background: '#f0f0f0',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      >\n                                        {champion.profilePicture ? (\n                                          <img\n                                            src={champion.profilePicture}\n                                            alt={champion.name}\n                                            className=\"object-cover rounded-full w-full h-full\"\n                                          />\n                                        ) : (\n                                          <div\n                                            className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                            style={{\n                                              background: '#25D366',\n                                              color: '#FFFFFF',\n                                              fontSize: '12px'\n                                            }}\n                                          >\n                                            {champion.name.charAt(0).toUpperCase()}\n                                          </div>\n                                        )}\n                                      </div>\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-2 py-0.5 rounded-full text-xs font-bold\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                            }}\n                                          >\n                                            YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''}`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                                          <div\n                                            className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                            style={{\n                                              background: '#f0f0f0',\n                                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          >\n                                            {champion.profilePicture ? (\n                                              <img\n                                                src={champion.profilePicture}\n                                                alt={champion.name}\n                                                className=\"object-cover rounded-full w-full h-full\"\n                                              />\n                                            ) : (\n                                              <div\n                                                className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                                style={{\n                                                  background: '#25D366',\n                                                  color: '#FFFFFF',\n                                                  fontSize: '16px'\n                                                }}\n                                              >\n                                                {champion.name.charAt(0).toUpperCase()}\n                                              </div>\n                                            )}\n                                          </div>\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAMmE,UAAU,GAAGjE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMkE,SAAS,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMmE,cAAc,GAAGnE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMoE,aAAa,GAAGpE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMqE,WAAW,GAAGrE,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMsE,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzE,OAAO;MACb0E,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE/D,SAAS;MACfgE,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEzD,QAAQ;MACd0D,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE1D,OAAO;MACb2D,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE1E,QAAQ;MACd2E,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAElE,OAAO;MACbmE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAExE,MAAM;MACZyE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEhE,QAAQ;MACdiE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIlE,KAAK,IAAK;IACpC,MAAMmE,OAAO,GAAG,CAAC,CAAC;IAElBnE,KAAK,CAACoE,OAAO,CAACnE,IAAI,IAAI;MACpB,MAAMoE,UAAU,GAAGT,aAAa,CAAC3D,IAAI,CAACqE,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClBrE,KAAK,EAAE;QACT,CAAC;MACH;MACAmE,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAAC9D,KAAK,CAACuE,IAAI,CAAC;QACpC,GAAGtE,IAAI;QACPuE,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAAC1E,KAAK,CAAC2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMjD,WAAW,GAAG0D,QAAQ,CAACE,MAAM,CAAChF,IAAI,IAAI;MAC1C,MAAM6D,MAAM,GAAGF,aAAa,CAAC3D,IAAI,CAACqE,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClBrE,KAAK,EAAEqB,WAAW;MAClB6D,QAAQ,EAAE7D,WAAW,CAAC8D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAEjE,WAAW,CAACkE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IACxCe,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEhB,SAAS,CAAC;IAC7Ce,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE1B,MAAM,CAACS,IAAI,CAAC5C,UAAU,CAAC8D,OAAO,CAAC,CAAC;IACtEF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE1B,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC;IAExD,IAAIF,cAAc,KAAKiD,SAAS,EAAE;MAChChD,iBAAiB,CAAC,IAAI,CAAC;MACvBF,iBAAiB,CAAC,KAAK,CAAC;;MAExB;MACA,MAAMoE,qBAAqB,GAAGC,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC;MAChF,IAAIF,qBAAqB,EAAE;QACzBA,qBAAqB,CAACG,cAAc,CAAC;UACnCC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MAAA,IAAAC,qBAAA;MACLzE,iBAAiB,CAACgD,SAAS,CAAC;MAC5BlD,iBAAiB,CAAC,IAAI,CAAC;MACvBF,cAAc,CAAC,EAAA6E,qBAAA,GAAAxE,YAAY,CAAC+C,SAAS,CAAC,cAAAyB,qBAAA,uBAAvBA,qBAAA,CAAyBnG,KAAK,KAAI,EAAE,CAAC;;MAEpD;MACAoG,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,aAAa,GAAGxE,UAAU,CAAC8D,OAAO,CAACjB,SAAS,CAAC;QAEjD,IAAI,CAAC2B,aAAa,EAAE;UAClB;UACAA,aAAa,GAAGR,QAAQ,CAACC,cAAc,CAAE,UAASpB,SAAU,EAAC,CAAC;QAChE;QAEA,IAAI,CAAC2B,aAAa,EAAE;UAClB;UACAA,aAAa,GAAGR,QAAQ,CAACS,aAAa,CAAE,iBAAgB5B,SAAU,IAAG,CAAC;QACxE;QAEA,IAAI2B,aAAa,EAAE;UACjBZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEW,aAAa,CAAC;UAC5DA,aAAa,CAACN,cAAc,CAAC;YAC3BC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,QAAQ;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ,CAAC,MAAM;UACLT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEhB,SAAS,CAAC;UACzDe,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE1B,MAAM,CAACS,IAAI,CAAC5C,UAAU,CAAC8D,OAAO,CAAC,CAAC;;UAE/D;UACA,MAAMY,cAAc,GAAGV,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC;UACzE,IAAIS,cAAc,EAAE;YAClBd,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;YAClEa,cAAc,CAACR,cAAc,CAAC;cAC5BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ;QACF;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACxB,MAAM,CAACnB,MAAM,IAAInC,YAAY,CAACmC,MAAM,CAAC,IAAInC,YAAY,CAACmC,MAAM,CAAC,CAAC9D,KAAK,CAACuF,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAED;EACA,MAAMmB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpG,UAAU,CAAC,IAAI,CAAC;MAChBmF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMiB,qBAAqB,GAAG,MAAMtH,gBAAgB,CAAC;UACnDuH,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAA5G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6G,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiB,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFxB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMwB,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAAChC,MAAM,CAACkC,QAAQ,IAC5DA,QAAQ,CAAC7C,OAAO,IAAI6C,QAAQ,CAAC7C,OAAO,GAAG,CAAC,IACxC6C,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACC,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,MAAM;YAC7DlC,GAAG,EAAE8B,QAAQ,CAAC9B,GAAG;YACjBmC,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACS,YAAY,IAAI,EAAE;YAC3CtD,OAAO,EAAE6C,QAAQ,CAAC7C,OAAO,IAAI,CAAC;YAC9B8C,iBAAiB,EAAED,QAAQ,CAACC,iBAAiB,IAAI,CAAC;YAClDS,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEZ,aAAa,CAACuD,QAAQ,CAAC7C,OAAO,IAAI,CAAC,CAAC;YAC1C4D,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEhB,QAAQ,CAACgB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElB,QAAQ,CAACkB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHrI,cAAc,CAACiH,eAAe,CAAC;;UAE/B;UACA,MAAMqB,aAAa,GAAGrB,eAAe,CAAClC,SAAS,CAACwD,IAAI,IAAIA,IAAI,CAACtD,GAAG,MAAKpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,EAAC;UAC/E7E,kBAAkB,CAACkI,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAIzI,IAAI,EAAE;YACR,MAAM2I,cAAc,GAAG9D,wBAAwB,CAACuC,eAAe,EAAEpH,IAAI,CAAC;YACtEmB,oBAAoB,CAACwH,cAAc,CAAC;YACpCtH,cAAc,CAAC,CAAAsH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE5I,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAM6I,OAAO,GAAG3E,kBAAkB,CAACmD,eAAe,CAAC;UACnDzF,eAAe,CAACiH,OAAO,CAAC;UAExBvI,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAOwI,OAAO,EAAE;QAChBrD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoD,OAAO,CAAC;MACpE;;MAEA;MACArD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqD,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqD,eAAe,GAAG,MAAM3J,uBAAuB,CAAC,CAAC;QACjDqG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsD,aAAa,GAAG,MAAMzJ,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAO0J,KAAK,EAAE;QACdxD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuD,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMzJ,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAO2J,SAAS,EAAE;UAClBzD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwD,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAChC,OAAO,IAAIgC,aAAa,CAAC/B,IAAI,EAAE;QAChExB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyD,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC/B,OAAO,IAAI+B,eAAe,CAAC9B,IAAI,EAAE;UACtE8B,eAAe,CAAC9B,IAAI,CAAC7C,OAAO,CAACuE,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAT,IAAI,CAAC1I,IAAI,cAAAmJ,UAAA,uBAATA,UAAA,CAAW/D,GAAG,KAAIsD,IAAI,CAACU,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVF,cAAc,CAACE,MAAM,CAAC,GAAGV,IAAI,CAACW,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAjC,eAAe,GAAG2B,aAAa,CAAC/B,IAAI,CACjChC,MAAM,CAACkC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAC9B,GAAG,IAAI8B,QAAQ,CAACoC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EjC,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,KAAK;UACxB;UACA,MAAMiC,WAAW,GAAGL,cAAc,CAAChC,QAAQ,CAAC9B,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIoE,YAAY,GAAGD,WAAW,CAACjE,MAAM,IAAI4B,QAAQ,CAACC,iBAAiB,IAAI,CAAC;UACxE,IAAIsC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIjC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAGtC,QAAQ,CAACU,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAACjE,MAAM,IAAI4B,QAAQ,CAAC8C,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAAC8C,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC1H,GAAG,CAAC,EAAE,EAAE0H,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIhD,QAAQ,CAAC8C,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BrC,YAAY,GAAGkC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACnC,YAAY,GAAG4B,YAAY,CAAC;YAEpDhE,OAAO,CAACC,GAAG,CAAE,0BAAyByB,QAAQ,CAACK,IAAK,KAAI0C,gBAAiB,aAAYG,gBAAiB,cAAalD,QAAQ,CAAC8C,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI3F,OAAO,GAAG6C,QAAQ,CAAC7C,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAI6C,QAAQ,CAAC8C,WAAW,EAAE;cACxB;cACA3F,OAAO,GAAGyF,IAAI,CAACK,KAAK,CAClBjD,QAAQ,CAAC8C,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAnF,OAAO,GAAGyF,IAAI,CAACK,KAAK,CACjBvC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGX,QAAQ,CAACW,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAACjE,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI+E,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACpF,OAAO,CAACyF,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZvC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACpC,UAAU,EAAEuC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFxC,aAAa,GAAGwC,UAAU;UAC5B,CAAC,MAAM,IAAInD,QAAQ,CAAC8C,WAAW,IAAI,CAACnC,aAAa,EAAE;YACjD;YACA,MAAMyC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAGtC,QAAQ,CAAC8C,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBzC,aAAa,GAAGiC,IAAI,CAAC1H,GAAG,CAACoH,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxExC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACrC,aAAa,EAAEiC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLlF,GAAG,EAAE8B,QAAQ,CAAC9B,GAAG;YACjBmC,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACQ,cAAc,IAAI,EAAE;YAC7CrD,OAAO,EAAEA,OAAO;YAChB8C,iBAAiB,EAAEqC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACf/C,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5B4D,UAAU,EAAE,IAAI;YAChB;YACAsC,cAAc,EAAErD,QAAQ,CAAC8C,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAACjE,MAAM,GAAG,CAAC;YAClCkD,UAAU,EAAEe,WAAW,CAACjE,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG4B,QAAQ,CAAC8C,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA5C,eAAe,CAAC1C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACA+C,eAAe,CAACjD,OAAO,CAAC,CAACnE,IAAI,EAAEsH,KAAK,KAAK;UACvCtH,IAAI,CAACgI,IAAI,GAAGV,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFnH,cAAc,CAACiH,eAAe,CAAC;;QAE/B;QACA,IAAInC,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIjF,IAAI,EAAE;UACR;UACAiF,QAAQ,GAAGmC,eAAe,CAAClC,SAAS,CAACwD,IAAI,IAAIA,IAAI,CAACtD,GAAG,KAAKpF,IAAI,CAACoF,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGmC,eAAe,CAAClC,SAAS,CAACwD,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACtD,GAAG,CAAC,KAAKqF,MAAM,CAACzK,IAAI,CAACoF,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIjF,IAAI,CAACuH,IAAI,EAAE;YAChCtC,QAAQ,GAAGmC,eAAe,CAAClC,SAAS,CAACwD,IAAI,IAAIA,IAAI,CAACnB,IAAI,KAAKvH,IAAI,CAACuH,IAAI,CAAC;UACvE;QACF;QAEAhH,kBAAkB,CAAC0E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIjF,IAAI,EAAE;UACR,MAAM2I,cAAc,GAAG9D,wBAAwB,CAACuC,eAAe,EAAEpH,IAAI,CAAC;UACtEmB,oBAAoB,CAACwH,cAAc,CAAC;UACpCtH,cAAc,CAAC,CAAAsH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE5I,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAM6I,OAAO,GAAG3E,kBAAkB,CAACmD,eAAe,CAAC;QACnDzF,eAAe,CAACiH,OAAO,CAAC;;QAExB;QACApD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CV,WAAW,EAAE/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuH,IAAI;UACvB6B,MAAM,EAAEpJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG;UACjBsF,UAAU,EAAE,QAAO1K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG;UAC5BuF,OAAO,EAAE,CAAA3K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsJ,IAAI,MAAK,OAAO,KAAItJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2K,OAAO;UAChDC,MAAM,EAAE5K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,OAAO;UACrBoE,aAAa,EAAExD,QAAQ;UACvB4F,gBAAgB,EAAE5F,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrD6F,gBAAgB,EAAE1D,eAAe,CAAC9B,MAAM;UACxCyF,eAAe,EAAE3D,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAAClC,CAAC,KAAK;YAAE8F,EAAE,EAAE9F,CAAC,CAACC,GAAG;YAAE8F,IAAI,EAAE,OAAO/F,CAAC,CAACC,GAAG;YAAEmC,IAAI,EAAEpC,CAAC,CAACoC;UAAK,CAAC,CAAC,CAAC;UACxG4D,UAAU,EAAE/D,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACtD,GAAG,MAAKpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,EAAC;UAChEiG,WAAW,EAAEjE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACtD,GAAG,CAAC,KAAKqF,MAAM,CAACzK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,CAAC,CAAC;UACjFkG,SAAS,EAAElE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACnB,IAAI,MAAKvH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuH,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMgE,WAAW,GAAG;UAClBlC,OAAO,EAAEjC,eAAe,CAACpC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,SAAS,CAAC,CAAClD,MAAM;UACvEkG,aAAa,EAAEpE,eAAe,CAACpC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,eAAe,CAAC,CAAClD,MAAM;UACnFmG,SAAS,EAAErE,eAAe,CAACpC,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,WAAW,CAAC,CAAClD;QACvE,CAAC;QAEDE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2B,eAAe,CAAC9B,MAAM,EAAE,gBAAgB,CAAC;QACxFE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8F,WAAW,CAAC;QAC5C/F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAAClC,CAAC,KAAK;UACvEoC,IAAI,EAAEpC,CAAC,CAACoC,IAAI;UACZ3D,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACbqH,OAAO,EAAEvG,CAAC,CAACgC,iBAAiB;UAC5BwE,GAAG,EAAExG,CAAC,CAACyC,YAAY;UACnBgE,MAAM,EAAEzG,CAAC,CAACqD;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLhD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCtF,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvC,OAAO,CAAC6N,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhL,OAAO,CAACgL,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR3I,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACd+I,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMqF,WAAW,GAAG7J,kBAAkB,CAAC6H,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG9J,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FvE,oBAAoB,CAAC+K,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCpL,iBAAiB,CAACqL,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B3G,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DgB,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM2F,mBAAmB,GAAIC,KAAK,IAAK;MACrC7G,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE4G,KAAK,CAACC,MAAM,CAAC;MACnE;MACAnG,UAAU,CAAC,MAAM;QACfM,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAED8F,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,iBAAiB,CAAC;IACnDI,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXK,aAAa,CAACT,cAAc,CAAC;MAC7B;MACAO,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEP,iBAAiB,CAAC;MACtDI,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEN,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,aAAa,GAAGzM,WAAW,CAAC8K,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM4B,eAAe,GAAG1M,WAAW,CAAC8K,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBrH,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEzF,IAAI,CAAC;IAC/BwF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEvE,iBAAiB,CAAC;IACtDsE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE/D,YAAY,CAAC;;IAE3C;IACA,MAAMoL,aAAa,GAAG5M,WAAW,CAACkL,IAAI,CAAC1C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAACtD,GAAG,CAAC,KAAKqF,MAAM,CAACzK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,GAAG,CAAC,CAAC;IACtFI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqH,aAAa,CAAC;IAEpD,IAAI,CAAC9M,IAAI,EAAE;MACTwF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;;IAEA;IACA,IAAI1B,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,EAAE;MACxC,IAAIlB,UAAU,GAAG,IAAI;MACrB,IAAIuE,cAAc,GAAG,IAAI;;MAEzB;MACA,KAAK,MAAM,CAAClE,SAAS,EAAEsI,UAAU,CAAC,IAAIhJ,MAAM,CAACC,OAAO,CAACtC,YAAY,CAAC,EAAE;QAClE,MAAMsL,YAAY,GAAGD,UAAU,CAAChN,KAAK,CAACqL,IAAI,CAACjG,CAAC,IAAIsF,MAAM,CAACtF,CAAC,CAACC,GAAG,CAAC,KAAKqF,MAAM,CAACzK,IAAI,CAACoF,GAAG,CAAC,CAAC;QACnF,IAAI4H,YAAY,EAAE;UAChB5I,UAAU,GAAGK,SAAS;UACtBkE,cAAc,GAAGoE,UAAU;UAC3BvH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEhB,SAAS,EAAEuI,YAAY,CAAC;UAC/D;QACF;MACF;MAEA,IAAI5I,UAAU,IAAIuE,cAAc,EAAE;QAChC;QACAlH,iBAAiB,CAAC2C,UAAU,CAAC;QAC7B7C,iBAAiB,CAAC,IAAI,CAAC;QACvBF,cAAc,CAACsH,cAAc,CAAC5I,KAAK,CAAC;;QAEpC;QACAkB,aAAa,CAAC,IAAI,CAAC;;QAEnB;QACAkF,UAAU,CAAC,MAAM;UACflF,aAAa,CAAC,KAAK,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC;;QAER;QACAkF,UAAU,CAAC,MAAM;UACf,MAAMC,aAAa,GAAGxE,UAAU,CAAC8D,OAAO,CAACtB,UAAU,CAAC;UACpD,IAAIgC,aAAa,EAAE;YACjBZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEW,aAAa,CAAC;YACjEA,aAAa,CAACN,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;;YAEF;YACAE,UAAU,CAAC,MAAM;cACf,MAAM8G,WAAW,GAAGrH,QAAQ,CAACS,aAAa,CAAE,kBAAiBrG,IAAI,CAACoF,GAAI,IAAG,CAAC;cAC1E,IAAI6H,WAAW,EAAE;gBACfzH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwH,WAAW,CAAC;gBACjDA,WAAW,CAACnH,cAAc,CAAC;kBACzBC,QAAQ,EAAE,QAAQ;kBAClBC,KAAK,EAAE,QAAQ;kBACfC,MAAM,EAAE;gBACV,CAAC,CAAC;cACJ;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAErB,UAAU,CAAC;UAC5D;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC/C;IACF,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMyH,oBAAoB,GAAGA,CAACnF,kBAAkB,EAAEoF,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1E3H,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCsC,kBAAkB;MAClBoF,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAIvF,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC0F,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBtL,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL6K,IAAI,EAAE,SAAS;UACftL,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL6K,IAAI,EAAE,SAAS;QACftL,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAI1C,OAAO,IAAIF,WAAW,CAACoF,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE9F,OAAA;MAAKoO,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DX,SAAS,EAAC;QAAqF;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFnP,OAAA;UAAGoO,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEnP,OAAA,CAAAE,SAAA;IAAAmO,QAAA,gBACErO,OAAA;MAAOoP,GAAG;MAAAf,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVnP,OAAA;MAAKoO,SAAS,EAAC,kIAAkI;MAAAC,QAAA,gBAEjJrO,OAAA;QAAKoO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CrO,OAAA;UAAKoO,SAAS,EAAC;QAA2H;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJnP,OAAA;UAAKoO,SAAS,EAAC;QAAgJ;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtKnP,OAAA;UAAKoO,SAAS,EAAC;QAA6I;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnKnP,OAAA;UAAKoO,SAAS,EAAC;QAA8I;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGNnP,OAAA;QAAKoO,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGgB,KAAK,CAAC,EAAE,CAAC,CAAC,CAACxH,GAAG,CAAC,CAACyH,CAAC,EAAEC,CAAC,kBACvBvP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAEnF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCiC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFG,UAAU,EAAE;YACVC,QAAQ,EAAE,CAAC,GAAGtE,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BsC,MAAM,EAAEC,QAAQ;YAChBY,KAAK,EAAEpF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFoD,KAAK,EAAE;YACLC,IAAI,EAAG,GAAEtF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/BsD,GAAG,EAAG,GAAEvF,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGgD,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnP,OAAA;QAAKoO,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtErO,OAAA;YAAKoO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCrO,OAAA;cAAKoO,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnIrO,OAAA;gBAAKoO,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGrO,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMzP,QAAQ,CAAC,WAAW,CAAE;kBACrC2N,SAAS,EAAC,gNAAgN;kBAC1NuB,KAAK,EAAE;oBACLQ,QAAQ,EAAEpD,MAAM,CAACqD,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFrO,OAAA,CAACjB,MAAM;oBAACqP,SAAS,EAAC;kBAAuB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CnP,OAAA;oBAAAqO,QAAA,EAAM;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGhBnP,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE7C,YAAa;kBACtBe,SAAS,EAAC,qNAAqN;kBAC/NuB,KAAK,EAAE;oBACLU,UAAU,EAAE,0CAA0C;oBACtDxN,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,MAAM;oBAClBC,UAAU,EAAE,KAAK;oBACjBJ,QAAQ,EAAEpD,MAAM,CAACqD,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFrO,OAAA,CAACnB,QAAQ;oBAACuP,SAAS,EAAC;kBAAuB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CnP,OAAA;oBAAAqO,QAAA,EACGvN,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAC9C,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsJ,IAAI,MAAK,OAAO,IAAItJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2K,OAAO,GAAI,YAAY,GAAG;kBAAS;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAKhBnP,OAAA;kBAAKoO,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpIrO,OAAA,CAAC5B,MAAM,CAACoS,EAAE;oBACRpC,SAAS,EAAC,sCAAsC;oBAChDuB,KAAK,EAAE;sBACLU,UAAU,EAAE,mDAAmD;sBAC/DI,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCJ,UAAU,EAAE,6BAA6B;sBACzC9K,MAAM,EAAE;oBACV,CAAE;oBACFiJ,OAAO,EAAE;sBAAEuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjCrB,UAAU,EAAE;sBAAEC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAT,QAAA,EAC/C;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZnP,OAAA;oBAAKoO,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvEtH,iBAAiB,CAAC,CAAC,CAACc,GAAG,CAAE5C,SAAS,IAAK;sBAAA,IAAA0L,sBAAA;sBACtC,MAAMtM,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAM2L,UAAU,GAAG5O,cAAc,KAAKiD,SAAS;sBAC/C,MAAM4L,SAAS,GAAG,EAAAF,sBAAA,GAAAzO,YAAY,CAAC+C,SAAS,CAAC,cAAA0L,sBAAA,uBAAvBA,sBAAA,CAAyBpQ,KAAK,CAACuF,MAAM,KAAI,CAAC;sBAE5D,oBACE9F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;wBAETF,SAAS,EAAC,kCAAkC;wBAC5C2B,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAAA3B,QAAA,gBAE5BrO,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;0BACZC,UAAU,EAAE;4BAAEC,KAAK,EAAE,GAAG;4BAAER,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClCS,QAAQ,EAAE;4BAAED,KAAK,EAAE;0BAAK,CAAE;0BAC1BE,OAAO,EAAEA,CAAA,KAAMnK,kBAAkB,CAACd,SAAS,CAAE;0BAC7CmJ,SAAS,EAAG,+GACVwC,UAAU,GACN,mDAAmD,GACnD,kCACL,EAAE;0BACHjB,KAAK,EAAE;4BACLU,UAAU,EAAG,2BAA0BhM,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrF+N,MAAM,EAAG,aAAYzM,MAAM,CAACf,WAAY,IAAG;4BAC3CyN,SAAS,EAAEH,UAAU,GAAI,cAAavM,MAAM,CAACpB,WAAY,IAAG,GAAI,cAAaoB,MAAM,CAACpB,WAAY;0BAClG,CAAE;0BACFG,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAWyN,SAAU,SAAS;0BAAAxC,QAAA,gBAEnErO,OAAA;4BAAMoO,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAEhK,MAAM,CAACb;0BAAU;4BAAAwL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChEyB,UAAU,iBACT5Q,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;4BACTC,OAAO,EAAE;8BAAEyB,KAAK,EAAE;4BAAE,CAAE;4BACtBvB,OAAO,EAAE;8BAAEuB,KAAK,EAAE;4BAAE,CAAE;4BACtB5B,SAAS,EAAC,oHAAoH;4BAAAC,QAAA,eAE9HrO,OAAA;8BAAMoO,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,EAAC;4BAAC;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChD,CACb,eACDnP,OAAA;4BACEoO,SAAS,EAAC,2HAA2H;4BACrIuB,KAAK,EAAE;8BACLU,UAAU,EAAEhM,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChBsN,QAAQ,EAAE;4BACZ,CAAE;4BAAA9B,QAAA,EAEDwC;0BAAS;4BAAA7B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhBnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;0BACTF,SAAS,EAAC,aAAa;0BACvB2B,UAAU,EAAE;4BAAEC,KAAK,EAAE;0BAAK,CAAE;0BAAA3B,QAAA,eAE5BrO,OAAA;4BACEoO,SAAS,EAAC,mDAAmD;4BAC7DuB,KAAK,EAAE;8BACL9M,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBsN,UAAU,EAAG,eAAcjM,MAAM,CAACpB,WAAY,EAAC;8BAC/CoN,UAAU,EAAG,GAAEhM,MAAM,CAACf,WAAY,IAAG;8BACrCwN,MAAM,EAAG,aAAYzM,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAA+K,QAAA,EAEDhK,MAAM,CAACjB;0BAAK;4BAAA4L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA1DRlK,SAAS;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2DJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENnP,OAAA;oBAAGoO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGNnP,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEtB,MAAM,EAAE;kBAAI,CAAE;kBACzCuB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEjJ,gBAAiB;kBAC1B+J,QAAQ,EAAEpQ,OAAQ;kBAClBwN,SAAS,EAAC,qNAAqN;kBAC/NuB,KAAK,EAAE;oBACLQ,QAAQ,EAAEpD,MAAM,CAACqD,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA/B,QAAA,gBAEFrO,OAAA,CAAChB,SAAS;oBAACoP,SAAS,EAAG,yBAAwBxN,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAoO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFnP,OAAA;oBAAAqO,QAAA,EAAM;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAA3O,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsJ,IAAI,MAAK,OAAO,KAAItJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2K,OAAO,mBACvCnL,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CrO,OAAA;YAAKoO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCrO,OAAA;cAAKoO,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HrO,OAAA;gBAAKoO,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCrO,OAAA;kBAAKoO,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFrO,OAAA;oBAAMoO,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNnP,OAAA;kBAAAqO,QAAA,gBACErO,OAAA;oBAAIoO,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDnP,OAAA;oBAAGoO,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CX,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzCrO,OAAA;YAAKoO,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GrO,OAAA;cAAKoO,SAAS,EAAC;YAA6E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGnP,OAAA;cAAKoO,SAAS,EAAC;YAA+E;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGnP,OAAA;cAAKoO,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFrO,OAAA;gBAAKoO,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5CrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBACTG,OAAO,EAAE;oBACPuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBiB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFtC,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFX,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBrO,OAAA;oBAAIoO,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GrO,OAAA,CAAC5B,MAAM,CAAC8S,IAAI;sBACVzC,OAAO,EAAE;wBACP0C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACFxC,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFX,SAAS,EAAC,+HAA+H;sBACzIuB,KAAK,EAAE;wBACLyB,cAAc,EAAE,WAAW;wBAC3BX,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClClL,MAAM,EAAE;sBACV,CAAE;sBAAA6I,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdnP,OAAA;sBAAAgP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnP,OAAA,CAAC5B,MAAM,CAAC8S,IAAI;sBACVzC,OAAO,EAAE;wBACP6B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF3B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFY,KAAK,EAAE;wBACL9M,KAAK,EAAE,SAAS;wBAChB0N,UAAU,EAAE,KAAK;wBACjBD,UAAU,EAAE;sBACd,CAAE;sBAAAjC,QAAA,EACH;oBAED;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbnP,OAAA,CAAC5B,MAAM,CAACiT,CAAC;kBACP9C,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,8GAA8G;kBACxHuB,KAAK,EAAE;oBACL9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCD,UAAU,EAAE,0CAA0C;oBACtDI,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAArC,QAAA,EACH;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEwB,KAAK,EAAE;kBAAI,CAAE;kBACpCvB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEwB,KAAK,EAAE;kBAAE,CAAE;kBAClCrB,UAAU,EAAE;oBAAEe,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CR,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBrO,OAAA;oBAAGoO,SAAS,EAAC,6JAA6J;oBACvKuB,KAAK,EAAE;sBACLW,UAAU,EAAE,6BAA6B;sBACzCgB,SAAS,EAAE;oBACb,CAAE;oBAAAjD,QAAA,EACF/M;kBAAiB;oBAAA0N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAG,CAAE;kBAC/Bf,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE;kBAAE,CAAE;kBAC9Bb,UAAU,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBACxCR,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACElL,IAAI,EAAE5D,OAAO;oBACbgS,KAAK,EAAE7Q,WAAW,CAACoF,MAAM;oBACzB0L,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBpO,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE1E,QAAQ;oBACd8S,KAAK,EAAEpE,aAAa,CAACrH,MAAM;oBAC3B0L,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBpO,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEvE,OAAO;oBACb2S,KAAK,EAAE7Q,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC0C,aAAa,GAAG,CAAC,CAAC,CAACvC,MAAM;oBAC1D0L,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBpO,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAExE,MAAM;oBACZ4S,KAAK,EAAE7Q,WAAW,CAACwJ,MAAM,CAAC,CAACC,GAAG,EAAExE,CAAC,KAAKwE,GAAG,IAAIxE,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC8M,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBpO,WAAW,EAAE;kBACf,CAAC,CACF,CAACuE,GAAG,CAAC,CAAC+J,IAAI,EAAE9J,KAAK,kBAChB9H,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAI,CAAE;oBACpCvB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAE,CAAE;oBAClCrB,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,GAAG;sBAAE8G,QAAQ,EAAE;oBAAI,CAAE;oBACxDmB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAER,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCpB,SAAS,EAAG,qBAAoBwD,IAAI,CAACH,UAAW,8EAA8E;oBAC9H9B,KAAK,EAAE;sBACLmB,MAAM,EAAG,aAAYc,IAAI,CAACtO,WAAY,IAAG;sBACzCyN,SAAS,EAAG,cAAaa,IAAI,CAACtO,WAAY;oBAC5C,CAAE;oBAAA+K,QAAA,gBAEFrO,OAAA;sBAAKoO,SAAS,EAAC;oBAAgE;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFnP,OAAA,CAAC4R,IAAI,CAACzO,IAAI;sBACRiL,SAAS,EAAC,kDAAkD;sBAC5DuB,KAAK,EAAE;wBAAE9M,KAAK,EAAE+O,IAAI,CAACF,SAAS;wBAAElM,MAAM,EAAE;sBAAyC;oBAAE;sBAAAwJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFnP,OAAA;sBACEoO,SAAS,EAAC,0EAA0E;sBACpFuB,KAAK,EAAE;wBACL9M,KAAK,EAAE+O,IAAI,CAACF,SAAS;wBACrBpB,UAAU,EAAG,6BAA4B;wBACzC9K,MAAM,EAAE,oCAAoC;wBAC5C2K,QAAQ,EAAE;sBACZ,CAAE;sBAAA9B,QAAA,EAEDuD,IAAI,CAACL;oBAAK;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNnP,OAAA;sBACEoO,SAAS,EAAC,4CAA4C;sBACtDuB,KAAK,EAAE;wBACL9M,KAAK,EAAE,SAAS;wBAChByN,UAAU,EAAE,6BAA6B;wBACzCH,QAAQ,EAAE;sBACZ,CAAE;sBAAA9B,QAAA,EAEDuD,IAAI,CAACJ;oBAAK;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCDrH,KAAK;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZvO,OAAO,iBACNZ,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBC,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DX,SAAS,EAAC;UAA6E;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFnP,OAAA;YAAGoO,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACvO,OAAO,iBACPZ,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEe,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CR,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjErO,OAAA;YAAKoO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BlB,aAAa,CAACrH,MAAM,GAAG,CAAC,iBACvB9F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClCrB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBrO,OAAA;gBAAIoO,SAAS,EAAC,gGAAgG;gBAACuB,KAAK,EAAE;kBACpHU,UAAU,EAAE,mDAAmD;kBAC/DI,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCJ,UAAU,EAAE,6BAA6B;kBACzC9K,MAAM,EAAE;gBACV,CAAE;gBAAA6I,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLnP,OAAA;gBAAKoO,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/GlB,aAAa,CAAC,CAAC,CAAC,iBACfnN,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBAETuD,GAAG,EAAErR,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAc4K,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI;kBACnC,kBAAgB,CAAE;kBAClB2I,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEiB,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCf,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACViB,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJQ,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBiB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFtC,UAAU,EAAE;oBACVe,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACboB,KAAK,EAAE;sBAAEpB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DkC,OAAO,EAAE;sBAAErC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFgB,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAER,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCpB,SAAS,EAAG,oBAAmB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGpE,UAAU,IAAIhB,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACjM+J,KAAK,EAAE;oBAAEmC,MAAM,EAAE;kBAAQ,CAAE;kBAAAzD,QAAA,gBAG3BrO,OAAA;oBAAKoO,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnKrO,OAAA;sBAAMoO,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNnP,OAAA;oBACEoO,SAAS,EAAG,8BAA6BjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAClC,KAAM,mBAAkBsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyM,KAAK,EAAE;sBACLoB,SAAS,EAAG,cAAa5D,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,IAAG;sBAC9D8O,KAAK,EAAE;oBACT,CAAE;oBAAA1D,QAAA,eAEFrO,OAAA;sBACEoO,SAAS,EAAG,GAAEjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAuL,QAAA,gBAEnHrO,OAAA;wBAAKoO,SAAS,EAAC;sBAAgE;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFnP,OAAA;wBACEoO,SAAS,EAAC,oMAAoM;wBAC9MuB,KAAK,EAAE;0BACL9M,KAAK,EAAE,SAAS;0BAChBiO,MAAM,EAAE;wBACV,CAAE;wBAAAzC,QAAA,EACH;sBAED;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnP,OAAA;wBAAKoO,SAAS,EAAG,yBAAwB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAyI,QAAA,eACnIrO,OAAA;0BACEoO,SAAS,EAAC,wEAAwE;0BAClFuB,KAAK,EAAE;4BACLU,UAAU,EAAE,SAAS;4BACrBU,SAAS,EAAE,4BAA4B;4BACvCgB,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAzD,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAc,gBAC9BlI,OAAA;4BACEgS,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAe;4BACrC+J,GAAG,EAAE9E,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAK;4BAC3BqG,SAAS,EAAC;0BAAyC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnP,OAAA;4BACEoO,SAAS,EAAC,2EAA2E;4BACrFuB,KAAK,EAAE;8BACLU,UAAU,EAAE,SAAS;8BACrBxN,KAAK,EAAE,SAAS;8BAChBsN,QAAQ,EAAE;4BACZ,CAAE;4BAAA9B,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAI,CAACmK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNnP,OAAA;wBACEoO,SAAS,EAAC,iCAAiC;wBAC3CuB,KAAK,EAAE;0BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAqL,QAAA,EAEjDlB,aAAa,CAAC,CAAC,CAAC,CAACpF;sBAAI;wBAAAiH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELnP,OAAA;wBAAKoO,SAAS,EAAC,yBAAyB;wBAACuB,KAAK,EAAE;0BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;wBAAU,CAAE;wBAAAsL,QAAA,GACxFlB,aAAa,CAAC,CAAC,CAAC,CAACtI,OAAO,CAAC8M,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENnP,OAAA;wBAAKoO,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDrO,OAAA;0BAAM2P,KAAK,EAAE;4BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;0BAAU,CAAE;0BAAAsL,QAAA,GAAC,eACpD,EAAClB,aAAa,CAAC,CAAC,CAAC,CAACxF,iBAAiB;wBAAA;0BAAAqH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPnP,OAAA;0BAAM2P,KAAK,EAAE;4BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;0BAAU,CAAE;0BAAAsL,QAAA,GAAC,eACpD,EAAClB,aAAa,CAAC,CAAC,CAAC,CAAC9E,aAAa;wBAAA;0BAAA2G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxGA,UAAShC,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI,EAAC;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyG3B,CACb,EAGAhC,aAAa,CAAC,CAAC,CAAC,iBACfnN,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBAETuD,GAAG,EAAErR,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAc4K,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI;kBACnC,kBAAgB,CAAE;kBAClB2I,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,CAAC,GAAG;oBAAEQ,KAAK,EAAE;kBAAI,CAAE;kBAC7CvB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVgB,CAAC,EAAE,CAAC;oBACJQ,KAAK,EAAE,CAAC;oBACRiB,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxBzB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFb,UAAU,EAAE;oBACVe,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACbqC,OAAO,EAAE;sBAAErC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DS,CAAC,EAAE;sBAAEZ,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFgB,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAER,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCpB,SAAS,EAAG,yBAAwB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGpE,UAAU,IAAIhB,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACtM+J,KAAK,EAAE;oBAAEmC,MAAM,EAAE;kBAAQ,CAAE;kBAAAzD,QAAA,gBAG3BrO,OAAA;oBAAKoO,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzKrO,OAAA;sBAAMoO,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGNnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDb,UAAU,EAAE;sBAAEC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CV,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErErO,OAAA,CAACtB,OAAO;sBAAC0P,SAAS,EAAC;oBAA0C;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGbnP,OAAA;oBACEoO,SAAS,EAAG,8BAA6BjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAClC,KAAM,sBAAqBsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5JyM,KAAK,EAAE;sBACLoB,SAAS,EAAG,cAAa5D,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/F8O,KAAK,EAAE;oBACT,CAAE;oBAAA1D,QAAA,eAEFrO,OAAA;sBACEoO,SAAS,EAAG,GAAEjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH6M,KAAK,EAAE;wBACLU,UAAU,EAAG,GAAElD,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAuL,QAAA,gBAEFrO,OAAA;wBAAKoO,SAAS,EAAC;sBAA4E;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlGnP,OAAA;wBACEoO,SAAS,EAAC,wMAAwM;wBAClNuB,KAAK,EAAE;0BACL9M,KAAK,EAAE,SAAS;0BAChByN,UAAU,EAAE,6BAA6B;0BACzCQ,MAAM,EAAE;wBACV,CAAE;wBAAAzC,QAAA,EACH;sBAED;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnP,OAAA;wBAAKoO,SAAS,EAAG,yBAAwB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAyI,QAAA,gBACnIrO,OAAA;0BACEoO,SAAS,EAAC,wEAAwE;0BAClFuB,KAAK,EAAE;4BACLU,UAAU,EAAE,SAAS;4BACrBU,SAAS,EAAE,4BAA4B;4BACvCgB,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAzD,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAc,gBAC9BlI,OAAA;4BACEgS,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAe;4BACrC+J,GAAG,EAAE9E,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAK;4BAC3BqG,SAAS,EAAC;0BAAyC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnP,OAAA;4BACEoO,SAAS,EAAC,2EAA2E;4BACrFuB,KAAK,EAAE;8BACLU,UAAU,EAAE,SAAS;8BACrBxN,KAAK,EAAE,SAAS;8BAChBsN,QAAQ,EAAE;4BACZ,CAAE;4BAAA9B,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAI,CAACmK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACL3O,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,iBACxC5F,OAAA;0BACEoO,SAAS,EAAC,4DAA4D;0BACtEuB,KAAK,EAAE;4BACLU,UAAU,EAAE,0CAA0C;4BACtDU,SAAS,EAAE;0BACb,CAAE;0BAAA1C,QAAA,eAEFrO,OAAA,CAACrB,MAAM;4BAACyP,SAAS,EAAC;0BAAuB;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNnP,OAAA;wBACEoO,SAAS,EAAC,uCAAuC;wBACjDuB,KAAK,EAAE;0BACL9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC/B,SAAS;0BACtCsN,UAAU,EAAG,eAAcnD,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,EAAC;0BAC9DuC,MAAM,EAAE;wBACV,CAAE;wBAAA6I,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACpF;sBAAI;wBAAAiH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELnP,OAAA;wBACEoO,SAAS,EAAG,6DAA4DjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ8M,KAAK,EAAE;0BACLU,UAAU,EAAG,2BAA0BlD,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAACzB,WAAY,KAAI6J,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChByN,UAAU,EAAE,6BAA6B;0BACzCS,SAAS,EAAG,cAAa5D,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,IAAG;0BAC9D6N,MAAM,EAAE;wBACV,CAAE;wBAAAzC,QAAA,GAEDlB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC5B,IAAI,iBAAInF,KAAK,CAACoU,aAAa,CAACjF,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC5B,IAAI,EAAE;0BAC7EiL,SAAS,EAAE,SAAS;0BACpBuB,KAAK,EAAE;4BAAE9M,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACF7C,OAAA;0BAAM2P,KAAK,EAAE;4BAAE9M,KAAK,EAAE;0BAAU,CAAE;0BAAAwL,QAAA,EAAElB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC3B;wBAAK;0BAAA4L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGNnP,OAAA;wBAAKoO,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCrO,OAAA;0BAAKoO,SAAS,EAAC,oBAAoB;0BAACuB,KAAK,EAAE;4BACzC9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC/B,SAAS;4BACtCsN,UAAU,EAAG,eAAcnD,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA6I,QAAA,GACClB,aAAa,CAAC,CAAC,CAAC,CAACtI,OAAO,CAAC8M,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAENnP,OAAA;0BAAKoO,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDrO,OAAA;4BAAKoO,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BrO,OAAA;8BAAKoO,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDrO,OAAA,CAAClB,OAAO;gCAACsP,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;gCAAU;8BAAE;gCAAAiM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClFnP,OAAA;gCAAMoO,SAAS,EAAC,WAAW;gCAACuB,KAAK,EAAE;kCAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;gCAAU,CAAE;gCAAAsL,QAAA,EAC3ElB,aAAa,CAAC,CAAC,CAAC,CAACxF;8BAAiB;gCAAAqH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNnP,OAAA;8BAAKoO,SAAS,EAAC,oBAAoB;8BAACuB,KAAK,EAAE;gCAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;8BAAU,CAAE;8BAAAsL,QAAA,EAAC;4BAAO;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACNnP,OAAA;4BAAKoO,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BrO,OAAA;8BAAKoO,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDrO,OAAA,CAACpB,OAAO;gCAACwP,SAAS,EAAC,SAAS;gCAACuB,KAAK,EAAE;kCAAE9M,KAAK,EAAE;gCAAU;8BAAE;gCAAAmM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5DnP,OAAA;gCAAMoO,SAAS,EAAC,WAAW;gCAACuB,KAAK,EAAE;kCAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;gCAAU,CAAE;gCAAAsL,QAAA,EAC3ElB,aAAa,CAAC,CAAC,CAAC,CAAC9E;8BAAa;gCAAA2G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNnP,OAAA;8BAAKoO,SAAS,EAAC,oBAAoB;8BAACuB,KAAK,EAAE;gCAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;8BAAU,CAAE;8BAAAsL,QAAA,EAAC;4BAAM;8BAAAW,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxKA,SAAQhC,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI,EAAC;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyK1B,CACb,EAGAhC,aAAa,CAAC,CAAC,CAAC,iBACfnN,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBAETuD,GAAG,EAAErR,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAc4K,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI;kBACnC,kBAAgB,CAAE;kBAClB2I,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEiB,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCf,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACViB,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJQ,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBiB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFtC,UAAU,EAAE;oBACVe,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACboB,KAAK,EAAE;sBAAEpB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3DkC,OAAO,EAAE;sBAAErC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFgB,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAER,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCpB,SAAS,EAAG,oBAAmB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGpE,UAAU,IAAIhB,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACjM+J,KAAK,EAAE;oBAAEmC,MAAM,EAAE;kBAAQ,CAAE;kBAAAzD,QAAA,gBAG3BrO,OAAA;oBAAKoO,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtKrO,OAAA;sBAAMoO,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNnP,OAAA;oBACEoO,SAAS,EAAG,8BAA6BjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAClC,KAAM,mBAAkBsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyM,KAAK,EAAE;sBACLoB,SAAS,EAAG,cAAa5D,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC9B,WAAY,IAAG;sBAC9D8O,KAAK,EAAE;oBACT,CAAE;oBAAA1D,QAAA,eAEFrO,OAAA;sBACEoO,SAAS,EAAG,GAAEjB,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAuL,QAAA,gBAEnHrO,OAAA;wBAAKoO,SAAS,EAAC;sBAAgE;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFnP,OAAA;wBACEoO,SAAS,EAAC,sMAAsM;wBAChNuB,KAAK,EAAE;0BACL9M,KAAK,EAAE,SAAS;0BAChBiO,MAAM,EAAE;wBACV,CAAE;wBAAAzC,QAAA,EACH;sBAED;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNnP,OAAA;wBAAKoO,SAAS,EAAG,yBAAwB5N,IAAI,IAAI2M,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAG,KAAKpF,IAAI,CAACoF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAyI,QAAA,eACnIrO,OAAA;0BACEoO,SAAS,EAAC,wEAAwE;0BAClFuB,KAAK,EAAE;4BACLU,UAAU,EAAE,SAAS;4BACrBU,SAAS,EAAE,4BAA4B;4BACvCgB,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAAzD,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAc,gBAC9BlI,OAAA;4BACEgS,GAAG,EAAE7E,aAAa,CAAC,CAAC,CAAC,CAACjF,cAAe;4BACrC+J,GAAG,EAAE9E,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAK;4BAC3BqG,SAAS,EAAC;0BAAyC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEFnP,OAAA;4BACEoO,SAAS,EAAC,2EAA2E;4BACrFuB,KAAK,EAAE;8BACLU,UAAU,EAAE,SAAS;8BACrBxN,KAAK,EAAE,SAAS;8BAChBsN,QAAQ,EAAE;4BACZ,CAAE;4BAAA9B,QAAA,EAEDlB,aAAa,CAAC,CAAC,CAAC,CAACpF,IAAI,CAACmK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAAnD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNnP,OAAA;wBACEoO,SAAS,EAAC,iCAAiC;wBAC3CuB,KAAK,EAAE;0BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAC/B;wBAAU,CAAE;wBAAAqL,QAAA,EAEjDlB,aAAa,CAAC,CAAC,CAAC,CAACpF;sBAAI;wBAAAiH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELnP,OAAA;wBAAKoO,SAAS,EAAC,yBAAyB;wBAACuB,KAAK,EAAE;0BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;wBAAU,CAAE;wBAAAsL,QAAA,GACxFlB,aAAa,CAAC,CAAC,CAAC,CAACtI,OAAO,CAAC8M,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENnP,OAAA;wBAAKoO,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDrO,OAAA;0BAAM2P,KAAK,EAAE;4BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;0BAAU,CAAE;0BAAAsL,QAAA,GAAC,eACpD,EAAClB,aAAa,CAAC,CAAC,CAAC,CAACxF,iBAAiB;wBAAA;0BAAAqH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPnP,OAAA;0BAAM2P,KAAK,EAAE;4BAAE9M,KAAK,EAAEsK,aAAa,CAAC,CAAC,CAAC,CAACpI,IAAI,CAAChC;0BAAU,CAAE;0BAAAsL,QAAA,GAAC,eACpD,EAAClB,aAAa,CAAC,CAAC,CAAC,CAAC9E,aAAa;wBAAA;0BAAA2G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxGA,SAAQhC,aAAa,CAAC,CAAC,CAAC,CAACvH,GAAI,EAAC;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyG1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLzN,iBAAiB,iBAChB1B,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEgB,CAAC,EAAE;gBAAG,CAAE;gBAC/Bf,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEgB,CAAC,EAAE;gBAAE,CAAE;gBAC9Bb,UAAU,EAAE;kBAAEe,KAAK,EAAE,GAAG;kBAAEd,QAAQ,EAAE;gBAAI,CAAE;gBAC1CR,SAAS,EAAC,wJAAwJ;gBAAAC,QAAA,gBAElKrO,OAAA;kBAAKoO,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BrO,OAAA;oBAAIoO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,eACnC,EAAC3M,iBAAiB,CAAC2C,MAAM,CAACb,UAAU,EAAC,GAAC,EAAC9B,iBAAiB,CAAC2C,MAAM,CAACjB,KAAK;kBAAA;oBAAA4L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACLnP,OAAA;oBAAGoO,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAC7B,EAAC3M,iBAAiB,CAAC+D,QAAQ,EAAC,MAAI,EAAC/D,iBAAiB,CAACmE,aAAa,EAAC,iBACzE;kBAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENnP,OAAA;kBAAKoO,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDrO,OAAA;oBAAKoO,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDrO,OAAA;sBAAKoO,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACtC3M,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,GAAG,CAAC,GACtC,GAAE/B,iBAAiB,CAAC2C,MAAM,CAACZ,WAAW,IAAI,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,OAAO,KAAI,CAAC,CAAE,KAAI,GACnE;oBAAY;sBAAAmK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CAAC,eACNnP,OAAA;sBAAKoO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNnP,OAAA;oBAAKoO,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACxDrO,OAAA;sBAAKoO,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAE3M,iBAAiB,CAACmE;oBAAa;sBAAAmJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFnP,OAAA;sBAAKoO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNnP,OAAA;oBAAKoO,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1DrO,OAAA;sBAAKoO,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GAAC,GAAC,EAAC3M,iBAAiB,CAAC+D,QAAQ;oBAAA;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9EnP,OAAA;sBAAKoO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGS,CACb,EAGAnN,cAAc,GACb;YACAJ,WAAW,CAACkE,MAAM,GAAG,CAAC,iBACpB9F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtCrO,OAAA;gBAAKoO,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCrO,OAAA,CAAC5B,MAAM,CAACiU,EAAE;kBACRjE,SAAS,EAAC,kDAAkD;kBAC5DuB,KAAK,EAAE;oBACLU,UAAU,EAAG,0BAAyB3N,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY,KAAIZ,YAAY,CAACV,cAAc,CAAC,CAACe,SAAU,GAAE;oBAC5H0N,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCJ,UAAU,EAAE,6BAA6B;oBACzC9K,MAAM,EAAG,wBAAuB9C,YAAY,CAACV,cAAc,CAAC,CAACsB,WAAY;kBAC3E,CAAE;kBACFmL,OAAO,EAAE;oBAAEuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAT,QAAA,GAE7C3L,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU,EAAC,GAAC,EAACd,YAAY,CAACV,cAAc,CAAC,CAACoB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACV,cAAc,CAAC,CAACwB,UAAU;gBAAA;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZnP,OAAA;kBAAGoO,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1DzM,WAAW,CAACkE,MAAM,EAAC,2BACtB;gBAAA;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJnP,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMjO,iBAAiB,CAAC,IAAI,CAAE;kBACvCmM,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGNnP,OAAA;gBAAKoO,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCrO,OAAA;kBAAKoO,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjCzM,WAAW,CAACiG,GAAG,CAAC,CAACyK,QAAQ,EAAExK,KAAK,KAAK;oBACpC,MAAMyK,UAAU,GAAGzK,KAAK,GAAG,CAAC;oBAC5B,MAAM0K,aAAa,GAAGhS,IAAI,IAAI8R,QAAQ,CAAC1M,GAAG,KAAKpF,IAAI,CAACoF,GAAG;oBAEvD,oBACE5F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;sBAETuD,GAAG,EAAEW,aAAa,GAAGhQ,WAAW,GAAG,IAAK;sBACxC,gBAAc8P,QAAQ,CAAC1M,GAAI;sBAC3B,kBAAgB2M,UAAW;sBAC3BhE,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEgB,CAAC,EAAE;sBAAG,CAAE;sBAC/Bf,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEgB,CAAC,EAAE;sBAAE,CAAE;sBAC9Bb,UAAU,EAAE;wBAAEe,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,IAAI;wBAAE8G,QAAQ,EAAE;sBAAI,CAAE;sBACzDmB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAER,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCpB,SAAS,EAAG,+BAA8BoE,aAAa,GAAG,2BAA2B,GAAG,EAAG,IAAGhR,UAAU,IAAIgR,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;sBAAAnE,QAAA,eAGvJrO,OAAA;wBACEoO,SAAS,EAAG,oBAAmBkE,QAAQ,CAACvN,IAAI,CAAClC,KAAM,sBAAqByP,QAAQ,CAACvN,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJyM,KAAK,EAAE;0BACLoB,SAAS,EAAG,cAAauB,QAAQ,CAACvN,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAoL,QAAA,eAEFrO,OAAA;0BACEoO,SAAS,EAAG,GAAEkE,QAAQ,CAACvN,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH6M,KAAK,EAAE;4BACLmB,MAAM,EAAG,aAAYwB,QAAQ,CAACvN,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAA+K,QAAA,gBAGFrO,OAAA;4BAAKoO,SAAS,EAAC;0BAA2E;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjGnP,OAAA;4BAAKoO,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDrO,OAAA;8BAAKoO,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvBrO,OAAA;gCACEoO,SAAS,EAAC,kJAAkJ;gCAC5JuB,KAAK,EAAE;kCACL9M,KAAK,EAAE,SAAS;kCAChByN,UAAU,EAAE,6BAA6B;kCACzCQ,MAAM,EAAE,iCAAiC;kCACzCC,SAAS,EAAE;gCACb,CAAE;gCAAA1C,QAAA,GACH,GACE,EAACkE,UAAU;8BAAA;gCAAAvD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGNnP,OAAA;8BAAKoO,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBrO,OAAA;gCACEoO,SAAS,EAAC,gEAAgE;gCAC1EuB,KAAK,EAAE;kCACLU,UAAU,EAAE,SAAS;kCACrBU,SAAS,EAAE,4BAA4B;kCACvCgB,KAAK,EAAE,MAAM;kCACbD,MAAM,EAAE;gCACV,CAAE;gCAAAzD,QAAA,EAEDiE,QAAQ,CAACpK,cAAc,gBACtBlI,OAAA;kCACEgS,GAAG,EAAEM,QAAQ,CAACpK,cAAe;kCAC7B+J,GAAG,EAAEK,QAAQ,CAACvK,IAAK;kCACnBqG,SAAS,EAAC;gCAAyC;kCAAAY,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACpD,CAAC,gBAEFnP,OAAA;kCACEoO,SAAS,EAAC,2EAA2E;kCACrFuB,KAAK,EAAE;oCACLU,UAAU,EAAE,SAAS;oCACrBxN,KAAK,EAAE,SAAS;oCAChBsN,QAAQ,EAAE;kCACZ,CAAE;kCAAA9B,QAAA,EAEDiE,QAAQ,CAACvK,IAAI,CAACmK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAAnD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAELqD,aAAa,iBACZxS,OAAA;gCACEoO,SAAS,EAAC,8FAA8F;gCACxGuB,KAAK,EAAE;kCACLU,UAAU,EAAE,0CAA0C;kCACtDU,SAAS,EAAE;gCACb,CAAE;gCAAA1C,QAAA,eAEFrO,OAAA,CAACrB,MAAM;kCAACyP,SAAS,EAAC;gCAA2B;kCAAAY,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNnP,OAAA;4BAAKoO,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClCrO,OAAA;8BAAKoO,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBrO,OAAA;gCAAKoO,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3CrO,OAAA;kCACEoO,SAAS,EAAC,yCAAyC;kCACnDuB,KAAK,EAAE;oCACL9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAC/B,SAAS;oCAC9BsN,UAAU,EAAG,eAAcgC,QAAQ,CAACvN,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAA6I,QAAA,EAEDiE,QAAQ,CAACvK;gCAAI;kCAAAiH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJqD,aAAa,iBACZxS,OAAA;kCACEoO,SAAS,EAAC,4CAA4C;kCACtDuB,KAAK,EAAE;oCACLU,UAAU,EAAE,0CAA0C;oCACtDxN,KAAK,EAAE,SAAS;oCAChBkO,SAAS,EAAE;kCACb,CAAE;kCAAA1C,QAAA,EACH;gCAED;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNnP,OAAA;gCAAKoO,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1CiE,QAAQ,CAACjL,KAAK,EAAC,gBAAS,EAACiL,QAAQ,CAACrK,KAAK;8BAAA;gCAAA+G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNnP,OAAA;4BAAKoO,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DrO,OAAA;8BACEoO,SAAS,EAAC,oCAAoC;8BAC9CuB,KAAK,EAAE;gCACL9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAC/B,SAAS;gCAC9BsN,UAAU,EAAG,eAAcgC,QAAQ,CAACvN,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAA6I,QAAA,GAEDiE,QAAQ,CAACzN,OAAO,CAAC8M,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAA3C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGNnP,OAAA;8BAAKoO,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9CrO,OAAA;gCACEoO,SAAS,EAAC,8CAA8C;gCACxDuB,KAAK,EAAE;kCACL8C,eAAe,EAAG,GAAEH,QAAQ,CAACvN,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAChC;gCACvB,CAAE;gCAAAsL,QAAA,gBAEFrO,OAAA,CAAClB,OAAO;kCAACsP,SAAS,EAAC;gCAAS;kCAAAY,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BnP,OAAA;kCAAMoO,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEiE,QAAQ,CAAC3K;gCAAiB;kCAAAqH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACNnP,OAAA;gCACEoO,SAAS,EAAC,8CAA8C;gCACxDuB,KAAK,EAAE;kCACL8C,eAAe,EAAE,WAAW;kCAC5B5P,KAAK,EAAE;gCACT,CAAE;gCAAAwL,QAAA,gBAEFrO,OAAA,CAACpB,OAAO;kCAACwP,SAAS,EAAC;gCAAS;kCAAAY,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BnP,OAAA;kCAAMoO,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAEiE,QAAQ,CAACjK;gCAAa;kCAAA2G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAnKDmD,QAAQ,CAAC1M,GAAG;sBAAAoJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAoKP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACA5K,MAAM,CAACS,IAAI,CAAC9C,YAAY,CAAC,CAAC4D,MAAM,GAAG,CAAC,iBAClC9F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,4BAA4B;cACtC3C,EAAE,EAAC,yBAAyB;cAAA4C,QAAA,gBAG5BrO,OAAA;gBAAKoO,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCrO,OAAA,CAAC5B,MAAM,CAACiU,EAAE;kBACRjE,SAAS,EAAC,kDAAkD;kBAC5DuB,KAAK,EAAE;oBACLU,UAAU,EAAE,mDAAmD;oBAC/DI,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCJ,UAAU,EAAE,6BAA6B;oBACzC9K,MAAM,EAAE;kBACV,CAAE;kBACFiJ,OAAO,EAAE;oBAAEuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAT,QAAA,EAC/C;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZnP,OAAA;kBAAGoO,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNnP,OAAA;gBAAKoO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CtH,iBAAiB,CAAC,CAAC,CAACc,GAAG,CAAE5C,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMsI,UAAU,GAAGrL,YAAY,CAAC+C,SAAS,CAAC;kBAC1C,MAAMyN,QAAQ,GAAGnF,UAAU,CAAChN,KAAK,CAACiL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACExL,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;oBAETuD,GAAG,EAAGc,EAAE,IAAMvQ,UAAU,CAAC8D,OAAO,CAACjB,SAAS,CAAC,GAAG0N,EAAI;oBAClDpE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAG,CAAE;oBAC/Bf,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAC9Bb,UAAU,EAAE;sBAAEe,KAAK,EAAE,GAAG;sBAAEd,QAAQ,EAAE;oBAAI,CAAE;oBAC1CR,SAAS,EAAC,mGAAmG;oBAC7G3C,EAAE,EAAG,UAASxG,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAoJ,QAAA,gBAGvBrO,OAAA;sBAAKoO,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDrO,OAAA;wBAAKoO,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCrO,OAAA;0BACEoO,SAAS,EAAC,gEAAgE;0BAC1EuB,KAAK,EAAE;4BACLU,UAAU,EAAG,2BAA0BhM,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrF+N,MAAM,EAAG,aAAYzM,MAAM,CAACf,WAAY,IAAG;4BAC3CyN,SAAS,EAAG,cAAa1M,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAoL,QAAA,EAEDhK,MAAM,CAACb;wBAAU;0BAAAwL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACNnP,OAAA;0BAAAqO,QAAA,gBACErO,OAAA;4BACEoO,SAAS,EAAC,0BAA0B;4BACpCuB,KAAK,EAAE;8BACL9M,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvBsN,UAAU,EAAG,eAAcjM,MAAM,CAACpB,WAAY,EAAC;8BAC/CuC,MAAM,EAAE;4BACV,CAAE;4BAAA6I,QAAA,GAEDhK,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAA4L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLnP,OAAA;4BAAGoO,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjCd,UAAU,CAAChN,KAAK,CAACuF,MAAM,EAAC,oBAAa,EAACzB,MAAM,CAAChB,WAAW;0BAAA;4BAAA2L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnP,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1BE,OAAO,EAAEA,CAAA,KAAMnK,kBAAkB,CAACd,SAAS,CAAE;wBAC7CmJ,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAACd,UAAU,CAAChN,KAAK,CAACuF,MAAM,EAAC,GACrC;sBAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGNnP,OAAA;sBAAKoO,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClEqE,QAAQ,CAAC7K,GAAG,CAAC,CAACyK,QAAQ,EAAExK,KAAK,KAAK;wBACjC,MAAM0K,aAAa,GAAGhS,IAAI,IAAI8R,QAAQ,CAAC1M,GAAG,KAAKpF,IAAI,CAACoF,GAAG;wBACvD,MAAMgN,UAAU,GAAG9K,KAAK,GAAG,CAAC;wBAE5B,oBACE9H,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;0BAETC,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAEwB,KAAK,EAAE;0BAAI,CAAE;0BACpCvB,OAAO,EAAE;4BAAED,OAAO,EAAE,CAAC;4BAAEwB,KAAK,EAAE;0BAAE,CAAE;0BAClCrB,UAAU,EAAE;4BAAEe,KAAK,EAAE,GAAG,GAAG5H,KAAK,GAAG,GAAG;4BAAE8G,QAAQ,EAAE;0BAAI,CAAE;0BACxDmB,UAAU,EAAE;4BAAEC,KAAK,EAAE,IAAI;4BAAER,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnCpB,SAAS,EAAG,YAAWoE,aAAa,GAAG,2BAA2B,GAAG,EAAG,EAAE;0BAAAnE,QAAA,eAE1ErO,OAAA;4BACEoO,SAAS,EAAG,qBAAoBkE,QAAQ,CAACvN,IAAI,CAAClC,KAAM,qBAAoByP,QAAQ,CAACvN,IAAI,CAAC7B,IAAK,YAAY;4BACvGyM,KAAK,EAAE;8BACLoB,SAAS,EAAG,cAAauB,QAAQ,CAACvN,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAAoL,QAAA,eAEFrO,OAAA;8BACEoO,SAAS,EAAG,GAAEkE,QAAQ,CAACvN,IAAI,CAACjC,OAAQ,uEAAuE;8BAAAuL,QAAA,gBAE3GrO,OAAA;gCAAKoO,SAAS,EAAC;8BAAgE;gCAAAY,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtFnP,OAAA;gCACEoO,SAAS,EAAC,mGAAmG;gCAC7GuB,KAAK,EAAE;kCACLU,UAAU,EAAEhM,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChBiO,MAAM,EAAE;gCACV,CAAE;gCAAAzC,QAAA,GACH,GACE,EAACuE,UAAU;8BAAA;gCAAA5D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGNnP,OAAA;gCAAKoO,SAAS,EAAG,yBAAwBoE,aAAa,GAAG,wCAAwC,GAAG,EAAG,EAAE;gCAAAnE,QAAA,gBACvGrO,OAAA;kCACEoO,SAAS,EAAC,wEAAwE;kCAClFuB,KAAK,EAAE;oCACLU,UAAU,EAAE,SAAS;oCACrBU,SAAS,EAAE,4BAA4B;oCACvCgB,KAAK,EAAE,MAAM;oCACbD,MAAM,EAAE;kCACV,CAAE;kCAAAzD,QAAA,EAEDiE,QAAQ,CAACpK,cAAc,gBACtBlI,OAAA;oCACEgS,GAAG,EAAEM,QAAQ,CAACpK,cAAe;oCAC7B+J,GAAG,EAAEK,QAAQ,CAACvK,IAAK;oCACnBqG,SAAS,EAAC;kCAAyC;oCAAAY,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACpD,CAAC,gBAEFnP,OAAA;oCACEoO,SAAS,EAAC,2EAA2E;oCACrFuB,KAAK,EAAE;sCACLU,UAAU,EAAE,SAAS;sCACrBxN,KAAK,EAAE,SAAS;sCAChBsN,QAAQ,EAAE;oCACZ,CAAE;oCAAA9B,QAAA,EAEDiE,QAAQ,CAACvK,IAAI,CAACmK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kCAAC;oCAAAnD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACnC;gCACN;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,EACLqD,aAAa,iBACZxS,OAAA;kCACEoO,SAAS,EAAC,iGAAiG;kCAC3GuB,KAAK,EAAE;oCACLU,UAAU,EAAE,0CAA0C;oCACtDU,SAAS,EAAE;kCACb,CAAE;kCAAA1C,QAAA,eAEFrO,OAAA,CAACrB,MAAM;oCAACyP,SAAS,EAAC;kCAA2B;oCAAAY,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNnP,OAAA;gCACEoO,SAAS,EAAC,iCAAiC;gCAC3CuB,KAAK,EAAE;kCAAE9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAC/B;gCAAU,CAAE;gCAAAqL,QAAA,GAEzCiE,QAAQ,CAACvK,IAAI,EACbyK,aAAa,iBACZxS,OAAA;kCAAMoO,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAELnP,OAAA;gCAAKoO,SAAS,EAAC,yBAAyB;gCAACuB,KAAK,EAAE;kCAAE9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAChC;gCAAU,CAAE;gCAAAsL,QAAA,GAChFiE,QAAQ,CAACzN,OAAO,CAAC8M,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAA3C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAENnP,OAAA;gCAAKoO,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChDrO,OAAA;kCAAM2P,KAAK,EAAE;oCAAE9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAChC;kCAAU,CAAE;kCAAAsL,QAAA,GAAC,eAC5C,EAACiE,QAAQ,CAAC3K,iBAAiB;gCAAA;kCAAAqH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACPnP,OAAA;kCAAM2P,KAAK,EAAE;oCAAE9M,KAAK,EAAEyP,QAAQ,CAACvN,IAAI,CAAChC;kCAAU,CAAE;kCAAAsL,QAAA,GAAC,eAC5C,EAACiE,QAAQ,CAACjK,aAAa;gCAAA;kCAAA2G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GAjGDmD,QAAQ,CAAC1M,GAAG;0BAAAoJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAkGP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGL5B,UAAU,CAAChN,KAAK,CAACuF,MAAM,GAAG,CAAC,iBAC1B9F,OAAA;sBAAKoO,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BrO,OAAA;wBAAGoO,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAACd,UAAU,CAAChN,KAAK,CAACuF,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GAtKIlK,SAAS;oBAAA+J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuKJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMAzO,WAAW,CAACoF,MAAM,GAAG,CAAC,iBACrB9F,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJrO,OAAA;gBAAKoO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrO,OAAA;kBAAIoO,SAAS,EAAC,wBAAwB;kBAACuB,KAAK,EAAE;oBAC5C9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAA6B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCnP,OAAA;kBAAKoO,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DrO,OAAA;oBAAKoO,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CrO,OAAA;sBAAKoO,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9C3N,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,SAAS,CAAC,CAAClD;oBAAM;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNnP,OAAA;sBAAKoO,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNnP,OAAA;oBAAKoO,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5CrO,OAAA;sBAAKoO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C3N,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,eAAe,CAAC,CAAClD;oBAAM;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNnP,OAAA;sBAAKoO,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNnP,OAAA;oBAAKoO,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CrO,OAAA;sBAAKoO,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/C3N,WAAW,CAAC8E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACqD,UAAU,KAAK,WAAW,CAAC,CAAClD;oBAAM;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNnP,OAAA;sBAAKoO,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnP,OAAA;kBAAGoO,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGArO,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCd,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClCrB,UAAU,EAAE;gBAAEe,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CR,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJrO,OAAA;gBAAKoO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrO,OAAA;kBAAIoO,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BnP,OAAA;kBAAKoO,SAAS,EAAC,0BAA0B;kBAACuB,KAAK,EAAE;oBAC/C9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,GAAC,GAAC,EAACvN,eAAe;gBAAA;kBAAAkO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BnP,OAAA;kBAAGoO,SAAS,EAAC,SAAS;kBAACuB,KAAK,EAAE;oBAC5B9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGDnP,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEgB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEe,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCR,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BrO,OAAA;gBAAKoO,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IrO,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;kBACTG,OAAO,EAAE;oBAAEuB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjCrB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAT,QAAA,eAE9CrO,OAAA,CAACb,QAAQ;oBAACiP,SAAS,EAAC;kBAAwC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACbnP,OAAA;kBAAIoO,SAAS,EAAC,yBAAyB;kBAACuB,KAAK,EAAE;oBAC7C9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BnP,OAAA;kBAAGoO,SAAS,EAAC,gCAAgC;kBAACuB,KAAK,EAAE;oBACnD9M,KAAK,EAAE,SAAS;oBAChByN,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAAlC,QAAA,EAAC;gBAGH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJnP,OAAA,CAAC5B,MAAM,CAAC0R,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1B5B,SAAS,EAAC,sJAAsJ;kBAChK8B,OAAO,EAAEA,CAAA,KAAMnD,MAAM,CAAC8F,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAzE,QAAA,EACpD;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZzO,WAAW,CAACoF,MAAM,KAAK,CAAC,IAAI,CAAClF,OAAO,iBACnCZ,OAAA,CAAC5B,MAAM,CAACkQ,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAI,CAAE;cACpCvB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEwB,KAAK,EAAE;cAAE,CAAE;cAClC5B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BrO,OAAA,CAACvB,QAAQ;gBAAC2P,SAAS,EAAC;cAAsC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DnP,OAAA;gBAAIoO,SAAS,EAAC,yBAAyB;gBAACuB,KAAK,EAAE;kBAC7C9M,KAAK,EAAE,SAAS;kBAChByN,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAAgB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBnP,OAAA;gBAAGoO,SAAS,EAAC,SAAS;gBAACuB,KAAK,EAAE;kBAC5B9M,KAAK,EAAE,SAAS;kBAChByN,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAAlC,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC/O,EAAA,CA15EID,kBAAkB;EAAA,QACJ7B,WAAW,EAEZC,WAAW;AAAA;AAAAwU,EAAA,GAHxB5S,kBAAkB;AA45ExB,eAAeA,kBAAkB;AAAC,IAAA4S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}