{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const inspiringQuotes = [\"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\", \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\", \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\"];\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  const navigationItems = [{\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help from AI',\n    icon: FaRobot,\n    path: '/user/chat',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: 'Reports',\n    description: 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-orange-500 to-orange-600',\n    hoverColor: 'from-orange-600 to-orange-700'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Profile',\n    description: 'Manage your account',\n    icon: FaUser,\n    path: '/user/profile',\n    color: 'from-indigo-500 to-indigo-600',\n    hoverColor: 'from-indigo-600 to-indigo-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/user/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade your learning',\n    icon: FaCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600',\n    hoverColor: 'from-emerald-600 to-emerald-700'\n  }, {\n    title: 'About Us',\n    description: 'Learn about our mission',\n    icon: FaInfoCircle,\n    path: '/user/about-us',\n    color: 'from-cyan-500 to-cyan-600',\n    hoverColor: 'from-cyan-600 to-cyan-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-welcome relative overflow-hidden min-h-[200px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 pointer-events-none\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"absolute top-1/2 left-8 transform -translate-y-1/2\",\n              animate: {\n                rotateY: [0, 360],\n                rotateX: [0, 180, 360],\n                scale: [1, 1.2, 1],\n                y: [-10, 10, -10]\n              },\n              transition: {\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              style: {\n                perspective: '1000px',\n                transformStyle: 'preserve-3d'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  transformStyle: 'preserve-3d'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b, #d97706)',\n                    clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                    boxShadow: '0 0 30px rgba(251, 191, 36, 0.8), 0 0 60px rgba(251, 191, 36, 0.4)',\n                    filter: 'brightness(1.2)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #f59e0b, #d97706, #b45309)',\n                    clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                    transform: 'translateZ(-5px) scale(0.9)',\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #d97706, #b45309, #92400e)',\n                    clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                    transform: 'translateZ(-10px) scale(0.8)',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"absolute top-1/2 right-8 transform -translate-y-1/2\",\n              animate: {\n                rotateX: [0, 360],\n                rotateY: [0, 360],\n                rotateZ: [0, 180],\n                scale: [1, 1.1, 1],\n                y: [10, -10, 10]\n              },\n              transition: {\n                duration: 10,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              style: {\n                perspective: '1000px',\n                transformStyle: 'preserve-3d'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                style: {\n                  width: '50px',\n                  height: '50px',\n                  transformStyle: 'preserve-3d'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #ef4444, #dc2626, #b91c1c)',\n                    transform: 'translateZ(25px)',\n                    boxShadow: '0 0 20px rgba(239, 68, 68, 0.6)',\n                    border: '2px solid #7f1d1d'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #3b82f6, #2563eb, #1d4ed8)',\n                    transform: 'translateZ(-25px) rotateY(180deg)',\n                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.6)',\n                    border: '2px solid #1e3a8a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #10b981, #059669, #047857)',\n                    transform: 'rotateY(90deg) translateZ(25px)',\n                    boxShadow: '0 0 20px rgba(16, 185, 129, 0.6)',\n                    border: '2px solid #064e3b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #f59e0b, #d97706, #b45309)',\n                    transform: 'rotateY(-90deg) translateZ(25px)',\n                    boxShadow: '0 0 20px rgba(245, 158, 11, 0.6)',\n                    border: '2px solid #78350f'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8b5cf6, #7c3aed, #6d28d9)',\n                    transform: 'rotateX(90deg) translateZ(25px)',\n                    boxShadow: '0 0 20px rgba(139, 92, 246, 0.6)',\n                    border: '2px solid #4c1d95'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0\",\n                  style: {\n                    background: 'linear-gradient(45deg, #ec4899, #db2777, #be185d)',\n                    transform: 'rotateX(-90deg) translateZ(25px)',\n                    boxShadow: '0 0 20px rgba(236, 72, 153, 0.6)',\n                    border: '2px solid #831843'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 rounded-3xl\",\n            style: {\n              background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n              filter: 'blur(30px)'\n            },\n            animate: {\n              scale: [1, 1.1, 1],\n              opacity: [0.3, 0.6, 0.3],\n              rotate: [0, 2, -2, 0]\n            },\n            transition: {\n              duration: 8,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"relative z-10 text-center\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative inline-block mr-6\",\n              initial: {\n                x: -200,\n                opacity: 0,\n                rotateY: -90\n              },\n              animate: {\n                x: 0,\n                opacity: 1,\n                rotateY: 0\n              },\n              transition: {\n                duration: 1.8,\n                delay: 0.5,\n                ease: \"easeOut\"\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",\n                style: {\n                  background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                  letterSpacing: '-0.06em',\n                  textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  y: [0, -5, 0],\n                  textShadow: ['0 0 50px rgba(59, 130, 246, 0.4)', '0 0 80px rgba(59, 130, 246, 0.7)', '0 0 50px rgba(59, 130, 246, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  y: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  textShadow: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateZ: [0, 2, -2, 0],\n                  transition: {\n                    duration: 0.6\n                  }\n                },\n                children: \"Study\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative inline-block\",\n              initial: {\n                x: 200,\n                opacity: 0,\n                rotateY: 90\n              },\n              animate: {\n                x: 0,\n                opacity: 1,\n                rotateY: 0\n              },\n              transition: {\n                duration: 1.8,\n                delay: 1,\n                ease: \"easeOut\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",\n                style: {\n                  background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                  letterSpacing: '-0.06em',\n                  textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  y: [0, 5, 0],\n                  textShadow: ['0 0 50px rgba(16, 185, 129, 0.4)', '0 0 80px rgba(16, 185, 129, 0.7)', '0 0 50px rgba(16, 185, 129, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  },\n                  y: {\n                    duration: 3.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 0.5\n                  },\n                  textShadow: {\n                    duration: 4.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  }\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateZ: [0, -2, 2, 0],\n                  transition: {\n                    duration: 0.6\n                  }\n                },\n                children: \"Smarter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -top-8 -right-4 text-4xl text-green-500\",\n                animate: {\n                  rotate: [0, -360],\n                  scale: [1, 1.4, 1],\n                  opacity: [0.6, 1, 0.6]\n                },\n                transition: {\n                  duration: 7,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                style: {\n                  filter: 'drop-shadow(0 0 15px rgba(16, 185, 129, 0.6))'\n                },\n                children: /*#__PURE__*/_jsxDEV(FaRocket, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -bottom-6 -left-6 text-3xl text-green-400\",\n                animate: {\n                  y: [8, -8, 8],\n                  rotate: [0, 30, -30, 0],\n                  scale: [1, 1.3, 1],\n                  opacity: [0.7, 1, 0.7]\n                },\n                transition: {\n                  duration: 4.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1.5\n                },\n                style: {\n                  filter: 'drop-shadow(0 0 12px rgba(16, 185, 129, 0.5))'\n                },\n                children: /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-8 relative\",\n              initial: {\n                opacity: 0,\n                y: 50,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 1.5,\n                delay: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"text-3xl sm:text-4xl font-bold block\",\n                style: {\n                  background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  scale: [1, 1.02, 1],\n                  textShadow: ['0 0 30px rgba(245, 158, 11, 0.4)', '0 0 50px rgba(245, 158, 11, 0.7)', '0 0 30px rgba(245, 158, 11, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  scale: {\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  textShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                whileHover: {\n                  scale: 1.1,\n                  rotate: [0, 3, -3, 0],\n                  transition: {\n                    duration: 0.4\n                  }\n                },\n                children: [user === null || user === void 0 ? void 0 : user.name, \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -top-4 -right-8 text-2xl text-yellow-500\",\n                animate: {\n                  rotate: [0, 360],\n                  scale: [1, 1.5, 1],\n                  opacity: [0.6, 1, 0.6]\n                },\n                transition: {\n                  duration: 5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                style: {\n                  filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))'\n                },\n                children: /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -bottom-2 -left-8 text-xl text-orange-500\",\n                animate: {\n                  y: [-5, 5, -5],\n                  rotate: [0, -20, 20, 0],\n                  scale: [1, 1.3, 1],\n                  opacity: [0.7, 1, 0.7]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                style: {\n                  filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.5))'\n                },\n                children: /*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-6 relative\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                duration: 1,\n                delay: 2.5\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"h-2 mx-auto rounded-full relative overflow-hidden\",\n                style: {\n                  width: '90%',\n                  background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                  boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                },\n                animate: {\n                  boxShadow: ['0 0 30px rgba(59, 130, 246, 0.5)', '0 0 50px rgba(16, 185, 129, 0.7)', '0 0 40px rgba(245, 158, 11, 0.6)', '0 0 30px rgba(59, 130, 246, 0.5)']\n                },\n                transition: {\n                  duration: 6,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                    width: '40%'\n                  },\n                  animate: {\n                    x: ['-100%', '250%']\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: \"Ready to shine today? \\u2728 Choose your learning path below.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"yUKD1BuiW8zY+bONKPa0/Mswuw0=\", false, function () {\n  return [useNavigate, useSelector];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "currentQuote", "setCurrentQuote", "handleLogout", "localStorage", "removeItem", "success", "inspiringQuotes", "interval", "setInterval", "prev", "length", "clearInterval", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "rotateY", "rotateX", "scale", "repeat", "Infinity", "ease", "style", "perspective", "transformStyle", "width", "height", "background", "clipPath", "boxShadow", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transform", "rotateZ", "border", "rotate", "x", "delay", "span", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "backgroundSize", "fontFamily", "letterSpacing", "textShadow", "backgroundPosition", "whileHover", "name", "marginRight", "marginLeft", "fontSize", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - <PERSON>\",\n    \"The future belongs to those who believe in the beauty of their dreams. - <PERSON>\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help from AI',\n      icon: FaRobot,\n      path: '/user/chat',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-orange-500 to-orange-600',\n      hoverColor: 'from-orange-600 to-orange-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/user/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/user/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade your learning',\n      icon: FaCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600',\n      hoverColor: 'from-emerald-600 to-emerald-700'\n    },\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Dynamic Inspiring Study Smarter Animation */}\n          <div className=\"hub-welcome relative overflow-hidden min-h-[200px]\">\n            {/* 3D Glowing Elements */}\n            <div className=\"absolute inset-0 pointer-events-none\">\n              {/* 3D Glowing Star - Left Side */}\n              <motion.div\n                className=\"absolute top-1/2 left-8 transform -translate-y-1/2\"\n                animate={{\n                  rotateY: [0, 360],\n                  rotateX: [0, 180, 360],\n                  scale: [1, 1.2, 1],\n                  y: [-10, 10, -10]\n                }}\n                transition={{\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                style={{\n                  perspective: '1000px',\n                  transformStyle: 'preserve-3d'\n                }}\n              >\n                <div\n                  className=\"relative\"\n                  style={{\n                    width: '60px',\n                    height: '60px',\n                    transformStyle: 'preserve-3d'\n                  }}\n                >\n                  {/* Star Shape with 3D effect */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #fbbf24, #f59e0b, #d97706)',\n                      clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                      boxShadow: '0 0 30px rgba(251, 191, 36, 0.8), 0 0 60px rgba(251, 191, 36, 0.4)',\n                      filter: 'brightness(1.2)'\n                    }}\n                  />\n                  {/* 3D depth layers */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #f59e0b, #d97706, #b45309)',\n                      clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                      transform: 'translateZ(-5px) scale(0.9)',\n                      opacity: 0.7\n                    }}\n                  />\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #d97706, #b45309, #92400e)',\n                      clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',\n                      transform: 'translateZ(-10px) scale(0.8)',\n                      opacity: 0.5\n                    }}\n                  />\n                </div>\n              </motion.div>\n\n              {/* 3D Glowing Rubik's Cube - Right Side */}\n              <motion.div\n                className=\"absolute top-1/2 right-8 transform -translate-y-1/2\"\n                animate={{\n                  rotateX: [0, 360],\n                  rotateY: [0, 360],\n                  rotateZ: [0, 180],\n                  scale: [1, 1.1, 1],\n                  y: [10, -10, 10]\n                }}\n                transition={{\n                  duration: 10,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                style={{\n                  perspective: '1000px',\n                  transformStyle: 'preserve-3d'\n                }}\n              >\n                <div\n                  className=\"relative\"\n                  style={{\n                    width: '50px',\n                    height: '50px',\n                    transformStyle: 'preserve-3d'\n                  }}\n                >\n                  {/* Cube faces */}\n                  {/* Front face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #ef4444, #dc2626, #b91c1c)',\n                      transform: 'translateZ(25px)',\n                      boxShadow: '0 0 20px rgba(239, 68, 68, 0.6)',\n                      border: '2px solid #7f1d1d'\n                    }}\n                  />\n                  {/* Back face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #3b82f6, #2563eb, #1d4ed8)',\n                      transform: 'translateZ(-25px) rotateY(180deg)',\n                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.6)',\n                      border: '2px solid #1e3a8a'\n                    }}\n                  />\n                  {/* Right face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #10b981, #059669, #047857)',\n                      transform: 'rotateY(90deg) translateZ(25px)',\n                      boxShadow: '0 0 20px rgba(16, 185, 129, 0.6)',\n                      border: '2px solid #064e3b'\n                    }}\n                  />\n                  {/* Left face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #f59e0b, #d97706, #b45309)',\n                      transform: 'rotateY(-90deg) translateZ(25px)',\n                      boxShadow: '0 0 20px rgba(245, 158, 11, 0.6)',\n                      border: '2px solid #78350f'\n                    }}\n                  />\n                  {/* Top face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #8b5cf6, #7c3aed, #6d28d9)',\n                      transform: 'rotateX(90deg) translateZ(25px)',\n                      boxShadow: '0 0 20px rgba(139, 92, 246, 0.6)',\n                      border: '2px solid #4c1d95'\n                    }}\n                  />\n                  {/* Bottom face */}\n                  <div\n                    className=\"absolute inset-0\"\n                    style={{\n                      background: 'linear-gradient(45deg, #ec4899, #db2777, #be185d)',\n                      transform: 'rotateX(-90deg) translateZ(25px)',\n                      boxShadow: '0 0 20px rgba(236, 72, 153, 0.6)',\n                      border: '2px solid #831843'\n                    }}\n                  />\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Dynamic Background Waves */}\n            <motion.div\n              className=\"absolute inset-0 rounded-3xl\"\n              style={{\n                background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n                filter: 'blur(30px)'\n              }}\n              animate={{\n                scale: [1, 1.1, 1],\n                opacity: [0.3, 0.6, 0.3],\n                rotate: [0, 2, -2, 0]\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n\n            {/* Main Content */}\n            <motion.div\n              className=\"relative z-10 text-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 1.5 }}\n            >\n              {/* Study Text with Motion */}\n              <motion.div\n                className=\"relative inline-block mr-6\"\n                initial={{ x: -200, opacity: 0, rotateY: -90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 0.5,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, -5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(59, 130, 246, 0.4)',\n                      '0 0 80px rgba(59, 130, 246, 0.7)',\n                      '0 0 50px rgba(59, 130, 246, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 6, repeat: Infinity, ease: \"easeInOut\" },\n                    y: { duration: 3, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, 2, -2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Study\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Smarter Text with Motion */}\n              <motion.div\n                className=\"relative inline-block\"\n                initial={{ x: 200, opacity: 0, rotateY: 90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 1,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, 5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(16, 185, 129, 0.4)',\n                      '0 0 80px rgba(16, 185, 129, 0.7)',\n                      '0 0 50px rgba(16, 185, 129, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 1 },\n                    y: { duration: 3.5, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 },\n                    textShadow: { duration: 4.5, repeat: Infinity, ease: \"easeInOut\", delay: 1 }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, -2, 2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Smarter\n                </motion.span>\n\n                {/* Dynamic Smart Icons around \"Smarter\" */}\n                <motion.div\n                  className=\"absolute -top-8 -right-4 text-4xl text-green-500\"\n                  animate={{\n                    rotate: [0, -360],\n                    scale: [1, 1.4, 1],\n                    opacity: [0.6, 1, 0.6]\n                  }}\n                  transition={{\n                    duration: 7,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 0.5\n                  }}\n                  style={{\n                    filter: 'drop-shadow(0 0 15px rgba(16, 185, 129, 0.6))'\n                  }}\n                >\n                  <FaRocket />\n                </motion.div>\n\n                <motion.div\n                  className=\"absolute -bottom-6 -left-6 text-3xl text-green-400\"\n                  animate={{\n                    y: [8, -8, 8],\n                    rotate: [0, 30, -30, 0],\n                    scale: [1, 1.3, 1],\n                    opacity: [0.7, 1, 0.7]\n                  }}\n                  transition={{\n                    duration: 4.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1.5\n                  }}\n                  style={{\n                    filter: 'drop-shadow(0 0 12px rgba(16, 185, 129, 0.5))'\n                  }}\n                >\n                  <FaTrophy />\n                </motion.div>\n              </motion.div>\n\n              {/* User Name with Inspiring Animation */}\n              <motion.div\n                className=\"mt-8 relative\"\n                initial={{ opacity: 0, y: 50, scale: 0.8 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 1.5, delay: 2 }}\n              >\n                <motion.span\n                  className=\"text-3xl sm:text-4xl font-bold block\"\n                  style={{\n                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    scale: [1, 1.02, 1],\n                    textShadow: [\n                      '0 0 30px rgba(245, 158, 11, 0.4)',\n                      '0 0 50px rgba(245, 158, 11, 0.7)',\n                      '0 0 30px rgba(245, 158, 11, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                    scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 3, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    rotate: [0, 3, -3, 0],\n                    transition: { duration: 0.4 }\n                  }}\n                >\n                  {user?.name}!\n                </motion.span>\n\n                {/* Celebration Icons */}\n                <motion.div\n                  className=\"absolute -top-4 -right-8 text-2xl text-yellow-500\"\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [1, 1.5, 1],\n                    opacity: [0.6, 1, 0.6]\n                  }}\n                  transition={{\n                    duration: 5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  style={{\n                    filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))'\n                  }}\n                >\n                  <FaTrophy />\n                </motion.div>\n\n                <motion.div\n                  className=\"absolute -bottom-2 -left-8 text-xl text-orange-500\"\n                  animate={{\n                    y: [-5, 5, -5],\n                    rotate: [0, -20, 20, 0],\n                    scale: [1, 1.3, 1],\n                    opacity: [0.7, 1, 0.7]\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  }}\n                  style={{\n                    filter: 'drop-shadow(0 0 8px rgba(245, 158, 11, 0.5))'\n                  }}\n                >\n                  <FaStar />\n                </motion.div>\n              </motion.div>\n\n              {/* Dynamic Inspiring Underline */}\n              <motion.div\n                className=\"mt-6 relative\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 2.5 }}\n              >\n                <motion.div\n                  className=\"h-2 mx-auto rounded-full relative overflow-hidden\"\n                  style={{\n                    width: '90%',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                  }}\n                  animate={{\n                    boxShadow: [\n                      '0 0 30px rgba(59, 130, 246, 0.5)',\n                      '0 0 50px rgba(16, 185, 129, 0.7)',\n                      '0 0 40px rgba(245, 158, 11, 0.6)',\n                      '0 0 30px rgba(59, 130, 246, 0.5)'\n                    ]\n                  }}\n                  transition={{\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                >\n                  {/* Moving Light Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full\"\n                    style={{\n                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                      width: '40%'\n                    }}\n                    animate={{\n                      x: ['-100%', '250%']\n                    }}\n                    transition={{\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 3\n                    }}\n                  />\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                >\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA1B,OAAO,CAAC2B,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAR,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMS,eAAe,GAAG,CACtB,+FAA+F,EAC/F,4FAA4F,EAC5F,4GAA4G,EAC5G,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,CAC5B;EAEDhC,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCP,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEnC,gBAAgB;IACtBoC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAElC,MAAM;IACZmC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAExB,OAAO;IACbyB,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEjC,WAAW;IACjBkC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE3B,QAAQ;IACd4B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAEhC,MAAM;IACZiC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE/B,UAAU;IAChBgC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE9B,YAAY;IAClB+B,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,iCAAiC;IACxCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAE7B,YAAY;IAClB8B,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1B,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAG1B1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAKtB1B,OAAA;UAAKyB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAEjE1B,OAAA;YAAKyB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEnD1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,oDAAoD;cAC9DM,OAAO,EAAE;gBACPG,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;gBACjBC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;gBACtBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClBN,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;cAClB,CAAE;cACFE,UAAU,EAAE;gBACVC,QAAQ,EAAE,CAAC;gBACXI,MAAM,EAAEC,QAAQ;gBAChBC,IAAI,EAAE;cACR,CAAE;cACFC,KAAK,EAAE;gBACLC,WAAW,EAAE,QAAQ;gBACrBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,eAEF1B,OAAA;gBACEyB,SAAS,EAAC,UAAU;gBACpBe,KAAK,EAAE;kBACLG,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdF,cAAc,EAAE;gBAClB,CAAE;gBAAAhB,QAAA,gBAGF1B,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DC,QAAQ,EAAE,iGAAiG;oBAC3GC,SAAS,EAAE,oEAAoE;oBAC/EC,MAAM,EAAE;kBACV;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DC,QAAQ,EAAE,iGAAiG;oBAC3GO,SAAS,EAAE,6BAA6B;oBACxCxB,OAAO,EAAE;kBACX;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DC,QAAQ,EAAE,iGAAiG;oBAC3GO,SAAS,EAAE,8BAA8B;oBACzCxB,OAAO,EAAE;kBACX;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,qDAAqD;cAC/DM,OAAO,EAAE;gBACPI,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;gBACjBD,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;gBACjBoB,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;gBACjBlB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClBN,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;cACjB,CAAE;cACFE,UAAU,EAAE;gBACVC,QAAQ,EAAE,EAAE;gBACZI,MAAM,EAAEC,QAAQ;gBAChBC,IAAI,EAAE;cACR,CAAE;cACFC,KAAK,EAAE;gBACLC,WAAW,EAAE,QAAQ;gBACrBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,eAEF1B,OAAA;gBACEyB,SAAS,EAAC,UAAU;gBACpBe,KAAK,EAAE;kBACLG,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdF,cAAc,EAAE;gBAClB,CAAE;gBAAAhB,QAAA,gBAIF1B,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,kBAAkB;oBAC7BN,SAAS,EAAE,iCAAiC;oBAC5CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,mCAAmC;oBAC9CN,SAAS,EAAE,kCAAkC;oBAC7CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,iCAAiC;oBAC5CN,SAAS,EAAE,kCAAkC;oBAC7CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,kCAAkC;oBAC7CN,SAAS,EAAE,kCAAkC;oBAC7CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,iCAAiC;oBAC5CN,SAAS,EAAE,kCAAkC;oBAC7CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFpD,OAAA;kBACEyB,SAAS,EAAC,kBAAkB;kBAC5Be,KAAK,EAAE;oBACLK,UAAU,EAAE,mDAAmD;oBAC/DQ,SAAS,EAAE,kCAAkC;oBAC7CN,SAAS,EAAE,kCAAkC;oBAC7CQ,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;YACTF,SAAS,EAAC,8BAA8B;YACxCe,KAAK,EAAE;cACLK,UAAU,EAAE,+GAA+G;cAC3HG,MAAM,EAAE;YACV,CAAE;YACFjB,OAAO,EAAE;cACPK,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACxB2B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YACtB,CAAE;YACFxB,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXI,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGFpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;YACTF,SAAS,EAAC,2BAA2B;YACrCG,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,gBAG9B1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,4BAA4B;cACtCG,OAAO,EAAE;gBAAE6B,CAAC,EAAE,CAAC,GAAG;gBAAE5B,OAAO,EAAE,CAAC;gBAAEK,OAAO,EAAE,CAAC;cAAG,CAAE;cAC/CH,OAAO,EAAE;gBACP0B,CAAC,EAAE,CAAC;gBACJ5B,OAAO,EAAE,CAAC;gBACVK,OAAO,EAAE;cACX,CAAE;cACFF,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbyB,KAAK,EAAE,GAAG;gBACVnB,IAAI,EAAE;cACR,CAAE;cAAAb,QAAA,eAEF1B,OAAA,CAACjB,MAAM,CAAC4E,IAAI;gBACVlC,SAAS,EAAC,kEAAkE;gBAC5Ee,KAAK,EAAE;kBACLK,UAAU,EAAE,6EAA6E;kBACzFe,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BC,UAAU,EAAE,kDAAkD;kBAC9DC,aAAa,EAAE,SAAS;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBACFnC,OAAO,EAAE;kBACPoC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpDrC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACboC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFlC,UAAU,EAAE;kBACVmC,kBAAkB,EAAE;oBAAElC,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACxET,CAAC,EAAE;oBAAEG,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACvD2B,UAAU,EAAE;oBAAEjC,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY;gBACjE,CAAE;gBACF6B,UAAU,EAAE;kBACVhC,KAAK,EAAE,IAAI;kBACXkB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACtBtB,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,EACH;cAED;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC,eAGbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,uBAAuB;cACjCG,OAAO,EAAE;gBAAE6B,CAAC,EAAE,GAAG;gBAAE5B,OAAO,EAAE,CAAC;gBAAEK,OAAO,EAAE;cAAG,CAAE;cAC7CH,OAAO,EAAE;gBACP0B,CAAC,EAAE,CAAC;gBACJ5B,OAAO,EAAE,CAAC;gBACVK,OAAO,EAAE;cACX,CAAE;cACFF,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbyB,KAAK,EAAE,CAAC;gBACRnB,IAAI,EAAE;cACR,CAAE;cAAAb,QAAA,gBAEF1B,OAAA,CAACjB,MAAM,CAAC4E,IAAI;gBACVlC,SAAS,EAAC,kEAAkE;gBAC5Ee,KAAK,EAAE;kBACLK,UAAU,EAAE,6EAA6E;kBACzFe,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BC,UAAU,EAAE,kDAAkD;kBAC9DC,aAAa,EAAE,SAAS;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBACFnC,OAAO,EAAE;kBACPoC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpDrC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACZoC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFlC,UAAU,EAAE;kBACVmC,kBAAkB,EAAE;oBAAElC,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEmB,KAAK,EAAE;kBAAE,CAAC;kBAClF5B,CAAC,EAAE;oBAAEG,QAAQ,EAAE,GAAG;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEmB,KAAK,EAAE;kBAAI,CAAC;kBACrEQ,UAAU,EAAE;oBAAEjC,QAAQ,EAAE,GAAG;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEmB,KAAK,EAAE;kBAAE;gBAC7E,CAAE;gBACFU,UAAU,EAAE;kBACVhC,KAAK,EAAE,IAAI;kBACXkB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACtBtB,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,EACH;cAED;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAGdpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,kDAAkD;gBAC5DM,OAAO,EAAE;kBACPyB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;kBACjBpB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXI,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBmB,KAAK,EAAE;gBACT,CAAE;gBACFlB,KAAK,EAAE;kBACLQ,MAAM,EAAE;gBACV,CAAE;gBAAAtB,QAAA,eAEF1B,OAAA,CAACJ,QAAQ;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,oDAAoD;gBAC9DM,OAAO,EAAE;kBACPD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACb0B,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACvBpB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,GAAG;kBACbI,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBmB,KAAK,EAAE;gBACT,CAAE;gBACFlB,KAAK,EAAE;kBACLQ,MAAM,EAAE;gBACV,CAAE;gBAAAtB,QAAA,eAEF1B,OAAA,CAACN,QAAQ;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,eAAe;cACzBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAI,CAAE;cAC3CL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACxCJ,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEyB,KAAK,EAAE;cAAE,CAAE;cAAAhC,QAAA,gBAExC1B,OAAA,CAACjB,MAAM,CAAC4E,IAAI;gBACVlC,SAAS,EAAC,sCAAsC;gBAChDe,KAAK,EAAE;kBACLK,UAAU,EAAE,4DAA4D;kBACxEe,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BG,UAAU,EAAE;gBACd,CAAE;gBACFnC,OAAO,EAAE;kBACPoC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpD/B,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnB8B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFlC,UAAU,EAAE;kBACVmC,kBAAkB,EAAE;oBAAElC,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACxEH,KAAK,EAAE;oBAAEH,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBAC3D2B,UAAU,EAAE;oBAAEjC,QAAQ,EAAE,CAAC;oBAAEI,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY;gBACjE,CAAE;gBACF6B,UAAU,EAAE;kBACVhC,KAAK,EAAE,GAAG;kBACVoB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACrBxB,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,GAEDtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,IAAI,EAAC,GACd;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAGdpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,mDAAmD;gBAC7DM,OAAO,EAAE;kBACPyB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;kBAChBpB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXI,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBACFC,KAAK,EAAE;kBACLQ,MAAM,EAAE;gBACV,CAAE;gBAAAtB,QAAA,eAEF1B,OAAA,CAACN,QAAQ;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,oDAAoD;gBAC9DM,OAAO,EAAE;kBACPD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;kBACd0B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;kBACvBpB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;kBAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBACvB,CAAE;gBACFG,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXI,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBmB,KAAK,EAAE;gBACT,CAAE;gBACFlB,KAAK,EAAE;kBACLQ,MAAM,EAAE;gBACV,CAAE;gBAAAtB,QAAA,eAEF1B,OAAA,CAACL,MAAM;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGbpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cACTF,SAAS,EAAC,eAAe;cACzBG,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEyB,KAAK,EAAE;cAAI,CAAE;cAAAhC,QAAA,eAExC1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;gBACTF,SAAS,EAAC,mDAAmD;gBAC7De,KAAK,EAAE;kBACLG,KAAK,EAAE,KAAK;kBACZE,UAAU,EAAE,qEAAqE;kBACjFE,SAAS,EAAE;gBACb,CAAE;gBACFhB,OAAO,EAAE;kBACPgB,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFf,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXI,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBAAAb,QAAA,eAGF1B,OAAA,CAACjB,MAAM,CAAC4C,GAAG;kBACTF,SAAS,EAAC,+BAA+B;kBACzCe,KAAK,EAAE;oBACLK,UAAU,EAAE,yEAAyE;oBACrFF,KAAK,EAAE;kBACT,CAAE;kBACFZ,OAAO,EAAE;oBACP0B,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;kBACrB,CAAE;kBACFzB,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXI,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE,WAAW;oBACjBmB,KAAK,EAAE;kBACT;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpD,OAAA;UAAGyB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJpD,OAAA;UAAKyB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1B,OAAA,CAACL,MAAM;YAAC6C,KAAK,EAAE;cAAEjB,KAAK,EAAE,SAAS;cAAE+C,WAAW,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAACxC,eAAe,CAACN,YAAY,CAAC,EAAC,IAChC,eAAAN,OAAA,CAACL,MAAM;YAAC6C,KAAK,EAAE;cAAEjB,KAAK,EAAE,SAAS;cAAEgD,UAAU,EAAE;YAAS;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DpD,OAAA;YAAKwC,KAAK,EAAE;cAAEgC,QAAQ,EAAE,UAAU;cAAEjD,KAAK,EAAE,SAAS;cAAEkD,SAAS,EAAE;YAAS,CAAE;YAAA/C,QAAA,EAAC;UAE7E;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbpD,OAAA;QAAKyB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC1B,OAAA;UAAKyB,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAACwD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACtD,IAAI;YAC/B,oBACErB,OAAA,CAACjB,MAAM,CAAC4C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEyB,KAAK,EAAEkB,KAAK,GAAG;cAAI,CAAE;cAClDnD,SAAS,EAAG,kBAAiBkD,IAAI,CAACnD,UAAW,IAAGmD,IAAI,CAACpD,KAAM,EAAE;cAC7DuD,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAACwE,IAAI,CAACrD,IAAI,CAAE;cACnCyD,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtChF,QAAQ,CAACwE,IAAI,CAACrD,IAAI,CAAC;gBACrB;cACF,CAAE;cAAAI,QAAA,gBAEF1B,OAAA;gBAAKyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1B,OAAA,CAAC6E,aAAa;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENpD,OAAA;gBAAIyB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BiD,IAAI,CAACxD;cAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELpD,OAAA;gBAAGyB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCiD,IAAI,CAACvD;cAAW;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GAxBCuB,IAAI,CAACxD,KAAK;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpD,OAAA,CAACjB,MAAM,CAAC4C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEyB,KAAK,EAAE;UAAI,CAAE;UAC1CjC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjC1B,OAAA;YAAKyB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1B,OAAA,CAACP,eAAe;cAACgC,SAAS,EAAC;YAAuC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEpD,OAAA;cAAA0B,QAAA,EAAM;YAAkC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CpD,OAAA,CAACJ,QAAQ;cAAC6B,SAAS,EAAC;YAAuC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAxoBID,GAAG;EAAA,QACUpB,WAAW,EACXC,WAAW;AAAA;AAAAsG,EAAA,GAFxBnF,GAAG;AA0oBT,eAAeA,GAAG;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}