{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto, sendOTP } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const sendOTPRequest = async email => {\n    dispatch(ShowLoading());\n    try {\n      const response = await sendOTP({\n        email\n      });\n      if (response.success) {\n        message.success(\"Please verify new email!\");\n        setEdit(false);\n        setServerGeneratedOTP(response.data);\n      } else {\n        message.error(response.message);\n        discardChanges();\n      }\n    } catch (error) {\n      message.error(error.message);\n      discardChanges();\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    if (!formData.name) return message.error(\"Please enter your name.\");\n    if (!formData.class_) return message.error(\"Please select a class.\");\n    if (!skipOTP && formData.email !== userDetails.email) {\n      return sendOTPRequest(formData.email);\n    }\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserInfo({\n        ...formData,\n        userId: userDetails._id\n      });\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        setServerGeneratedOTP(null);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    setProfileImage(file);\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const verifyUser = async values => {\n    if (values.otp === serverGeneratedOTP) {\n      handleUpdate({\n        skipOTP: true\n      });\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"WaXeGHP18SM3fDg4J3rcPvaWJng=\", false, function () {\n  return [useDispatch];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "sendOTP", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsForRanking", "jsxDEV", "_jsxDEV", "Profile", "_s", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "serverGeneratedOTP", "setServerGeneratedOTP", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "length", "prev", "discardChanges", "sendOTPRequest", "handleUpdate", "skipOTP", "levelChanged", "setTimeout", "window", "location", "reload", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleImageUpload", "FormData", "append", "verifyUser", "values", "otp", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    if (!formData.name) return message.error(\"Please enter your name.\");\r\n    if (!formData.class_) return message.error(\"Please select a class.\");\r\n\r\n    if (\r\n      !skipOTP &&\r\n      formData.email !== userDetails.email\r\n    ) {\r\n      return sendOTPRequest(formData.email);\r\n    }\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      });\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setProfileImage(file);\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      {/* Render UI Sections */}\r\n      {/* You may include your JSX from previous UI rendering here */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,EACfC,OAAO,QACF,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC2C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM6C,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,uBAAuB,CAAC,CAAC;MAChD,IAAIiC,QAAQ,CAACC,OAAO,EAAE;QACpB1B,cAAc,CAACyB,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL1C,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;MACjC;MACAsC,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;MAC5BsC,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAG/B,WAAW,CACxBgC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAACzC,WAAW,CAAC0C,GAAG,CAAC,CAAC;IAC/DrC,cAAc,CAAC4B,OAAO,CAAC;EACzB,CAAC;EAEDrD,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,IAAIF,WAAW,EAAE;MAC9BgC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC9B,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM2C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BjB,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMkC,QAAQ,GAAG,MAAM7C,WAAW,CAAC,CAAC;MACpC,IAAI6C,QAAQ,CAACC,OAAO,EAAE;QACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC;QAC7BnB,WAAW,CAAC;UACVC,IAAI,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEe,QAAQ,CAACE,IAAI,CAACjB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAAChB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEY,QAAQ,CAACE,IAAI,CAACc,KAAK,IAAI,EAAE;UACjC7B,KAAK,EAAEa,QAAQ,CAACE,IAAI,CAACf,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEW,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACZ,YAAY,EAAE;UAC9BC,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACZ,YAAY,CAAC;QAC7C;QACAS,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLvC,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRsC,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAIiE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEpC,IAAI;MAAEqC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAItC,IAAI,KAAK,aAAa,IAAIqC,KAAK,CAACE,MAAM,GAAG,EAAE,EAAE;IACjD,IAAIvC,IAAI,KAAK,OAAO,IAAIqC,KAAK,MAAKjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAIkC,KAAK,KAAK,EAAE,EAAE;MACpExB,qBAAqB,CAACwB,KAAK,CAAC;MAC5B1B,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAEyC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACxC,IAAI,GAAGqC,KAAK;MACb,IAAIrC,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B1C,WAAW,CAAC;MACVC,IAAI,EAAEZ,WAAW,CAACY,IAAI;MACtBC,KAAK,EAAEb,WAAW,CAACa,KAAK;MACxBC,MAAM,EAAEd,WAAW,CAACc,MAAM;MAC1BE,MAAM,EAAEhB,WAAW,CAAC4C,KAAK;MACzB7B,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBE,WAAW,EAAEjB,WAAW,CAACiB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAM+C,cAAc,GAAG,MAAOzC,KAAK,IAAK;IACtCa,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMkC,QAAQ,GAAG,MAAM1C,OAAO,CAAC;QAAE2B;MAAM,CAAC,CAAC;MACzC,IAAIe,QAAQ,CAACC,OAAO,EAAE;QACpBzC,OAAO,CAACyC,OAAO,CAAC,0BAA0B,CAAC;QAC3CtB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAACO,QAAQ,CAACE,IAAI,CAAC;MACtC,CAAC,MAAM;QACL1C,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;QAC/BiE,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;MAC5BiE,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACR3B,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM8D,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/C,IAAI,CAAC9C,QAAQ,CAACE,IAAI,EAAE,OAAOxB,OAAO,CAAC2C,KAAK,CAAC,yBAAyB,CAAC;IACnE,IAAI,CAACrB,QAAQ,CAACM,MAAM,EAAE,OAAO5B,OAAO,CAAC2C,KAAK,CAAC,wBAAwB,CAAC;IAEpE,IACE,CAACyB,OAAO,IACR9C,QAAQ,CAACG,KAAK,KAAKb,WAAW,CAACa,KAAK,EACpC;MACA,OAAOyC,cAAc,CAAC5C,QAAQ,CAACG,KAAK,CAAC;IACvC;IAEAa,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMkC,QAAQ,GAAG,MAAM5C,cAAc,CAAC;QACpC,GAAG0B,QAAQ;QACX8B,MAAM,EAAExC,WAAW,CAAC0C;MACtB,CAAC,CAAC;MACF,IAAId,QAAQ,CAACC,OAAO,EAAE;QACpBzC,OAAO,CAACyC,OAAO,CAACD,QAAQ,CAACxC,OAAO,CAAC;QACjCmB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAAC,IAAI,CAAC;QAC3BsB,WAAW,CAAC,CAAC;QACb,IAAIf,QAAQ,CAAC6B,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACLzE,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRsC,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMqE,wBAAwB,GAAGA,CAAA,KAAM;IACrCnD,WAAW,CAAEyC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPrC,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsC,uBAAuB,GAAGA,CAAA,KAAM;IACpCxC,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuC,iBAAiB,GAAIhB,CAAC,IAAK;IAC/B,MAAMiB,IAAI,GAAGjB,CAAC,CAACE,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9B/C,eAAe,CAAC8C,IAAI,CAAC;IACrB,IAAIA,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM5D,eAAe,CAAC0D,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAM1C,IAAI,GAAG,IAAI2C,QAAQ,CAAC,CAAC;IAC3B3C,IAAI,CAAC4C,MAAM,CAAC,cAAc,EAAExD,YAAY,CAAC;IACzCQ,QAAQ,CAAChC,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMkC,QAAQ,GAAG,MAAM3C,eAAe,CAAC6C,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpBzC,OAAO,CAACyC,OAAO,CAAC,6BAA6B,CAAC;QAC9Cc,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLvD,OAAO,CAAC2C,KAAK,CAACH,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAACA,KAAK,CAAC3C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRsC,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMkF,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACC,GAAG,KAAKzD,kBAAkB,EAAE;MACrCmC,YAAY,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLpE,OAAO,CAAC2C,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;EAED,oBACElC,OAAA;IAAKiF,SAAS,EAAC;EAAoE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAG9E,CAAC;AAEV,CAAC;AAACnF,EAAA,CAjOID,OAAO;EAAA,QAkBMN,WAAW;AAAA;AAAA2F,EAAA,GAlBxBrF,OAAO;AAmOb,eAAeA,OAAO;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}