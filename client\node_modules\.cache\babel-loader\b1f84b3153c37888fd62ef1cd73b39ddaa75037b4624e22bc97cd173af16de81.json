{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { TbMenu2, TbX, TbHome, TbBrain, TbBook, TbRobot, TbChartLine, TbTrophy, TbUser, TbMessageCircle, TbCreditCard, TbLogout, TbChevronRight } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = () => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigationItems = [{\n    title: 'Hub',\n    description: 'Main dashboard',\n    icon: TbHome,\n    path: '/user/hub',\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: TbBrain,\n    path: '/user/quiz',\n    color: 'from-purple-500 to-purple-600'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: TbBook,\n    path: '/user/study-material',\n    color: 'from-green-500 to-green-600'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help',\n    icon: TbRobot,\n    path: '/user/chat',\n    color: 'from-orange-500 to-orange-600'\n  }, {\n    title: 'Reports',\n    description: 'Track progress',\n    icon: TbChartLine,\n    path: '/user/reports',\n    color: 'from-red-500 to-red-600'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: TbTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600'\n  }, {\n    title: 'Profile',\n    description: 'Manage account',\n    icon: TbUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: TbMessageCircle,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade learning',\n    icon: TbCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600'\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n    setIsOpen(false);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n  const isActivePath = path => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\",\n      title: isOpen ? \"Close Menu\" : \"Open Menu\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n        className: \"w-6 h-6 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n        className: \"w-6 h-6 text-gray-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        onClick: () => setIsOpen(false),\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          x: -400,\n          opacity: 0\n        },\n        animate: {\n          x: 0,\n          opacity: 1\n        },\n        exit: {\n          x: -400,\n          opacity: 0\n        },\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200\n        },\n        className: \"fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-50 overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-6 rounded-sm overflow-hidden shadow-lg border border-white/20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-0 left-0 w-full h-1/3 bg-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-black tracking-tight\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white\",\n                  children: \"Brain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-300\",\n                  children: \"wave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-200 text-sm\",\n                children: \"Study Smarter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 rounded-full overflow-hidden shadow-lg border border-white/20 bg-white/10\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/favicon.png\",\n                alt: \"Brainwave Logo\",\n                className: \"w-full h-full object-cover\",\n                onError: e => {\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'flex';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm\",\n                style: {\n                  display: 'none'\n                },\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full overflow-hidden shadow-lg border-2 border-white/20 bg-white/10\",\n                children: [user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profileImage,\n                  alt: \"Profile\",\n                  className: \"w-full h-full object-cover\",\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg\",\n                  children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-white\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-200 text-sm\",\n                  children: [\"Class \", (user === null || user === void 0 ? void 0 : user.class) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            const isActive = isActivePath(item.path);\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.3,\n                delay: index * 0.05\n              },\n              onClick: () => handleNavigation(item.path),\n              className: `w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 ${isActive ? 'bg-blue-50 border-2 border-blue-200 shadow-md' : 'hover:bg-gray-50 border-2 border-transparent'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`,\n                  children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-left\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`,\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm ${isActive ? 'text-blue-600' : 'text-gray-500'}`,\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n                className: `w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-t border-gray-200 bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.3,\n              delay: 0.5\n            },\n            onClick: handleLogout,\n            className: \"w-full flex items-center justify-between p-4 rounded-xl bg-red-50 hover:bg-red-100 border-2 border-red-200 transition-all duration-200 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 rounded-lg bg-red-500 group-hover:bg-red-600 flex items-center justify-center transition-colors duration-200\",\n                children: /*#__PURE__*/_jsxDEV(TbLogout, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-red-700\",\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-red-600\",\n                  children: \"Sign out of account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n              className: \"w-5 h-5 text-red-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernSidebar, \"NZxrLuTrgWtZaSocs4AwyoW1Tmo=\", false, function () {\n  return [useNavigate, useLocation, useSelector];\n});\n_c = ModernSidebar;\nexport default ModernSidebar;\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "motion", "AnimatePresence", "useSelector", "TbMenu2", "TbX", "TbHome", "TbBrain", "TbBook", "TbRobot", "TbChartLine", "TbTrophy", "TbUser", "TbMessageCircle", "TbCreditCard", "TbLogout", "TbChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "_s", "_user$name", "_user$name$charAt", "isOpen", "setIsOpen", "navigate", "location", "user", "state", "navigationItems", "title", "description", "icon", "path", "color", "handleNavigation", "handleLogout", "localStorage", "removeItem", "isActivePath", "pathname", "startsWith", "children", "onClick", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "profileImage", "name", "char<PERSON>t", "toUpperCase", "class", "map", "item", "index", "IconComponent", "isActive", "button", "duration", "delay", "y", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernSidebar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight\n} from 'react-icons/tb';\n\nconst ModernSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  const navigationItems = [\n    {\n      title: 'Hub',\n      description: 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: TbBrain,\n      path: '/user/quiz',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: TbBook,\n      path: '/user/study-material',\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help',\n      icon: TbRobot,\n      path: '/user/chat',\n      color: 'from-orange-500 to-orange-600'\n    },\n    {\n      title: 'Reports',\n      description: 'Track progress',\n      icon: TbChartLine,\n      path: '/user/reports',\n      color: 'from-red-500 to-red-600'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: TbTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: TbMessageCircle,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade learning',\n      icon: TbCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600'\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\"\n        title={isOpen ? \"Close Menu\" : \"Open Menu\"}\n      >\n        {isOpen ? (\n          <TbX className=\"w-6 h-6 text-gray-700\" />\n        ) : (\n          <TbMenu2 className=\"w-6 h-6 text-gray-700\" />\n        )}\n      </button>\n\n      {/* Backdrop */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setIsOpen(false)}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ x: -400, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -400, opacity: 0 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 200 }}\n            className=\"fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-50 overflow-y-auto\"\n          >\n            {/* Header */}\n            <div className=\"p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white\">\n              {/* Brainwave Logo Section */}\n              <div className=\"flex items-center space-x-3 mb-6\">\n                {/* Tanzania Flag */}\n                <div className=\"w-8 h-6 rounded-sm overflow-hidden shadow-lg border border-white/20\">\n                  <div className=\"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\">\n                    <div className=\"absolute top-0 left-0 w-full h-1/3 bg-green-500\"></div>\n                    <div className=\"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"></div>\n                    <div className=\"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"></div>\n                  </div>\n                </div>\n\n                {/* Brainwave Text */}\n                <div>\n                  <h1 className=\"text-xl font-black tracking-tight\">\n                    <span className=\"text-white\">Brain</span>\n                    <span className=\"text-green-300\">wave</span>\n                  </h1>\n                  <p className=\"text-blue-200 text-sm\">Study Smarter</p>\n                </div>\n\n                {/* Official Logo */}\n                <div className=\"w-8 h-8 rounded-full overflow-hidden shadow-lg border border-white/20 bg-white/10\">\n                  <img\n                    src=\"/favicon.png\"\n                    alt=\"Brainwave Logo\"\n                    className=\"w-full h-full object-cover\"\n                    onError={(e) => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }}\n                  />\n                  <div className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm\" style={{display: 'none'}}>\n                    🧠\n                  </div>\n                </div>\n              </div>\n\n              {/* User Profile */}\n              <div className=\"bg-white/10 rounded-lg p-4\">\n                <div className=\"flex items-center space-x-3\">\n                  {/* User Profile Picture */}\n                  <div className=\"w-12 h-12 rounded-full overflow-hidden shadow-lg border-2 border-white/20 bg-white/10\">\n                    {user?.profileImage ? (\n                      <img\n                        src={user.profileImage}\n                        alt=\"Profile\"\n                        className=\"w-full h-full object-cover\"\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'flex';\n                        }}\n                      />\n                    ) : null}\n                    <div className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg\">\n                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                    </div>\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"font-semibold text-white\">{user?.name || 'User'}</p>\n                    <p className=\"text-blue-200 text-sm\">Class {user?.class || 'N/A'}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = isActivePath(item.path);\n\n                return (\n                  <motion.button\n                    key={item.path}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}>\n                        <IconComponent className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p className={`font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`}>\n                          {item.title}\n                        </p>\n                        <p className={`text-sm ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />\n                  </motion.button>\n                );\n              })}\n            </div>\n\n            {/* Logout Button - Fixed at Bottom */}\n            <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n              <motion.button\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: 0.5 }}\n                onClick={handleLogout}\n                className=\"w-full flex items-center justify-between p-4 rounded-xl bg-red-50 hover:bg-red-100 border-2 border-red-200 transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 rounded-lg bg-red-500 group-hover:bg-red-600 flex items-center justify-center transition-colors duration-200\">\n                    <TbLogout className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"text-left\">\n                    <p className=\"font-medium text-red-700\">Logout</p>\n                    <p className=\"text-sm text-red-600\">Sign out of account</p>\n                  </div>\n                </div>\n                <TbChevronRight className=\"w-5 h-5 text-red-400\" />\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default ModernSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,cAAc,QACT,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAK,CAAC,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAE5B,MAAM;IACZ6B,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE3B,OAAO;IACb4B,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE1B,MAAM;IACZ2B,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAEzB,OAAO;IACb0B,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAExB,WAAW;IACjByB,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAEvB,QAAQ;IACdwB,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAEtB,MAAM;IACZuB,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAErB,eAAe;IACrBsB,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAEpB,YAAY;IAClBqB,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAIF,IAAI,IAAK;IACjCR,QAAQ,CAACQ,IAAI,CAAC;IACdT,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/Bb,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMc,YAAY,GAAIN,IAAI,IAAK;IAC7B,OAAOP,QAAQ,CAACc,QAAQ,KAAKP,IAAI,IAAIP,QAAQ,CAACc,QAAQ,CAACC,UAAU,CAACR,IAAI,CAAC;EACzE,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBAEE1B,OAAA;MACE2B,OAAO,EAAEA,CAAA,KAAMnB,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCqB,SAAS,EAAC,8IAA8I;MACxJd,KAAK,EAAEP,MAAM,GAAG,YAAY,GAAG,WAAY;MAAAmB,QAAA,EAE1CnB,MAAM,gBACLP,OAAA,CAACb,GAAG;QAACyC,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEzChC,OAAA,CAACd,OAAO;QAAC0C,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC7C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAGThC,OAAA,CAAChB,eAAe;MAAA0C,QAAA,EACbnB,MAAM,iBACLP,OAAA,CAACjB,MAAM,CAACkD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBR,OAAO,EAAEA,CAAA,KAAMnB,SAAS,CAAC,KAAK,CAAE;QAChCoB,SAAS,EAAC;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBhC,OAAA,CAAChB,eAAe;MAAA0C,QAAA,EACbnB,MAAM,iBACLP,OAAA,CAACjB,MAAM,CAACkD,GAAG;QACTC,OAAO,EAAE;UAAEI,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACjCC,OAAO,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEC,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE;QAC5Dd,SAAS,EAAC,yEAAyE;QAAAF,QAAA,gBAGnF1B,OAAA;UAAK4B,SAAS,EAAC,2DAA2D;UAAAF,QAAA,gBAExE1B,OAAA;YAAK4B,SAAS,EAAC,kCAAkC;YAAAF,QAAA,gBAE/C1B,OAAA;cAAK4B,SAAS,EAAC,qEAAqE;cAAAF,QAAA,eAClF1B,OAAA;gBAAK4B,SAAS,EAAC,mFAAmF;gBAAAF,QAAA,gBAChG1B,OAAA;kBAAK4B,SAAS,EAAC;gBAAiD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEhC,OAAA;kBAAK4B,SAAS,EAAC;gBAAoD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1EhC,OAAA;kBAAK4B,SAAS,EAAC;gBAAmD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhC,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAI4B,SAAS,EAAC,mCAAmC;gBAAAF,QAAA,gBAC/C1B,OAAA;kBAAM4B,SAAS,EAAC,YAAY;kBAAAF,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzChC,OAAA;kBAAM4B,SAAS,EAAC,gBAAgB;kBAAAF,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACLhC,OAAA;gBAAG4B,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAGNhC,OAAA;cAAK4B,SAAS,EAAC,mFAAmF;cAAAF,QAAA,gBAChG1B,OAAA;gBACE2C,GAAG,EAAC,cAAc;gBAClBC,GAAG,EAAC,gBAAgB;gBACpBhB,SAAS,EAAC,4BAA4B;gBACtCiB,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;gBAC7C;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFhC,OAAA;gBAAK4B,SAAS,EAAC,8GAA8G;gBAACoB,KAAK,EAAE;kBAACC,OAAO,EAAE;gBAAM,CAAE;gBAAAvB,QAAA,EAAC;cAExJ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAK4B,SAAS,EAAC,4BAA4B;YAAAF,QAAA,eACzC1B,OAAA;cAAK4B,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBAE1C1B,OAAA;gBAAK4B,SAAS,EAAC,uFAAuF;gBAAAF,QAAA,GACnGf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwC,YAAY,gBACjBnD,OAAA;kBACE2C,GAAG,EAAEhC,IAAI,CAACwC,YAAa;kBACvBP,GAAG,EAAC,SAAS;kBACbhB,SAAS,EAAC,4BAA4B;kBACtCiB,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACA,IAAI,eACRhC,OAAA;kBAAK4B,SAAS,EAAC,wHAAwH;kBAAAF,QAAA,EACpI,CAAAf,IAAI,aAAJA,IAAI,wBAAAN,UAAA,GAAJM,IAAI,CAAEyC,IAAI,cAAA/C,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYgD,MAAM,CAAC,CAAC,CAAC,cAAA/C,iBAAA,uBAArBA,iBAAA,CAAuBgD,WAAW,CAAC,CAAC,KAAI;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhC,OAAA;gBAAK4B,SAAS,EAAC,QAAQ;gBAAAF,QAAA,gBACrB1B,OAAA;kBAAG4B,SAAS,EAAC,0BAA0B;kBAAAF,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,IAAI,KAAI;gBAAM;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEhC,OAAA;kBAAG4B,SAAS,EAAC,uBAAuB;kBAAAF,QAAA,GAAC,QAAM,EAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,KAAK,KAAI,KAAK;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhC,OAAA;UAAK4B,SAAS,EAAC,sCAAsC;UAAAF,QAAA,EAClDb,eAAe,CAAC2C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACzC,IAAI;YAC/B,MAAM4C,QAAQ,GAAGrC,YAAY,CAACkC,IAAI,CAACxC,IAAI,CAAC;YAExC,oBACEjB,OAAA,CAACjB,MAAM,CAAC8E,MAAM;cAEZ3B,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE;cAAE,CAAE;cAC9BC,UAAU,EAAE;gBAAEuB,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEL,KAAK,GAAG;cAAK,CAAE;cACnD/B,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAACsC,IAAI,CAACxC,IAAI,CAAE;cAC3CW,SAAS,EAAG,uFACVgC,QAAQ,GACJ,+CAA+C,GAC/C,8CACL,EAAE;cAAAlC,QAAA,gBAEH1B,OAAA;gBAAK4B,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1C1B,OAAA;kBAAK4B,SAAS,EAAG,yCAAwC6B,IAAI,CAACvC,KAAM,mCAAmC;kBAAAQ,QAAA,eACrG1B,OAAA,CAAC2D,aAAa;oBAAC/B,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNhC,OAAA;kBAAK4B,SAAS,EAAC,WAAW;kBAAAF,QAAA,gBACxB1B,OAAA;oBAAG4B,SAAS,EAAG,eAAcgC,QAAQ,GAAG,eAAe,GAAG,eAAgB,EAAE;oBAAAlC,QAAA,EACzE+B,IAAI,CAAC3C;kBAAK;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACJhC,OAAA;oBAAG4B,SAAS,EAAG,WAAUgC,QAAQ,GAAG,eAAe,GAAG,eAAgB,EAAE;oBAAAlC,QAAA,EACrE+B,IAAI,CAAC1C;kBAAW;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhC,OAAA,CAACF,cAAc;gBAAC8B,SAAS,EAAG,WAAUgC,QAAQ,GAAG,eAAe,GAAG,eAAgB;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAxBnFyB,IAAI,CAACxC,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBD,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhC,OAAA;UAAK4B,SAAS,EAAC,yCAAyC;UAAAF,QAAA,eACtD1B,OAAA,CAACjB,MAAM,CAAC8E,MAAM;YACZ3B,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE6B,CAAC,EAAE;YAAG,CAAE;YAC/B5B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE6B,CAAC,EAAE;YAAE,CAAE;YAC9BzB,UAAU,EAAE;cAAEuB,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CpC,OAAO,EAAEP,YAAa;YACtBQ,SAAS,EAAC,8IAA8I;YAAAF,QAAA,gBAExJ1B,OAAA;cAAK4B,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBAC1C1B,OAAA;gBAAK4B,SAAS,EAAC,wHAAwH;gBAAAF,QAAA,eACrI1B,OAAA,CAACH,QAAQ;kBAAC+B,SAAS,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNhC,OAAA;gBAAK4B,SAAS,EAAC,WAAW;gBAAAF,QAAA,gBACxB1B,OAAA;kBAAG4B,SAAS,EAAC,0BAA0B;kBAAAF,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClDhC,OAAA;kBAAG4B,SAAS,EAAC,sBAAsB;kBAAAF,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhC,OAAA,CAACF,cAAc;cAAC8B,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAAC5B,EAAA,CAhQID,aAAa;EAAA,QAEAtB,WAAW,EACXC,WAAW,EACXG,WAAW;AAAA;AAAAgF,EAAA,GAJxB9D,aAAa;AAkQnB,eAAeA,aAAa;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}