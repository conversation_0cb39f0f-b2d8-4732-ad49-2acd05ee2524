const axios = require('axios');
const mongoose = require('mongoose');
const User = require('./models/userModel');
const Exam = require('./models/examModel');
require('dotenv').config();

async function testQuizSubmission() {
  try {
    console.log('🔍 Testing Quiz Submission Flow...\n');

    // Connect to MongoDB to get test data
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');

    // Find a test user (non-admin)
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}\n`);

    // Find a test exam
    const testExam = await Exam.findOne().limit(1);
    if (!testExam) {
      console.log('❌ No test exam found');
      return;
    }

    console.log(`📝 Test Exam: ${testExam.name}`);
    console.log(`📚 Subject: ${testExam.subject || 'General'}\n`);

    // Create a mock quiz result (simulating frontend data)
    const mockResult = {
      correctAnswers: [
        { name: 'Question 1', userAnswer: 'A' },
        { name: 'Question 2', userAnswer: 'B' },
        { name: 'Question 3', userAnswer: 'C' }
      ],
      wrongAnswers: [
        { name: 'Question 4', userAnswer: 'D' }
      ],
      verdict: 'Pass',
      score: 75,
      points: 30,
      totalQuestions: 4,
      timeSpent: 300 // 5 minutes
    };

    // Simulate the exact API call that the frontend makes
    const reportPayload = {
      exam: testExam._id,
      result: mockResult,
      user: testUser._id
    };

    console.log('📤 Submitting quiz result to API...');
    console.log('Payload:', JSON.stringify(reportPayload, null, 2));

    // First, let's get a valid token by logging in
    const loginResponse = await axios.post('http://localhost:5000/api/users/login', {
      email: testUser.email,
      password: 'password123' // Assuming this is the test password
    });

    if (!loginResponse.data.success) {
      console.log('❌ Login failed, trying with different password...');
      // Try common test passwords
      const testPasswords = ['123456', 'password', 'test123', 'admin123'];
      let token = null;
      
      for (const pwd of testPasswords) {
        try {
          const loginAttempt = await axios.post('http://localhost:5000/api/users/login', {
            email: testUser.email,
            password: pwd
          });
          if (loginAttempt.data.success) {
            token = loginAttempt.data.data;
            console.log('✅ Login successful with password:', pwd);
            break;
          }
        } catch (e) {
          // Continue trying
        }
      }
      
      if (!token) {
        console.log('❌ Could not login with any test password');
        return;
      }
    } else {
      console.log('✅ Login successful');
    }

    const token = loginResponse.data.data;

    // Submit the quiz result
    const response = await axios.post(
      'http://localhost:5000/api/reports/add-report',
      reportPayload,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('\n📊 API Response:');
    console.log('Success:', response.data.success);
    console.log('Message:', response.data.message);
    console.log('XP Data:', response.data.xpData);

    if (response.data.xpData) {
      console.log('\n🎉 XP was awarded successfully!');
      console.log(`💰 XP Awarded: ${response.data.xpData.xpAwarded}`);
      console.log(`📈 New Total XP: ${response.data.xpData.newTotalXP}`);
      console.log(`🆙 Level Up: ${response.data.xpData.levelUp}`);
      console.log(`🎯 New Level: ${response.data.xpData.newLevel}`);
    } else {
      console.log('\n❌ No XP data in response - XP system may not be working');
    }

    // Verify the user's XP was actually updated
    const updatedUser = await User.findById(testUser._id);
    console.log('\n✅ User verification:');
    console.log(`📊 Updated XP: ${updatedUser.totalXP}`);
    console.log(`🎯 Updated Level: ${updatedUser.currentLevel}`);

    if (updatedUser.totalXP > testUser.totalXP) {
      console.log('🎉 XP was successfully added to user!');
    } else {
      console.log('❌ User XP was not updated');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the test
testQuizSubmission();
