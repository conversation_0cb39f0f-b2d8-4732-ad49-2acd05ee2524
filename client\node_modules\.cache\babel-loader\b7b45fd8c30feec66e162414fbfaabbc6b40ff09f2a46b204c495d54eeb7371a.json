{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\XPResultDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbBolt, TbTrophy, TbFlame, TbTarget, TbClock, TbTrendingUp, TbStar, TbMedal, TbChevronDown, TbChevronUp } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst XPResultDisplay = ({\n  xpData,\n  className = ''\n}) => {\n  _s();\n  const [showBreakdown, setShowBreakdown] = useState(false);\n  const [animatedXP, setAnimatedXP] = useState(0);\n\n  // Debug XP data (safely)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🎨 XPResultDisplay - XP Data received:', xpData);\n  }\n  useEffect(() => {\n    if (xpData !== null && xpData !== void 0 && xpData.xpAwarded) {\n      // Animate XP counter\n      const duration = 2000;\n      const steps = 60;\n      const increment = xpData.xpAwarded / steps;\n      let current = 0;\n      const timer = setInterval(() => {\n        current += increment;\n        if (current >= xpData.xpAwarded) {\n          setAnimatedXP(xpData.xpAwarded);\n          clearInterval(timer);\n        } else {\n          setAnimatedXP(Math.floor(current));\n        }\n      }, duration / steps);\n      return () => clearInterval(timer);\n    }\n  }, [xpData]);\n  if (!xpData) {\n    return null;\n  }\n  const {\n    xpAwarded = 0,\n    xpBreakdown = {},\n    levelUp = false,\n    newLevel = 1,\n    newTotalXP = 0,\n    currentStreak = 0,\n    achievements = []\n  } = xpData;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: levelUp && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.8,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          scale: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.8,\n          y: -20\n        },\n        className: \"mb-6 p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl text-white text-center shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-8 h-8 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold\",\n            children: \"LEVEL UP!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg\",\n          children: [\"You reached Level \", newLevel, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n          className: \"w-8 h-8 text-purple-600 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-800\",\n          children: \"XP Earned\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0\n        },\n        animate: {\n          scale: 1\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 200,\n          damping: 10\n        },\n        className: \"text-6xl font-bold text-purple-600 mb-2\",\n        children: [\"+\", animatedXP]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: [\"Total XP: \", newTotalXP.toLocaleString(), \" | Level \", newLevel]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 gap-4 mb-6\",\n      children: [currentStreak > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-3 text-center shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n          className: \"w-6 h-6 text-orange-500 mx-auto mb-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-800\",\n          children: currentStreak\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Streak\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-3 text-center shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n          className: \"w-6 h-6 text-yellow-500 mx-auto mb-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-800\",\n          children: achievements.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"New Badges\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), Object.keys(xpBreakdown).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowBreakdown(!showBreakdown),\n        className: \"w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-800\",\n          children: \"XP Breakdown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), showBreakdown ? /*#__PURE__*/_jsxDEV(TbChevronUp, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(TbChevronDown, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showBreakdown && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: \"auto\"\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: \"mt-3 bg-white rounded-lg p-4 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [xpBreakdown.baseXP && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-4 h-4 text-blue-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"Base XP\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-gray-800\",\n                children: [\"+\", xpBreakdown.baseXP]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 21\n            }, this), xpBreakdown.difficultyBonus > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                  className: \"w-4 h-4 text-green-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"Difficulty Bonus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-600\",\n                children: [\"+\", xpBreakdown.difficultyBonus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 21\n            }, this), xpBreakdown.speedBonus > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                  className: \"w-4 h-4 text-yellow-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"Speed Bonus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-yellow-600\",\n                children: [\"+\", xpBreakdown.speedBonus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 21\n            }, this), xpBreakdown.perfectScoreBonus > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-4 h-4 text-purple-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"Perfect Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-purple-600\",\n                children: [\"+\", xpBreakdown.perfectScoreBonus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 21\n            }, this), xpBreakdown.streakBonus > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                  className: \"w-4 h-4 text-orange-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"Streak Bonus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-orange-600\",\n                children: [\"+\", xpBreakdown.streakBonus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 21\n            }, this), xpBreakdown.firstAttemptBonus > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n                  className: \"w-4 h-4 text-indigo-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"First Attempt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-indigo-600\",\n                children: [\"+\", xpBreakdown.firstAttemptBonus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-bold text-gray-800 mb-3 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n          className: \"w-5 h-5 mr-2 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), \"New Achievements\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: achievements.map((achievement, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: \"flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 text-yellow-500 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-gray-800\",\n              children: achievement.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: achievement.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(XPResultDisplay, \"O4+TueljhbKxDQEsLf/osDEBB7M=\");\n_c = XPResultDisplay;\nexport default XPResultDisplay;\nvar _c;\n$RefreshReg$(_c, \"XPResultDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "TbBolt", "TbTrophy", "TbFlame", "TbTarget", "TbClock", "TbTrendingUp", "TbStar", "TbMedal", "TbChevronDown", "TbChevronUp", "jsxDEV", "_jsxDEV", "XPResultDisplay", "xpData", "className", "_s", "showBreakdown", "setShowBreakdown", "animatedXP", "setAnimatedXP", "process", "env", "NODE_ENV", "console", "log", "xpAwarded", "duration", "steps", "increment", "current", "timer", "setInterval", "clearInterval", "Math", "floor", "xpBreakdown", "levelUp", "newLevel", "newTotalXP", "currentStreak", "achievements", "children", "div", "initial", "opacity", "scale", "y", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "type", "stiffness", "damping", "toLocaleString", "length", "Object", "keys", "onClick", "height", "baseXP", "difficultyBonus", "speedBonus", "perfectScoreBonus", "streakBonus", "firstAttemptBonus", "map", "achievement", "index", "x", "delay", "name", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/XPResultDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  TbB<PERSON>, \n  TbTrophy, \n  Tb<PERSON>lame, \n  TbTarget, \n  TbClock, \n  TbTrendingUp,\n  TbStar,\n  TbMedal,\n  TbChevronDown,\n  TbChevronUp\n} from 'react-icons/tb';\n\nconst XPResultDisplay = ({ xpData, className = '' }) => {\n  const [showBreakdown, setShowBreakdown] = useState(false);\n  const [animatedXP, setAnimatedXP] = useState(0);\n\n  // Debug XP data (safely)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🎨 XPResultDisplay - XP Data received:', xpData);\n  }\n\n  useEffect(() => {\n    if (xpData?.xpAwarded) {\n      // Animate XP counter\n      const duration = 2000;\n      const steps = 60;\n      const increment = xpData.xpAwarded / steps;\n      let current = 0;\n\n      const timer = setInterval(() => {\n        current += increment;\n        if (current >= xpData.xpAwarded) {\n          setAnimatedXP(xpData.xpAwarded);\n          clearInterval(timer);\n        } else {\n          setAnimatedXP(Math.floor(current));\n        }\n      }, duration / steps);\n\n      return () => clearInterval(timer);\n    }\n  }, [xpData]);\n\n  if (!xpData) {\n    return null;\n  }\n\n  const {\n    xpAwarded = 0,\n    xpBreakdown = {},\n    levelUp = false,\n    newLevel = 1,\n    newTotalXP = 0,\n    currentStreak = 0,\n    achievements = []\n  } = xpData;\n\n  return (\n    <div className={`bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ${className}`}>\n      {/* Level Up Notification */}\n      <AnimatePresence>\n        {levelUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8, y: -20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.8, y: -20 }}\n            className=\"mb-6 p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl text-white text-center shadow-lg\"\n          >\n            <div className=\"flex items-center justify-center mb-2\">\n              <TbTrophy className=\"w-8 h-8 mr-2\" />\n              <span className=\"text-xl font-bold\">LEVEL UP!</span>\n            </div>\n            <p className=\"text-lg\">You reached Level {newLevel}!</p>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main XP Display */}\n      <div className=\"text-center mb-6\">\n        <div className=\"flex items-center justify-center mb-3\">\n          <TbBolt className=\"w-8 h-8 text-purple-600 mr-2\" />\n          <h3 className=\"text-2xl font-bold text-gray-800\">XP Earned</h3>\n        </div>\n        \n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 200, damping: 10 }}\n          className=\"text-6xl font-bold text-purple-600 mb-2\"\n        >\n          +{animatedXP}\n        </motion.div>\n        \n        <p className=\"text-gray-600\">\n          Total XP: {newTotalXP.toLocaleString()} | Level {newLevel}\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 gap-4 mb-6\">\n        {currentStreak > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbFlame className=\"w-6 h-6 text-orange-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{currentStreak}</div>\n            <div className=\"text-sm text-gray-600\">Streak</div>\n          </div>\n        )}\n        \n        {achievements.length > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbMedal className=\"w-6 h-6 text-yellow-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{achievements.length}</div>\n            <div className=\"text-sm text-gray-600\">New Badges</div>\n          </div>\n        )}\n      </div>\n\n      {/* XP Breakdown Toggle */}\n      {Object.keys(xpBreakdown).length > 0 && (\n        <div>\n          <button\n            onClick={() => setShowBreakdown(!showBreakdown)}\n            className=\"w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200\"\n          >\n            <span className=\"font-medium text-gray-800\">XP Breakdown</span>\n            {showBreakdown ? (\n              <TbChevronUp className=\"w-5 h-5 text-gray-600\" />\n            ) : (\n              <TbChevronDown className=\"w-5 h-5 text-gray-600\" />\n            )}\n          </button>\n\n          <AnimatePresence>\n            {showBreakdown && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-3 bg-white rounded-lg p-4 shadow-sm\"\n              >\n                <div className=\"space-y-3\">\n                  {xpBreakdown.baseXP && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTarget className=\"w-4 h-4 text-blue-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Base XP</span>\n                      </div>\n                      <span className=\"font-medium text-gray-800\">+{xpBreakdown.baseXP}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.difficultyBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTrendingUp className=\"w-4 h-4 text-green-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Difficulty Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-green-600\">+{xpBreakdown.difficultyBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.speedBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbClock className=\"w-4 h-4 text-yellow-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Speed Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-yellow-600\">+{xpBreakdown.speedBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.perfectScoreBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbStar className=\"w-4 h-4 text-purple-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Perfect Score</span>\n                      </div>\n                      <span className=\"font-medium text-purple-600\">+{xpBreakdown.perfectScoreBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.streakBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbFlame className=\"w-4 h-4 text-orange-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Streak Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-orange-600\">+{xpBreakdown.streakBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.firstAttemptBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbMedal className=\"w-4 h-4 text-indigo-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">First Attempt</span>\n                      </div>\n                      <span className=\"font-medium text-indigo-600\">+{xpBreakdown.firstAttemptBonus}</span>\n                    </div>\n                  )}\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      )}\n\n      {/* Achievement Notifications */}\n      {achievements.length > 0 && (\n        <div className=\"mt-6\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3 flex items-center\">\n            <TbMedal className=\"w-5 h-5 mr-2 text-yellow-500\" />\n            New Achievements\n          </h4>\n          <div className=\"space-y-2\">\n            {achievements.map((achievement, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\"\n              >\n                <TbTrophy className=\"w-6 h-6 text-yellow-500 mr-3\" />\n                <div>\n                  <div className=\"font-medium text-gray-800\">{achievement.name}</div>\n                  <div className=\"text-sm text-gray-600\">{achievement.description}</div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default XPResultDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,IAAIwB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEX,MAAM,CAAC;EAC/D;EAEAhB,SAAS,CAAC,MAAM;IACd,IAAIgB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEY,SAAS,EAAE;MACrB;MACA,MAAMC,QAAQ,GAAG,IAAI;MACrB,MAAMC,KAAK,GAAG,EAAE;MAChB,MAAMC,SAAS,GAAGf,MAAM,CAACY,SAAS,GAAGE,KAAK;MAC1C,IAAIE,OAAO,GAAG,CAAC;MAEf,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;QAC9BF,OAAO,IAAID,SAAS;QACpB,IAAIC,OAAO,IAAIhB,MAAM,CAACY,SAAS,EAAE;UAC/BN,aAAa,CAACN,MAAM,CAACY,SAAS,CAAC;UAC/BO,aAAa,CAACF,KAAK,CAAC;QACtB,CAAC,MAAM;UACLX,aAAa,CAACc,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC,CAAC;QACpC;MACF,CAAC,EAAEH,QAAQ,GAAGC,KAAK,CAAC;MAEpB,OAAO,MAAMK,aAAa,CAACF,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACjB,MAAM,CAAC,CAAC;EAEZ,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAM;IACJY,SAAS,GAAG,CAAC;IACbU,WAAW,GAAG,CAAC,CAAC;IAChBC,OAAO,GAAG,KAAK;IACfC,QAAQ,GAAG,CAAC;IACZC,UAAU,GAAG,CAAC;IACdC,aAAa,GAAG,CAAC;IACjBC,YAAY,GAAG;EACjB,CAAC,GAAG3B,MAAM;EAEV,oBACEF,OAAA;IAAKG,SAAS,EAAG,sGAAqGA,SAAU,EAAE;IAAA2B,QAAA,gBAEhI9B,OAAA,CAACZ,eAAe;MAAA0C,QAAA,EACbL,OAAO,iBACNzB,OAAA,CAACb,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,GAAG;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC5CC,OAAO,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QACxCE,IAAI,EAAE;UAAEJ,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE,GAAG;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QACzChC,SAAS,EAAC,qGAAqG;QAAA2B,QAAA,gBAE/G9B,OAAA;UAAKG,SAAS,EAAC,uCAAuC;UAAA2B,QAAA,gBACpD9B,OAAA,CAACV,QAAQ;YAACa,SAAS,EAAC;UAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCzC,OAAA;YAAMG,SAAS,EAAC,mBAAmB;YAAA2B,QAAA,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNzC,OAAA;UAAGG,SAAS,EAAC,SAAS;UAAA2B,QAAA,GAAC,oBAAkB,EAACJ,QAAQ,EAAC,GAAC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBzC,OAAA;MAAKG,SAAS,EAAC,kBAAkB;MAAA2B,QAAA,gBAC/B9B,OAAA;QAAKG,SAAS,EAAC,uCAAuC;QAAA2B,QAAA,gBACpD9B,OAAA,CAACX,MAAM;UAACc,SAAS,EAAC;QAA8B;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDzC,OAAA;UAAIG,SAAS,EAAC,kCAAkC;UAAA2B,QAAA,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENzC,OAAA,CAACb,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEE,KAAK,EAAE;QAAE,CAAE;QACtBE,OAAO,EAAE;UAAEF,KAAK,EAAE;QAAE,CAAE;QACtBQ,UAAU,EAAE;UAAEC,IAAI,EAAE,QAAQ;UAAEC,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAC5D1C,SAAS,EAAC,yCAAyC;QAAA2B,QAAA,GACpD,GACE,EAACvB,UAAU;MAAA;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEbzC,OAAA;QAAGG,SAAS,EAAC,eAAe;QAAA2B,QAAA,GAAC,YACjB,EAACH,UAAU,CAACmB,cAAc,CAAC,CAAC,EAAC,WAAS,EAACpB,QAAQ;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNzC,OAAA;MAAKG,SAAS,EAAC,6BAA6B;MAAA2B,QAAA,GACzCF,aAAa,GAAG,CAAC,iBAChB5B,OAAA;QAAKG,SAAS,EAAC,+CAA+C;QAAA2B,QAAA,gBAC5D9B,OAAA,CAACT,OAAO;UAACY,SAAS,EAAC;QAAsC;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DzC,OAAA;UAAKG,SAAS,EAAC,iCAAiC;UAAA2B,QAAA,EAAEF;QAAa;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtEzC,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAA2B,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACN,EAEAZ,YAAY,CAACkB,MAAM,GAAG,CAAC,iBACtB/C,OAAA;QAAKG,SAAS,EAAC,+CAA+C;QAAA2B,QAAA,gBAC5D9B,OAAA,CAACJ,OAAO;UAACO,SAAS,EAAC;QAAsC;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DzC,OAAA;UAAKG,SAAS,EAAC,iCAAiC;UAAA2B,QAAA,EAAED,YAAY,CAACkB;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5EzC,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAA2B,QAAA,EAAC;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLO,MAAM,CAACC,IAAI,CAACzB,WAAW,CAAC,CAACuB,MAAM,GAAG,CAAC,iBAClC/C,OAAA;MAAA8B,QAAA,gBACE9B,OAAA;QACEkD,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC,CAACD,aAAa,CAAE;QAChDF,SAAS,EAAC,2HAA2H;QAAA2B,QAAA,gBAErI9B,OAAA;UAAMG,SAAS,EAAC,2BAA2B;UAAA2B,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9DpC,aAAa,gBACZL,OAAA,CAACF,WAAW;UAACK,SAAS,EAAC;QAAuB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEjDzC,OAAA,CAACH,aAAa;UAACM,SAAS,EAAC;QAAuB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAETzC,OAAA,CAACZ,eAAe;QAAA0C,QAAA,EACbzB,aAAa,iBACZL,OAAA,CAACb,MAAM,CAAC4C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEkB,MAAM,EAAE;UAAE,CAAE;UACnCf,OAAO,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEkB,MAAM,EAAE;UAAO,CAAE;UACxCd,IAAI,EAAE;YAAEJ,OAAO,EAAE,CAAC;YAAEkB,MAAM,EAAE;UAAE,CAAE;UAChChD,SAAS,EAAC,wCAAwC;UAAA2B,QAAA,eAElD9B,OAAA;YAAKG,SAAS,EAAC,WAAW;YAAA2B,QAAA,GACvBN,WAAW,CAAC4B,MAAM,iBACjBpD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACR,QAAQ;kBAACW,SAAS,EAAC;gBAA4B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,2BAA2B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAAC4B,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CACN,EAEAjB,WAAW,CAAC6B,eAAe,GAAG,CAAC,iBAC9BrD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACN,YAAY;kBAACS,SAAS,EAAC;gBAA6B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,4BAA4B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAAC6B,eAAe;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CACN,EAEAjB,WAAW,CAAC8B,UAAU,GAAG,CAAC,iBACzBtD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACP,OAAO;kBAACU,SAAS,EAAC;gBAA8B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,6BAA6B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAAC8B,UAAU;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CACN,EAEAjB,WAAW,CAAC+B,iBAAiB,GAAG,CAAC,iBAChCvD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACL,MAAM;kBAACQ,SAAS,EAAC;gBAA8B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,6BAA6B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAAC+B,iBAAiB;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CACN,EAEAjB,WAAW,CAACgC,WAAW,GAAG,CAAC,iBAC1BxD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACT,OAAO;kBAACY,SAAS,EAAC;gBAA8B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,6BAA6B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAACgC,WAAW;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CACN,EAEAjB,WAAW,CAACiC,iBAAiB,GAAG,CAAC,iBAChCzD,OAAA;cAAKG,SAAS,EAAC,mCAAmC;cAAA2B,QAAA,gBAChD9B,OAAA;gBAAKG,SAAS,EAAC,mBAAmB;gBAAA2B,QAAA,gBAChC9B,OAAA,CAACJ,OAAO;kBAACO,SAAS,EAAC;gBAA8B;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDzC,OAAA;kBAAMG,SAAS,EAAC,uBAAuB;kBAAA2B,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNzC,OAAA;gBAAMG,SAAS,EAAC,6BAA6B;gBAAA2B,QAAA,GAAC,GAAC,EAACN,WAAW,CAACiC,iBAAiB;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACN,EAGAZ,YAAY,CAACkB,MAAM,GAAG,CAAC,iBACtB/C,OAAA;MAAKG,SAAS,EAAC,MAAM;MAAA2B,QAAA,gBACnB9B,OAAA;QAAIG,SAAS,EAAC,wDAAwD;QAAA2B,QAAA,gBACpE9B,OAAA,CAACJ,OAAO;UAACO,SAAS,EAAC;QAA8B;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEtD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzC,OAAA;QAAKG,SAAS,EAAC,WAAW;QAAA2B,QAAA,EACvBD,YAAY,CAAC6B,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnC5D,OAAA,CAACb,MAAM,CAAC4C,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCzB,OAAO,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAE4B,CAAC,EAAE;UAAE,CAAE;UAC9BnB,UAAU,EAAE;YAAEoB,KAAK,EAAEF,KAAK,GAAG;UAAI,CAAE;UACnCzD,SAAS,EAAC,wGAAwG;UAAA2B,QAAA,gBAElH9B,OAAA,CAACV,QAAQ;YAACa,SAAS,EAAC;UAA8B;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDzC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAKG,SAAS,EAAC,2BAA2B;cAAA2B,QAAA,EAAE6B,WAAW,CAACI;YAAI;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnEzC,OAAA;cAAKG,SAAS,EAAC,uBAAuB;cAAA2B,QAAA,EAAE6B,WAAW,CAACK;YAAW;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA,GAVDmB,KAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrC,EAAA,CA/NIH,eAAe;AAAAgE,EAAA,GAAfhE,eAAe;AAiOrB,eAAeA,eAAe;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}