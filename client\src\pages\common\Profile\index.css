/* Modern Profile Styles */
.Profile {
    /* Remove old styles and use modern Tailwind classes in JSX */

    /* Custom form styling */
    .form-group {
        @apply relative;
    }

    .form-group input:focus {
        @apply outline-none;
    }

    .form-group label {
        @apply transition-all duration-300;
    }

    .form-group input:focus + label,
    .form-group input:not(:placeholder-shown) + label {
        @apply transform -translate-y-2 scale-75 text-blue-600;
    }

    /* Modern button styles */
    .modern-btn {
        @apply inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105;
    }

    .modern-btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl;
    }

    .modern-btn-secondary {
        @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
    }

    .modern-btn-success {
        @apply bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 shadow-lg hover:shadow-xl;
    }

    /* Profile picture enhancements */
    .profile-picture-modern {
        @apply relative overflow-hidden transition-all duration-300 transform hover:scale-105;
    }

    .profile-picture-modern::after {
        content: '';
        @apply absolute inset-0 rounded-full ring-4 ring-blue-100 ring-opacity-50 transition-all duration-300;
    }

    .profile-picture-modern:hover::after {
        @apply ring-blue-300 ring-opacity-75;
    }

    /* Card hover effects */
    .profile-card {
        @apply transform transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;
    }

    /* Stats cards */
    .stat-card {
        @apply bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300;
    }

    .stat-card:hover {
        @apply transform -translate-y-1;
    }

    /* Form animations */
    .form-slide-in {
        @apply animate-fade-in-up;
    }

    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fade-in-up 0.6s ease-out;
    }

    /* Input focus effects */
    .input-modern {
        @apply transition-all duration-300 border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none;
    }

    .input-modern:disabled {
        @apply bg-gray-50 cursor-not-allowed;
    }

    /* Success states */
    .success-message {
        @apply bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg;
    }

    /* Error states */
    .error-message {
        @apply bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg;
    }
}

@media only screen and (max-width: 768px) {
    .Profile .ranking-data {
        font-size: 18px;
    }
}